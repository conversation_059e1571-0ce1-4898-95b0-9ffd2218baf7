# Production Ticket System

A FastAPI-based production ticket management system built with Clean Architecture and Domain-Driven Design (DDD) principles.

## Architecture

- **Domain Layer**: Core business entities and value objects
- **Application Layer**: Use cases and business logic orchestration  
- **Infrastructure Layer**: External services, databases, and repositories
- **Presentation Layer**: API endpoints and request/response handling

## Tech Stack

- **Web Framework**: FastAPI
- **ORM**: SQLAlchemy 2.0 + Alembic
- **Dependency Injection**: dependency-injector
- **Authentication**: JWT with python-jose
- **Task Queue**: Celery + Redis
- **Logging**: structlog
- **Caching**: Redis

## Setup

1. Install dependencies: `pip install -r requirements.txt`
2. Copy `.env.example` to `.env` and configure
3. Run migrations: `alembic upgrade head`
4. Start app: `python main.py`

Visit http://localhost:8000/docs for API documentation.