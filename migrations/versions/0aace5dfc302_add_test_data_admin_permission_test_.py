"""Add test data: ADMIN permission, test factory, role, department, and user

Revision ID: 0aace5dfc302
Revises: 369761ca1cfb
Create Date: 2025-06-19 15:45:36.349442

"""
from alembic import op
import sqlalchemy as sa
from datetime import datetime


# revision identifiers, used by Alembic.
revision = '0aace5dfc302'
down_revision = '369761ca1cfb'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create tables metadata for raw SQL operations
    connection = op.get_bind()
    
    # Insert ADMIN permission
    connection.execute(sa.text("""
        INSERT INTO permissions (code, name) 
        VALUES ('ADMIN', 'Administrator - Full system access')
    """))
    
    # Insert test factory
    connection.execute(sa.text("""
        INSERT INTO factories (name, code, address, phone, email, manager_name, is_active, description, created_at, updated_at)
        VALUES ('Test Factory', 'TEST001', '123 Test Street, Test City', '+1234567890', '<EMAIL>', 'Test Manager', 1, 'Test factory for development and testing', NOW(), NOW())
    """))
    
    # Insert test role with ADMIN permission
    connection.execute(sa.text("""
        INSERT INTO roles (name, description, is_active)
        VALUES ('Test Admin Role', 'Test role with admin permissions for development', 1)
    """))
    
    # Link role to ADMIN permission
    connection.execute(sa.text("""
        INSERT INTO role_permissions (role_id, permission_id, created_at, updated_at)
        SELECT r.id, p.id, NOW(), NOW()
        FROM roles r, permissions p
        WHERE r.name = 'Test Admin Role' AND p.code = 'ADMIN'
    """))
    
    # Insert test department
    connection.execute(sa.text("""
        INSERT INTO departments (name, code, factory_id, manager_name, phone, email, location, is_active, description, created_at, updated_at)
        SELECT 'Test Department', 'TEST_DEPT', f.id, 'Test Dept Manager', '+1234567891', '<EMAIL>', 'Building A', 1, 'Test department for development', NOW(), NOW()
        FROM factories f
        WHERE f.code = 'TEST001'
    """))
    
    # Insert test user with hashed password for 'testpass123'
    # Password hash for 'testpass123' using bcrypt
    connection.execute(sa.text("""
        INSERT INTO users (username, email, hashed_password, full_name, phone, is_active, is_superuser, role_id, created_at, updated_at)
        SELECT 'testuser', '<EMAIL>', '$2b$12$s6/UGzgbbrPpShK7NZdnP.KUmYpYb6j08YSvYaDb2c0m551zOBpZ2', 'Test User', '13800138000', 1, 0, r.id, NOW(), NOW()
        FROM roles r
        WHERE r.name = 'Test Admin Role'
    """))


def downgrade() -> None:
    # Remove test data in reverse order
    connection = op.get_bind()
    
    # Remove test user
    connection.execute(sa.text("DELETE FROM users WHERE username = 'testuser'"))
    
    # Remove test department
    connection.execute(sa.text("DELETE FROM departments WHERE code = 'TEST_DEPT'"))
    
    # Remove role permissions
    connection.execute(sa.text("""
        DELETE FROM role_permissions 
        WHERE role_id IN (SELECT id FROM roles WHERE name = 'Test Admin Role')
    """))
    
    # Remove test role
    connection.execute(sa.text("DELETE FROM roles WHERE name = 'Test Admin Role'"))
    
    # Remove test factory
    connection.execute(sa.text("DELETE FROM factories WHERE code = 'TEST001'"))
    
    # Remove ADMIN permission
    connection.execute(sa.text("DELETE FROM permissions WHERE code = 'ADMIN'"))