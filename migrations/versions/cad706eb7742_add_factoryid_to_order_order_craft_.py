"""Add FactoryId to order order_craft order_craft_routes

Revision ID: cad706eb7742
Revises: 3ad629ec3967
Create Date: 2025-06-23 18:59:24.185451

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'cad706eb7742'
down_revision = '3ad629ec3967'
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.add_column('orders', sa.Column('factory_id', sa.Integer(), nullable=False, comment='Factory ID this order belongs to'))
    op.add_column('order_crafts', sa.Column('factory_id', sa.Integer(), nullable=False, comment='Factory ID this craft belongs to'))
    op.add_column('order_craft_routes', sa.Column('factory_id', sa.Integer(), nullable=False, comment='Factory ID this route belongs to'))

    pass


def downgrade() -> None:
    op.drop_column('order_craft_routes', 'factory_id')
    op.drop_column('order_crafts', 'factory_id')
    op.drop_column('orders', 'factory_id')