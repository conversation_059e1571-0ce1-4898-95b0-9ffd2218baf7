"""add daily worker bills and settlement status

Revision ID: 18a0636a7dfd
Revises: 7c7eec947099
Create Date: 2025-06-24 11:50:09.659851

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '18a0636a7dfd'
down_revision = '7c7eec947099'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create daily_worker_bills table
    op.create_table('daily_worker_bills',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False, comment='ID'),
        sa.Column('factory_id', sa.Integer(), nullable=False, comment='工厂ID'),
        sa.Column('worker_user_id', sa.Integer(), nullable=False, comment='工人ID'),
        sa.Column('bill_date', sa.Date(), nullable=False, comment='账单日期'),
        sa.Column('bill_no', sa.String(length=100), nullable=False, comment='账单号'),
        sa.Column('total_completed_quantity', sa.Integer(), nullable=False, comment='总完成数量'),
        sa.Column('total_work_instances', sa.Integer(), nullable=False, comment='总工作实例数'),
        sa.Column('total_work_duration_minutes', sa.Integer(), nullable=False, comment='总工作时长(分钟)'),
        sa.Column('base_amount', sa.DECIMAL(precision=10, scale=2), nullable=False, comment='基础金额'),
        sa.Column('bonus_amount', sa.DECIMAL(precision=10, scale=2), nullable=False, comment='奖金金额'),
        sa.Column('deduction_amount', sa.DECIMAL(precision=10, scale=2), nullable=False, comment='扣除金额'),
        sa.Column('total_amount', sa.DECIMAL(precision=10, scale=2), nullable=False, comment='总金额'),
        sa.Column('status', sa.Enum('DRAFT', 'PENDING', 'APPROVED', 'PAID', 'REJECTED', 'CANCELLED', name='billstatus'), nullable=False, comment='账单状态'),
        sa.Column('reviewed_by_user_id', sa.Integer(), nullable=True, comment='审核人ID'),
        sa.Column('reviewed_at', sa.DateTime(), nullable=True, comment='审核时间'),
        sa.Column('review_notes', sa.Text(), nullable=True, comment='审核备注'),
        sa.Column('payment_method', sa.Enum('CASH', 'BANK_TRANSFER', 'MOBILE_PAY', 'CHECK', 'OTHER', name='paymentmethod'), nullable=True, comment='支付方式'),
        sa.Column('paid_by_user_id', sa.Integer(), nullable=True, comment='支付人ID'),
        sa.Column('paid_at', sa.DateTime(), nullable=True, comment='支付时间'),
        sa.Column('payment_reference', sa.String(length=200), nullable=True, comment='支付凭证号'),
        sa.Column('payment_notes', sa.Text(), nullable=True, comment='支付备注'),
        sa.Column('quality_score_average', sa.DECIMAL(precision=5, scale=2), nullable=True, comment='平均质量分数'),
        sa.Column('defect_count', sa.Integer(), nullable=False, comment='次品数量'),
        sa.Column('rework_count', sa.Integer(), nullable=False, comment='返工数量'),
        sa.Column('breakdown_by_granularity', sa.JSON(), nullable=True, comment='按粒度分解的统计'),
        sa.Column('breakdown_by_craft_route', sa.JSON(), nullable=True, comment='按工艺路线分解的统计'),
        sa.Column('breakdown_by_order', sa.JSON(), nullable=True, comment='按订单分解的统计'),
        sa.Column('is_auto_generated', sa.Boolean(), nullable=False, comment='是否自动生成'),
        sa.Column('generated_at', sa.DateTime(), nullable=True, comment='生成时间'),
        sa.Column('notes', sa.Text(), nullable=True, comment='备注'),
        sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
        sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新时间'),
        sa.ForeignKeyConstraint(['factory_id'], ['factories.id'], ),
        sa.ForeignKeyConstraint(['paid_by_user_id'], ['users.id'], ),
        sa.ForeignKeyConstraint(['reviewed_by_user_id'], ['users.id'], ),
        sa.ForeignKeyConstraint(['worker_user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('worker_user_id', 'factory_id', 'bill_date', name='uq_worker_factory_date'),
        sa.UniqueConstraint('bill_no', name='uq_daily_worker_bills_bill_no')
    )
    
    # Create indexes for daily_worker_bills
    op.create_index(op.f('ix_daily_worker_bills_bill_date'), 'daily_worker_bills', ['bill_date'], unique=False)
    op.create_index(op.f('ix_daily_worker_bills_bill_no'), 'daily_worker_bills', ['bill_no'], unique=True)
    op.create_index(op.f('ix_daily_worker_bills_factory_id'), 'daily_worker_bills', ['factory_id'], unique=False)
    op.create_index(op.f('ix_daily_worker_bills_status'), 'daily_worker_bills', ['status'], unique=False)
    op.create_index(op.f('ix_daily_worker_bills_worker_user_id'), 'daily_worker_bills', ['worker_user_id'], unique=False)
    
    # Add settlement_status column to order_craft_route_instances
    op.add_column('order_craft_route_instances', sa.Column('settlement_status', sa.Enum('PENDING', 'INCLUDED', 'SETTLED', 'DISPUTED', 'EXCLUDED', name='settlementstatus'), nullable=False, server_default='PENDING', comment='结算状态: pending, included, settled, disputed, excluded'))
    op.create_index(op.f('ix_order_craft_route_instances_settlement_status'), 'order_craft_route_instances', ['settlement_status'], unique=False)


def downgrade() -> None:
    # Remove settlement_status column from order_craft_route_instances
    op.drop_index(op.f('ix_order_craft_route_instances_settlement_status'), table_name='order_craft_route_instances')
    op.drop_column('order_craft_route_instances', 'settlement_status')
    
    # Drop daily_worker_bills table
    op.drop_index(op.f('ix_daily_worker_bills_worker_user_id'), table_name='daily_worker_bills')
    op.drop_index(op.f('ix_daily_worker_bills_status'), table_name='daily_worker_bills')
    op.drop_index(op.f('ix_daily_worker_bills_factory_id'), table_name='daily_worker_bills')
    op.drop_index(op.f('ix_daily_worker_bills_bill_no'), table_name='daily_worker_bills')
    op.drop_index(op.f('ix_daily_worker_bills_bill_date'), table_name='daily_worker_bills')
    op.drop_table('daily_worker_bills')