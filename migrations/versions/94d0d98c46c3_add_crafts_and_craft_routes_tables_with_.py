"""Add crafts and craft_routes tables with proper schema

Revision ID: 94d0d98c46c3
Revises: 4876c2c47007
Create Date: 2025-06-19 21:37:38.411097

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '94d0d98c46c3'
down_revision = '4876c2c47007'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('crafts',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('code', sa.String(length=50), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('priority', sa.Integer(), nullable=False),
    sa.Column('enabled', sa.Boolean(), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_crafts_code'), 'crafts', ['code'], unique=True)
    op.create_index(op.f('ix_crafts_enabled'), 'crafts', ['enabled'], unique=False)
    op.create_index(op.f('ix_crafts_priority'), 'crafts', ['priority'], unique=False)
    op.create_table('craft_routes',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('craft_code', sa.String(length=50), nullable=False),
    sa.Column('skill_code', sa.String(length=50), nullable=False),
    sa.Column('order', sa.Integer(), nullable=False),
    sa.Column('measurement_types', sa.JSON(), nullable=True, comment='Available measurement types (JSON array)'),
    sa.Column('registration_types', sa.JSON(), nullable=True, comment='Available registration types (JSON array)'),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('is_required', sa.Boolean(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['craft_code'], ['crafts.code'], ),
    sa.ForeignKeyConstraint(['skill_code'], ['skills.code'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_craft_routes_craft_code'), 'craft_routes', ['craft_code'], unique=False)
    op.create_index(op.f('ix_craft_routes_order'), 'craft_routes', ['order'], unique=False)
    op.create_index(op.f('ix_craft_routes_skill_code'), 'craft_routes', ['skill_code'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_craft_routes_skill_code'), table_name='craft_routes')
    op.drop_index(op.f('ix_craft_routes_order'), table_name='craft_routes')
    op.drop_index(op.f('ix_craft_routes_craft_code'), table_name='craft_routes')
    op.drop_table('craft_routes')
    op.drop_index(op.f('ix_crafts_priority'), table_name='crafts')
    op.drop_index(op.f('ix_crafts_enabled'), table_name='crafts')
    op.drop_index(op.f('ix_crafts_code'), table_name='crafts')
    op.drop_table('crafts')
    # ### end Alembic commands ###