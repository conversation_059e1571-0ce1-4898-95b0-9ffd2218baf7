"""Add more test permissions for roles and users

Revision ID: 63fe1f2624b4
Revises: 0aace5dfc302
Create Date: 2025-06-19 16:45:36.629095

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '63fe1f2624b4'
down_revision = '0aace5dfc302'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create tables metadata for raw SQL operations
    connection = op.get_bind()
    
    # Insert top-level permission categories
    connection.execute(sa.text("""
        INSERT INTO permissions (code, name, parent_id) VALUES
        ('user_management', '用户管理权限', NULL),
        ('role_management', '角色管理权限', NULL),
        ('permission_management', '权限管理权限', NULL),
        ('factory_management', '工厂管理权限', NULL),
        ('department_management', '部门管理权限', NULL)
    """))
    
    # Insert user-related permissions under user_management
    connection.execute(sa.text("""
        INSERT INTO permissions (code, name, parent_id) 
        SELECT 'users.view', '查看用户', p.id FROM permissions p WHERE p.code = 'user_management'
        UNION ALL
        SELECT 'users.create', '创建用户', p.id FROM permissions p WHERE p.code = 'user_management'
        UNION ALL
        SELECT 'users.update', '修改用户', p.id FROM permissions p WHERE p.code = 'user_management'
        UNION ALL
        SELECT 'users.delete', '删除用户', p.id FROM permissions p WHERE p.code = 'user_management'
        UNION ALL
        SELECT 'users.manage_roles', '管理用户角色', p.id FROM permissions p WHERE p.code = 'user_management'
        UNION ALL
        SELECT 'users.reset_password', '重置用户密码', p.id FROM permissions p WHERE p.code = 'user_management'
        UNION ALL
        SELECT 'users.suspend', '暂停用户', p.id FROM permissions p WHERE p.code = 'user_management'
        UNION ALL
        SELECT 'users.activate', '激活用户', p.id FROM permissions p WHERE p.code = 'user_management'
    """))
    
    # Insert role-related permissions under role_management
    connection.execute(sa.text("""
        INSERT INTO permissions (code, name, parent_id) 
        SELECT 'roles.view', '查看角色', p.id FROM permissions p WHERE p.code = 'role_management'
        UNION ALL
        SELECT 'roles.create', '创建角色', p.id FROM permissions p WHERE p.code = 'role_management'
        UNION ALL
        SELECT 'roles.update', '修改角色', p.id FROM permissions p WHERE p.code = 'role_management'
        UNION ALL
        SELECT 'roles.delete', '删除角色', p.id FROM permissions p WHERE p.code = 'role_management'
        UNION ALL
        SELECT 'roles.manage_permissions', '修改角色权限', p.id FROM permissions p WHERE p.code = 'role_management'
    """))
    
    # Insert permission-related permissions under permission_management
    connection.execute(sa.text("""
        INSERT INTO permissions (code, name, parent_id) 
        SELECT 'permissions.view', '查看权限', p.id FROM permissions p WHERE p.code = 'permission_management'
        UNION ALL
        SELECT 'permissions.manage', '管理权限', p.id FROM permissions p WHERE p.code = 'permission_management'
    """))
    
    # Insert factory-related permissions under factory_management
    connection.execute(sa.text("""
        INSERT INTO permissions (code, name, parent_id) 
        SELECT 'factories.view', '查看工厂', p.id FROM permissions p WHERE p.code = 'factory_management'
        UNION ALL
        SELECT 'factories.create', '创建工厂', p.id FROM permissions p WHERE p.code = 'factory_management'
        UNION ALL
        SELECT 'factories.update', '修改工厂', p.id FROM permissions p WHERE p.code = 'factory_management'
        UNION ALL
        SELECT 'factories.delete', '删除工厂', p.id FROM permissions p WHERE p.code = 'factory_management'
        UNION ALL
        SELECT 'factories.manage_users', '管理工厂用户', p.id FROM permissions p WHERE p.code = 'factory_management'
    """))
    
    # Insert department-related permissions under department_management
    connection.execute(sa.text("""
        INSERT INTO permissions (code, name, parent_id) 
        SELECT 'departments.view', '查看部门', p.id FROM permissions p WHERE p.code = 'department_management'
        UNION ALL
        SELECT 'departments.create', '创建部门', p.id FROM permissions p WHERE p.code = 'department_management'
        UNION ALL
        SELECT 'departments.update', '修改部门', p.id FROM permissions p WHERE p.code = 'department_management'
        UNION ALL
        SELECT 'departments.delete', '删除部门', p.id FROM permissions p WHERE p.code = 'department_management'
    """))
    
    # Link all new permissions to the Test Admin Role
    connection.execute(sa.text("""
        INSERT INTO role_permissions (role_id, permission_id, created_at, updated_at)
        SELECT r.id, p.id, NOW(), NOW()
        FROM roles r, permissions p
        WHERE r.name = 'Test Admin Role' 
        AND p.code IN (
            'user_management', 'role_management', 'permission_management', 'factory_management', 'department_management',
            'roles.view', 'roles.create', 'roles.update', 'roles.delete', 'roles.manage_permissions',
            'users.view', 'users.create', 'users.update', 'users.delete', 'users.manage_roles', 
            'users.reset_password', 'users.suspend', 'users.activate',
            'permissions.view', 'permissions.manage',
            'factories.view', 'factories.create', 'factories.update', 'factories.delete', 'factories.manage_users',
            'departments.view', 'departments.create', 'departments.update', 'departments.delete'
        )
    """))


def downgrade() -> None:
    # Remove new permissions
    connection = op.get_bind()
    
    # Remove role permissions first
    connection.execute(sa.text("""
        DELETE FROM role_permissions 
        WHERE permission_id IN (
            SELECT id FROM permissions WHERE code IN (
                'user_management', 'role_management', 'permission_management', 'factory_management', 'department_management',
                'roles.view', 'roles.create', 'roles.update', 'roles.delete', 'roles.manage_permissions',
                'users.view', 'users.create', 'users.update', 'users.delete', 'users.manage_roles', 
                'users.reset_password', 'users.suspend', 'users.activate',
                'permissions.view', 'permissions.manage',
                'factories.view', 'factories.create', 'factories.update', 'factories.delete', 'factories.manage_users',
                'departments.view', 'departments.create', 'departments.update', 'departments.delete'
            )
        )
    """))
    
    # Remove child permissions first (due to foreign key constraint)
    connection.execute(sa.text("""
        DELETE FROM permissions WHERE code IN (
            'roles.view', 'roles.create', 'roles.update', 'roles.delete', 'roles.manage_permissions',
            'users.view', 'users.create', 'users.update', 'users.delete', 'users.manage_roles', 
            'users.reset_password', 'users.suspend', 'users.activate',
            'permissions.view', 'permissions.manage',
            'factories.view', 'factories.create', 'factories.update', 'factories.delete', 'factories.manage_users',
            'departments.view', 'departments.create', 'departments.update', 'departments.delete'
        )
    """))
    
    # Remove parent permissions
    connection.execute(sa.text("""
        DELETE FROM permissions WHERE code IN (
            'user_management', 'role_management', 'permission_management', 'factory_management', 'department_management'
        )
    """))