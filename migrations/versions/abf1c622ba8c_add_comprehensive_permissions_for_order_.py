"""add comprehensive permissions for order parts and order bundles

Revision ID: abf1c622ba8c
Revises: f06aa360e6ed
Create Date: 2025-06-23 23:54:06.089141

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'abf1c622ba8c'
down_revision = 'f06aa360e6ed'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create tables metadata for raw SQL operations
    connection = op.get_bind()
    
    # Insert top-level permission categories for order parts and order bundles
    connection.execute(sa.text("""
        INSERT IGNORE INTO permissions (code, name, parent_id) VALUES
        ('order_parts_management', '订单部位管理权限', NULL),
        ('order_bundles_management', '订单扎管理权限', NULL)
    """))
    
    # Insert order parts-related permissions under order_parts_management
    connection.execute(sa.text("""
        INSERT IGNORE INTO permissions (code, name, parent_id) 
        SELECT 'order_parts.view', '查看订单部位', p.id FROM permissions p WHERE p.code = 'order_parts_management'
        UNION ALL
        SELECT 'order_parts.create', '创建订单部位', p.id FROM permissions p WHERE p.code = 'order_parts_management'
        UNION ALL
        SELECT 'order_parts.update', '修改订单部位', p.id FROM permissions p WHERE p.code = 'order_parts_management'
        UNION ALL
        SELECT 'order_parts.delete', '删除订单部位', p.id FROM permissions p WHERE p.code = 'order_parts_management'
        UNION ALL
        SELECT 'order_parts.manage', '管理订单部位', p.id FROM permissions p WHERE p.code = 'order_parts_management'
        UNION ALL
        SELECT 'order_parts.view_all', '查看所有订单部位', p.id FROM permissions p WHERE p.code = 'order_parts_management'
        UNION ALL
        SELECT 'order_parts.manage_status', '管理订单部位状态', p.id FROM permissions p WHERE p.code = 'order_parts_management'
        UNION ALL
        SELECT 'order_parts.track_progress', '跟踪部位进度', p.id FROM permissions p WHERE p.code = 'order_parts_management'
        UNION ALL
        SELECT 'order_parts.assign_supervisor', '分配负责人', p.id FROM permissions p WHERE p.code = 'order_parts_management'
        UNION ALL
        SELECT 'order_parts.manage_machine', '管理机床分配', p.id FROM permissions p WHERE p.code = 'order_parts_management'
        UNION ALL
        SELECT 'order_parts.manage_schedule', '管理生产计划', p.id FROM permissions p WHERE p.code = 'order_parts_management'
    """))
    
    # Insert order bundles-related permissions under order_bundles_management
    connection.execute(sa.text("""
        INSERT IGNORE INTO permissions (code, name, parent_id) 
        SELECT 'order_bundles.view', '查看订单扎', p.id FROM permissions p WHERE p.code = 'order_bundles_management'
        UNION ALL
        SELECT 'order_bundles.create', '创建订单扎', p.id FROM permissions p WHERE p.code = 'order_bundles_management'
        UNION ALL
        SELECT 'order_bundles.update', '修改订单扎', p.id FROM permissions p WHERE p.code = 'order_bundles_management'
        UNION ALL
        SELECT 'order_bundles.delete', '删除订单扎', p.id FROM permissions p WHERE p.code = 'order_bundles_management'
        UNION ALL
        SELECT 'order_bundles.manage', '管理订单扎', p.id FROM permissions p WHERE p.code = 'order_bundles_management'
        UNION ALL
        SELECT 'order_bundles.view_all', '查看所有订单扎', p.id FROM permissions p WHERE p.code = 'order_bundles_management'
        UNION ALL
        SELECT 'order_bundles.manage_status', '管理订单扎状态', p.id FROM permissions p WHERE p.code = 'order_bundles_management'
        UNION ALL
        SELECT 'order_bundles.track_progress', '跟踪扎进度', p.id FROM permissions p WHERE p.code = 'order_bundles_management'
        UNION ALL
        SELECT 'order_bundles.assign_workers', '分配工人', p.id FROM permissions p WHERE p.code = 'order_bundles_management'
        UNION ALL
        SELECT 'order_bundles.manage_production', '管理扎生产', p.id FROM permissions p WHERE p.code = 'order_bundles_management'
        UNION ALL
        SELECT 'order_bundles.quality_control', '质量控制', p.id FROM permissions p WHERE p.code = 'order_bundles_management'
        UNION ALL
        SELECT 'order_bundles.manage_cutting', '管理裁剪', p.id FROM permissions p WHERE p.code = 'order_bundles_management'
        UNION ALL
        SELECT 'order_bundles.manage_sewing', '管理缝制', p.id FROM permissions p WHERE p.code = 'order_bundles_management'
        UNION ALL
        SELECT 'order_bundles.manage_machines', '管理机床分配', p.id FROM permissions p WHERE p.code = 'order_bundles_management'
        UNION ALL
        SELECT 'order_bundles.manage_schedule', '管理生产计划', p.id FROM permissions p WHERE p.code = 'order_bundles_management'
        UNION ALL
        SELECT 'order_bundles.bulk_operations', '批量操作', p.id FROM permissions p WHERE p.code = 'order_bundles_management'
    """))
    
    # Insert additional workflow permissions for order parts
    connection.execute(sa.text("""
        INSERT IGNORE INTO permissions (code, name, parent_id) 
        SELECT 'order_parts.start_cutting', '开始裁剪', p.id FROM permissions p WHERE p.code = 'order_parts_management'
        UNION ALL
        SELECT 'order_parts.complete_cutting', '完成裁剪', p.id FROM permissions p WHERE p.code = 'order_parts_management'
        UNION ALL
        SELECT 'order_parts.start_sewing', '开始缝制', p.id FROM permissions p WHERE p.code = 'order_parts_management'
        UNION ALL
        SELECT 'order_parts.complete_sewing', '完成缝制', p.id FROM permissions p WHERE p.code = 'order_parts_management'
        UNION ALL
        SELECT 'order_parts.quality_check', '质量检查', p.id FROM permissions p WHERE p.code = 'order_parts_management'
        UNION ALL
        SELECT 'order_parts.complete_part', '完成部位', p.id FROM permissions p WHERE p.code = 'order_parts_management'
    """))
    
    # Insert additional workflow permissions for order bundles
    connection.execute(sa.text("""
        INSERT IGNORE INTO permissions (code, name, parent_id) 
        SELECT 'order_bundles.start_cutting', '开始裁剪', p.id FROM permissions p WHERE p.code = 'order_bundles_management'
        UNION ALL
        SELECT 'order_bundles.complete_cutting', '完成裁剪', p.id FROM permissions p WHERE p.code = 'order_bundles_management'
        UNION ALL
        SELECT 'order_bundles.start_sewing', '开始缝制', p.id FROM permissions p WHERE p.code = 'order_bundles_management'
        UNION ALL
        SELECT 'order_bundles.complete_sewing', '完成缝制', p.id FROM permissions p WHERE p.code = 'order_bundles_management'
        UNION ALL
        SELECT 'order_bundles.start_qc', '开始质检', p.id FROM permissions p WHERE p.code = 'order_bundles_management'
        UNION ALL
        SELECT 'order_bundles.complete_qc', '完成质检', p.id FROM permissions p WHERE p.code = 'order_bundles_management'
        UNION ALL
        SELECT 'order_bundles.mark_defective', '标记次品', p.id FROM permissions p WHERE p.code = 'order_bundles_management'
        UNION ALL
        SELECT 'order_bundles.mark_rework', '标记返工', p.id FROM permissions p WHERE p.code = 'order_bundles_management'
        UNION ALL
        SELECT 'order_bundles.complete_bundle', '完成扎', p.id FROM permissions p WHERE p.code = 'order_bundles_management'
    """))
    
    # Insert statistics permissions for order parts and bundles
    connection.execute(sa.text("""
        INSERT IGNORE INTO permissions (code, name, parent_id) 
        SELECT 'order_parts.statistics', '查看部位统计', p.id FROM permissions p WHERE p.code = 'order_parts_management'
        UNION ALL
        SELECT 'order_parts.export', '导出部位数据', p.id FROM permissions p WHERE p.code = 'order_parts_management'
        UNION ALL
        SELECT 'order_bundles.statistics', '查看扎统计', p.id FROM permissions p WHERE p.code = 'order_bundles_management'
        UNION ALL
        SELECT 'order_bundles.export', '导出扎数据', p.id FROM permissions p WHERE p.code = 'order_bundles_management'
    """))


def downgrade() -> None:
    # Remove new permissions
    connection = op.get_bind()
    
    # Remove child permissions first (due to foreign key constraint)
    connection.execute(sa.text("""
        DELETE FROM permissions WHERE code IN (
            -- Order Parts permissions
            'order_parts.view', 'order_parts.create', 'order_parts.update', 'order_parts.delete', 
            'order_parts.manage', 'order_parts.view_all', 'order_parts.manage_status', 'order_parts.track_progress',
            'order_parts.assign_supervisor', 'order_parts.manage_machine', 'order_parts.manage_schedule',
            'order_parts.start_cutting', 'order_parts.complete_cutting', 'order_parts.start_sewing', 
            'order_parts.complete_sewing', 'order_parts.quality_check', 'order_parts.complete_part',
            'order_parts.statistics', 'order_parts.export',
            
            -- Order Bundles permissions
            'order_bundles.view', 'order_bundles.create', 'order_bundles.update', 'order_bundles.delete',
            'order_bundles.manage', 'order_bundles.view_all', 'order_bundles.manage_status', 'order_bundles.track_progress',
            'order_bundles.assign_workers', 'order_bundles.manage_production', 'order_bundles.quality_control',
            'order_bundles.manage_cutting', 'order_bundles.manage_sewing', 'order_bundles.manage_machines',
            'order_bundles.manage_schedule', 'order_bundles.bulk_operations',
            'order_bundles.start_cutting', 'order_bundles.complete_cutting', 'order_bundles.start_sewing',
            'order_bundles.complete_sewing', 'order_bundles.start_qc', 'order_bundles.complete_qc',
            'order_bundles.mark_defective', 'order_bundles.mark_rework', 'order_bundles.complete_bundle',
            'order_bundles.statistics', 'order_bundles.export'
        )
    """))
    
    # Remove parent permissions
    connection.execute(sa.text("""
        DELETE FROM permissions WHERE code IN (
            'order_parts_management', 'order_bundles_management'
        )
    """))