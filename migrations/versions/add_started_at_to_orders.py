"""Add started_at field to orders table

Revision ID: add_started_at_to_orders  
Revises: 18a0636a7dfd
Create Date: 2025-06-24 19:05:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'add_started_at_to_orders'
down_revision = '18a0636a7dfd'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Add started_at column to orders table
    op.add_column('orders', sa.Column('started_at', sa.DateTime(), nullable=True, comment='订单开始时间'))


def downgrade() -> None:
    # Remove started_at column from orders table
    op.drop_column('orders', 'started_at')