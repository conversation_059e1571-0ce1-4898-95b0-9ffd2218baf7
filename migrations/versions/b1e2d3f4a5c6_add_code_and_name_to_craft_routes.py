"""Add code and name fields to craft_routes table with unique constraint

Revision ID: b1e2d3f4a5c6
Revises: fa9a4723ef3c
Create Date: 2025-06-20 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'b1e2d3f4a5c6'
down_revision = 'e88016ba7aef'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    connection = op.get_bind()
    
    # Check if columns already exist
    result = connection.execute(sa.text("""
        SELECT COUNT(*) 
        FROM information_schema.columns 
        WHERE table_name='craft_routes' AND column_name='code'
    """))
    
    code_exists = result.scalar() > 0
    
    if not code_exists:
        # Add new columns to craft_routes table
        op.add_column('craft_routes', sa.Column('code', sa.String(length=50), nullable=True, comment='Unique code within craft'))
        op.add_column('craft_routes', sa.Column('name', sa.String(length=100), nullable=True, comment='Display name for this route'))
        
        # Create index on code column
        op.create_index(op.f('ix_craft_routes_code'), 'craft_routes', ['code'], unique=False)
        
        # Update existing records with default values
        connection.execute(sa.text("""
            UPDATE craft_routes 
            SET code = CONCAT(skill_code, '_', `order`),
                name = CONCAT('Route ', `order`, ' - ', skill_code)
            WHERE code IS NULL OR name IS NULL
        """))
        
        # Now make the columns NOT NULL (MySQL requires existing type specification)
        op.alter_column('craft_routes', 'code', 
                       existing_type=sa.String(length=50),
                       nullable=False)
        op.alter_column('craft_routes', 'name', 
                       existing_type=sa.String(length=100),
                       nullable=False)
        
        # Create unique constraint on craft_code + code
        op.create_unique_constraint('uq_craft_route_craft_code_code', 'craft_routes', ['craft_code', 'code'])
    else:
        print("Columns 'code' and 'name' already exist in craft_routes table, skipping...")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # Drop unique constraint
    op.drop_constraint('uq_craft_route_craft_code_code', 'craft_routes', type_='unique')
    
    # Drop index
    op.drop_index(op.f('ix_craft_routes_code'), table_name='craft_routes')
    
    # Drop columns
    op.drop_column('craft_routes', 'name')
    op.drop_column('craft_routes', 'code')
    # ### end Alembic commands ###