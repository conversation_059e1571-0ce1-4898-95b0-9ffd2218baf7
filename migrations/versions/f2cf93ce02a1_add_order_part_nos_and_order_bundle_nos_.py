"""Add order_part_nos and order_bundle_nos columns to order_craft_route_instances

Revision ID: f2cf93ce02a1
Revises: add_started_at_to_orders
Create Date: 2025-06-25 16:19:08.381065

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'f2cf93ce02a1'
down_revision = 'add_started_at_to_orders'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Add missing JSON columns for multiple part/bundle numbers
    op.add_column('order_craft_route_instances', 
                  sa.Column('order_part_nos', sa.JSON(), nullable=True, 
                           comment='List of order part numbers for bed/bundle level'))
    op.add_column('order_craft_route_instances', 
                  sa.Column('order_bundle_nos', sa.JSON(), nullable=True, 
                           comment='List of order bundle numbers for bundle level'))


def downgrade() -> None:
    # Remove the added columns
    op.drop_column('order_craft_route_instances', 'order_bundle_nos')
    op.drop_column('order_craft_route_instances', 'order_part_nos')