"""Initial migration

Revision ID: 369761ca1cfb
Revises: 
Create Date: 2025-06-19 14:26:02.924482

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '369761ca1cfb'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('factories',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('code', sa.String(length=20), nullable=False),
    sa.Column('address', sa.Text(), nullable=True),
    sa.Column('phone', sa.String(length=20), nullable=True),
    sa.Column('email', sa.String(length=100), nullable=True),
    sa.Column('manager_name', sa.String(length=100), nullable=True),
    sa.Column('is_active', sa.<PERSON>(), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_factories_code'), 'factories', ['code'], unique=True)
    op.create_index(op.f('ix_factories_name'), 'factories', ['name'], unique=True)
    op.create_table('permissions',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('code', sa.String(length=100), nullable=False),
    sa.Column('name', sa.String(length=200), nullable=False),
    sa.Column('parent_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['parent_id'], ['permissions.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_permissions_code'), 'permissions', ['code'], unique=True)
    op.create_index(op.f('ix_permissions_id'), 'permissions', ['id'], unique=False)
    op.create_table('roles',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_roles_id'), 'roles', ['id'], unique=False)
    op.create_index(op.f('ix_roles_name'), 'roles', ['name'], unique=True)
    op.create_table('skills',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('code', sa.String(length=50), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('category', sa.String(length=50), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_skills_code'), 'skills', ['code'], unique=True)
    op.create_index(op.f('ix_skills_id'), 'skills', ['id'], unique=False)
    op.create_table('validation_codes',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('code', sa.String(length=10), nullable=False),
    sa.Column('code_type', sa.Enum('SMS', 'IMAGE', name='validationcodetype'), nullable=False),
    sa.Column('identifier', sa.String(length=100), nullable=False),
    sa.Column('expires_at', sa.DateTime(), nullable=False),
    sa.Column('is_used', sa.Boolean(), nullable=False),
    sa.Column('used_at', sa.DateTime(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_validation_codes_code'), 'validation_codes', ['code'], unique=False)
    op.create_index(op.f('ix_validation_codes_code_type'), 'validation_codes', ['code_type'], unique=False)
    op.create_index(op.f('ix_validation_codes_expires_at'), 'validation_codes', ['expires_at'], unique=False)
    op.create_index(op.f('ix_validation_codes_identifier'), 'validation_codes', ['identifier'], unique=False)
    op.create_table('departments',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('code', sa.String(length=20), nullable=False),
    sa.Column('factory_id', sa.Integer(), nullable=False),
    sa.Column('manager_name', sa.String(length=100), nullable=True),
    sa.Column('phone', sa.String(length=20), nullable=True),
    sa.Column('email', sa.String(length=100), nullable=True),
    sa.Column('location', sa.String(length=200), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['factory_id'], ['factories.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_departments_code'), 'departments', ['code'], unique=False)
    op.create_index(op.f('ix_departments_factory_id'), 'departments', ['factory_id'], unique=False)
    op.create_index(op.f('ix_departments_name'), 'departments', ['name'], unique=False)
    op.create_table('role_permissions',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('role_id', sa.Integer(), nullable=False),
    sa.Column('permission_id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['permission_id'], ['permissions.id'], ),
    sa.ForeignKeyConstraint(['role_id'], ['roles.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_role_permissions_id'), 'role_permissions', ['id'], unique=False)
    op.create_table('users',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('username', sa.String(length=50), nullable=False),
    sa.Column('email', sa.String(length=100), nullable=False),
    sa.Column('hashed_password', sa.String(length=255), nullable=False),
    sa.Column('full_name', sa.String(length=100), nullable=True),
    sa.Column('phone', sa.String(length=20), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('is_superuser', sa.Boolean(), nullable=False),
    sa.Column('avatar_url', sa.Text(), nullable=True),
    sa.Column('role_id', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['role_id'], ['roles.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)
    op.create_index(op.f('ix_users_phone'), 'users', ['phone'], unique=True)
    op.create_index(op.f('ix_users_username'), 'users', ['username'], unique=True)
    op.create_table('user_factories',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('factory_id', sa.Integer(), nullable=False),
    sa.Column('department_id', sa.Integer(), nullable=True),
    sa.Column('status', sa.Enum('PENDING', 'APPROVED', 'REJECTED', 'SUSPENDED', 'RESIGNED', name='userfactorystatus'), nullable=False),
    sa.Column('role', sa.Enum('WORKER', 'SUPERVISOR', 'MANAGER', 'ADMIN', name='userfactoryrole'), nullable=False),
    sa.Column('employee_id', sa.String(length=50), nullable=True),
    sa.Column('position', sa.String(length=100), nullable=True),
    sa.Column('start_date', sa.DateTime(), nullable=True),
    sa.Column('end_date', sa.DateTime(), nullable=True),
    sa.Column('requested_at', sa.DateTime(), nullable=False),
    sa.Column('approved_by', sa.Integer(), nullable=True),
    sa.Column('approved_at', sa.DateTime(), nullable=True),
    sa.Column('rejected_reason', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['approved_by'], ['users.id'], ),
    sa.ForeignKeyConstraint(['department_id'], ['departments.id'], ),
    sa.ForeignKeyConstraint(['factory_id'], ['factories.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_factories_department_id'), 'user_factories', ['department_id'], unique=False)
    op.create_index(op.f('ix_user_factories_employee_id'), 'user_factories', ['employee_id'], unique=False)
    op.create_index(op.f('ix_user_factories_factory_id'), 'user_factories', ['factory_id'], unique=False)
    op.create_index(op.f('ix_user_factories_status'), 'user_factories', ['status'], unique=False)
    op.create_index(op.f('ix_user_factories_user_id'), 'user_factories', ['user_id'], unique=False)
    op.create_table('user_factory_skills',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_factory_id', sa.Integer(), nullable=False),
    sa.Column('skill_id', sa.Integer(), nullable=False),
    sa.Column('proficiency_level', sa.String(length=20), nullable=False),
    sa.Column('certified', sa.Boolean(), nullable=False),
    sa.Column('certification_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('certification_expires', sa.DateTime(timezone=True), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('assigned_by', sa.Integer(), nullable=True),
    sa.Column('assigned_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['assigned_by'], ['users.id'], ),
    sa.ForeignKeyConstraint(['skill_id'], ['skills.id'], ),
    sa.ForeignKeyConstraint(['user_factory_id'], ['user_factories.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_factory_skills_id'), 'user_factory_skills', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_user_factory_skills_id'), table_name='user_factory_skills')
    op.drop_table('user_factory_skills')
    op.drop_index(op.f('ix_user_factories_user_id'), table_name='user_factories')
    op.drop_index(op.f('ix_user_factories_status'), table_name='user_factories')
    op.drop_index(op.f('ix_user_factories_factory_id'), table_name='user_factories')
    op.drop_index(op.f('ix_user_factories_employee_id'), table_name='user_factories')
    op.drop_index(op.f('ix_user_factories_department_id'), table_name='user_factories')
    op.drop_table('user_factories')
    op.drop_index(op.f('ix_users_username'), table_name='users')
    op.drop_index(op.f('ix_users_phone'), table_name='users')
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.drop_table('users')
    op.drop_index(op.f('ix_role_permissions_id'), table_name='role_permissions')
    op.drop_table('role_permissions')
    op.drop_index(op.f('ix_departments_name'), table_name='departments')
    op.drop_index(op.f('ix_departments_factory_id'), table_name='departments')
    op.drop_index(op.f('ix_departments_code'), table_name='departments')
    op.drop_table('departments')
    op.drop_index(op.f('ix_validation_codes_identifier'), table_name='validation_codes')
    op.drop_index(op.f('ix_validation_codes_expires_at'), table_name='validation_codes')
    op.drop_index(op.f('ix_validation_codes_code_type'), table_name='validation_codes')
    op.drop_index(op.f('ix_validation_codes_code'), table_name='validation_codes')
    op.drop_table('validation_codes')
    op.drop_index(op.f('ix_skills_id'), table_name='skills')
    op.drop_index(op.f('ix_skills_code'), table_name='skills')
    op.drop_table('skills')
    op.drop_index(op.f('ix_roles_name'), table_name='roles')
    op.drop_index(op.f('ix_roles_id'), table_name='roles')
    op.drop_table('roles')
    op.drop_index(op.f('ix_permissions_id'), table_name='permissions')
    op.drop_index(op.f('ix_permissions_code'), table_name='permissions')
    op.drop_table('permissions')
    op.drop_index(op.f('ix_factories_name'), table_name='factories')
    op.drop_index(op.f('ix_factories_code'), table_name='factories')
    op.drop_table('factories')
    # ### end Alembic commands ###