"""Rename style_no fields to skc_no across all tables

Revision ID: 38ccfdef7872
Revises: 3ed3c05729aa
Create Date: 2025-06-20 11:13:01.931081

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '38ccfdef7872'
down_revision = '3ed3c05729aa'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('bundles', sa.Column('skc_no', sa.String(length=100), nullable=False, comment='款号'))
    op.drop_column('bundles', 'style_no')
    op.add_column('orders', sa.Column('skc_no', sa.String(length=100), nullable=False, comment='款号'))
    op.add_column('orders', sa.Column('external_skc_no', sa.String(length=100), nullable=True, comment='外部款号'))
    op.drop_index('ix_orders_external_style_no', table_name='orders')
    op.drop_index('ix_orders_style_no', table_name='orders')
    op.create_index(op.f('ix_orders_external_skc_no'), 'orders', ['external_skc_no'], unique=False)
    op.create_index(op.f('ix_orders_skc_no'), 'orders', ['skc_no'], unique=False)
    op.drop_column('orders', 'external_style_no')
    op.drop_column('orders', 'style_no')
    op.add_column('parts', sa.Column('skc_no', sa.String(length=100), nullable=False, comment='款号'))
    op.drop_column('parts', 'style_no')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('parts', sa.Column('style_no', mysql.VARCHAR(length=100), nullable=False, comment='款号'))
    op.drop_column('parts', 'skc_no')
    op.add_column('orders', sa.Column('style_no', mysql.VARCHAR(length=100), nullable=False, comment='款号'))
    op.add_column('orders', sa.Column('external_style_no', mysql.VARCHAR(length=100), nullable=True, comment='外部款号'))
    op.drop_index(op.f('ix_orders_skc_no'), table_name='orders')
    op.drop_index(op.f('ix_orders_external_skc_no'), table_name='orders')
    op.create_index('ix_orders_style_no', 'orders', ['style_no'], unique=False)
    op.create_index('ix_orders_external_style_no', 'orders', ['external_style_no'], unique=False)
    op.drop_column('orders', 'external_skc_no')
    op.drop_column('orders', 'skc_no')
    op.add_column('bundles', sa.Column('style_no', mysql.VARCHAR(length=100), nullable=False, comment='款号'))
    op.drop_column('bundles', 'skc_no')
    # ### end Alembic commands ###