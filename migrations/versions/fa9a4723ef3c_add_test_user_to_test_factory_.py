"""Add test user to test factory relationship

Revision ID: fa9a4723ef3c
Revises: 63fe1f2624b4
Create Date: 2025-06-19 18:50:42.811593

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'fa9a4723ef3c'
down_revision = '63fe1f2624b4'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create tables metadata for raw SQL operations
    connection = op.get_bind()
    
    # Add test user to test factory as manager
    connection.execute(sa.text("""
        INSERT INTO user_factories (user_id, factory_id, department_id, status, role, employee_id, position, start_date, requested_at, approved_by, approved_at, created_at, updated_at)
        SELECT u.id, f.id, d.id, 'APPROVED', 'MANAGER', 'TEST_MGR_001', 'Test Manager', NOW(), NOW(), u.id, NOW(), NOW(), NOW()
        FROM users u, factories f, departments d
        WHERE u.username = 'testuser' 
        AND f.code = 'TEST001'
        AND d.code = 'TEST_DEPT'
    """))


def downgrade() -> None:
    # Remove test user factory relationship
    connection = op.get_bind()
    
    connection.execute(sa.text("""
        DELETE FROM user_factories 
        WHERE user_id IN (SELECT id FROM users WHERE username = 'testuser')
        AND factory_id IN (SELECT id FROM factories WHERE code = 'TEST001')
    """))