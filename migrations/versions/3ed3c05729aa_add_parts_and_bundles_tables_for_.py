"""add parts and bundles tables for garment production - actual

Revision ID: 3ed3c05729aa
Revises: 2b22b9a1c0ff
Create Date: 2025-06-19 23:34:26.663143

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '3ed3c05729aa'
down_revision = '2b22b9a1c0ff'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('parts',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('part_no', sa.String(length=100), nullable=False, comment='部位号'),
    sa.Column('order_no', sa.String(length=100), nullable=False, comment='订单号'),
    sa.Column('style_no', sa.String(length=100), nullable=False, comment='款号'),
    sa.Column('color', sa.String(length=50), nullable=False, comment='颜色'),
    sa.Column('part_type', sa.Enum('FRONT_BODY', 'BACK_BODY', 'SLEEVE', 'COLLAR', 'POCKET', 'CUFF', 'WAISTBAND', 'LEG', 'ZIPPER', 'BUTTON_PLACKET', 'LINING', 'ACCESSORIES', 'OTHER', name='parttype'), nullable=False, comment='部位类型'),
    sa.Column('part_name', sa.String(length=100), nullable=False, comment='部位名称'),
    sa.Column('part_sequence', sa.Integer(), nullable=False, comment='部位序号(同一订单内)'),
    sa.Column('total_quantity', sa.Integer(), nullable=False, comment='部位总件数'),
    sa.Column('completed_quantity', sa.Integer(), nullable=False, comment='已完成件数'),
    sa.Column('status', sa.Enum('PLANNED', 'CUTTING', 'SEWING', 'QUALITY_CHECK', 'COMPLETED', 'ON_HOLD', 'CANCELLED', name='partstatus'), nullable=False, comment='部位状态'),
    sa.Column('progress_percentage', sa.Integer(), nullable=False, comment='完成百分比'),
    sa.Column('planned_start_date', sa.DateTime(), nullable=True, comment='计划开始时间'),
    sa.Column('actual_start_date', sa.DateTime(), nullable=True, comment='实际开始时间'),
    sa.Column('planned_end_date', sa.DateTime(), nullable=True, comment='计划完成时间'),
    sa.Column('actual_end_date', sa.DateTime(), nullable=True, comment='实际完成时间'),
    sa.Column('machine_no', sa.String(length=50), nullable=True, comment='机床编号'),
    sa.Column('process_route', sa.String(length=200), nullable=True, comment='工序路线'),
    sa.Column('description', sa.Text(), nullable=True, comment='部位描述'),
    sa.Column('notes', sa.Text(), nullable=True, comment='备注'),
    sa.Column('supervisor_user_id', sa.Integer(), nullable=True, comment='负责人用户ID'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新时间'),
    sa.ForeignKeyConstraint(['order_no'], ['orders.order_no'], ),
    sa.ForeignKeyConstraint(['supervisor_user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_parts_part_no'), 'parts', ['part_no'], unique=True)
    op.create_table('bundles',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('bundle_no', sa.String(length=100), nullable=False, comment='扎号'),
    sa.Column('part_no', sa.String(length=100), nullable=False, comment='部位号'),
    sa.Column('order_no', sa.String(length=100), nullable=False, comment='订单号'),
    sa.Column('style_no', sa.String(length=100), nullable=False, comment='款号'),
    sa.Column('color', sa.String(length=50), nullable=False, comment='颜色'),
    sa.Column('size', sa.String(length=20), nullable=False, comment='尺码'),
    sa.Column('bundle_sequence', sa.Integer(), nullable=False, comment='扎序号(同一部位、同一尺码内)'),
    sa.Column('quantity', sa.Integer(), nullable=False, comment='扎件数'),
    sa.Column('completed_quantity', sa.Integer(), nullable=False, comment='已完成件数'),
    sa.Column('defective_quantity', sa.Integer(), nullable=False, comment='次品件数'),
    sa.Column('rework_quantity', sa.Integer(), nullable=False, comment='返工件数'),
    sa.Column('status', sa.Enum('PLANNED', 'CUTTING', 'CUT_COMPLETED', 'SEWING', 'SEW_COMPLETED', 'QUALITY_CHECK', 'COMPLETED', 'REWORK', 'ON_HOLD', 'CANCELLED', name='bundlestatus'), nullable=False, comment='扎状态'),
    sa.Column('progress_percentage', sa.Integer(), nullable=False, comment='完成百分比'),
    sa.Column('planned_start_date', sa.DateTime(), nullable=True, comment='计划开始时间'),
    sa.Column('actual_start_date', sa.DateTime(), nullable=True, comment='实际开始时间'),
    sa.Column('planned_end_date', sa.DateTime(), nullable=True, comment='计划完成时间'),
    sa.Column('actual_end_date', sa.DateTime(), nullable=True, comment='实际完成时间'),
    sa.Column('cut_start_date', sa.DateTime(), nullable=True, comment='裁剪开始时间'),
    sa.Column('cut_end_date', sa.DateTime(), nullable=True, comment='裁剪完成时间'),
    sa.Column('sew_start_date', sa.DateTime(), nullable=True, comment='缝制开始时间'),
    sa.Column('sew_end_date', sa.DateTime(), nullable=True, comment='缝制完成时间'),
    sa.Column('qc_start_date', sa.DateTime(), nullable=True, comment='质检开始时间'),
    sa.Column('qc_end_date', sa.DateTime(), nullable=True, comment='质检完成时间'),
    sa.Column('cutter_user_id', sa.Integer(), nullable=True, comment='裁剪工ID'),
    sa.Column('sewer_user_id', sa.Integer(), nullable=True, comment='缝制工ID'),
    sa.Column('qc_user_id', sa.Integer(), nullable=True, comment='质检员ID'),
    sa.Column('cutting_machine', sa.String(length=50), nullable=True, comment='裁剪机床'),
    sa.Column('sewing_machine', sa.String(length=50), nullable=True, comment='缝制机床'),
    sa.Column('quality_level', sa.String(length=20), nullable=True, comment='质量等级(A/B/C)'),
    sa.Column('quality_notes', sa.Text(), nullable=True, comment='质量备注'),
    sa.Column('notes', sa.Text(), nullable=True, comment='备注'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新时间'),
    sa.ForeignKeyConstraint(['cutter_user_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['order_no'], ['orders.order_no'], ),
    sa.ForeignKeyConstraint(['part_no'], ['parts.part_no'], ),
    sa.ForeignKeyConstraint(['qc_user_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['sewer_user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_bundles_bundle_no'), 'bundles', ['bundle_no'], unique=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_bundles_bundle_no'), table_name='bundles')
    op.drop_table('bundles')
    op.drop_index(op.f('ix_parts_part_no'), table_name='parts')
    op.drop_table('parts')
    # ### end Alembic commands ###