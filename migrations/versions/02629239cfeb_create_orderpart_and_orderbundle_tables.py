"""Create OrderPart and OrderBundle tables

Revision ID: 02629239cfeb
Revises: cad706eb7742
Create Date: 2025-06-23 21:47:23.217943

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '02629239cfeb'
down_revision = 'cad706eb7742'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('craft_routes', 'code',
               existing_type=mysql.VARCHAR(length=50),
               comment='Unique code within craft',
               existing_nullable=False)
    op.alter_column('craft_routes', 'name',
               existing_type=mysql.VARCHAR(length=100),
               comment='Display name for this route',
               existing_nullable=False)
    op.add_column('order_lines', sa.Column('amount', sa.Integer(), nullable=False, comment='数量'))
    op.add_column('order_lines', sa.Column('produced_amount', sa.Integer(), nullable=False, comment='已生产数量'))
    op.add_column('order_lines', sa.Column('completed_amount', sa.Integer(), nullable=False, comment='已完成数量'))
    op.alter_column('order_lines', 'order_line_no',
               existing_type=mysql.VARCHAR(length=100),
               type_=sa.String(length=150),
               comment='订单行号 {order_no}_{size}',
               existing_comment='订单行号',
               existing_nullable=False)
    op.alter_column('order_lines', 'notes',
               existing_type=mysql.TEXT(),
               type_=sa.String(length=500),
               existing_comment='备注',
               existing_nullable=True)
    op.alter_column('order_lines', 'created_at',
               existing_type=mysql.DATETIME(),
               comment=None,
               existing_comment='创建时间',
               existing_nullable=False)
    op.alter_column('order_lines', 'updated_at',
               existing_type=mysql.DATETIME(),
               comment=None,
               existing_comment='更新时间',
               existing_nullable=False)
    op.create_index(op.f('ix_order_lines_size'), 'order_lines', ['size'], unique=False)
    op.drop_column('order_lines', 'quantity')
    op.drop_column('order_lines', 'total_price')
    op.drop_column('order_lines', 'skc_no')
    op.drop_column('order_lines', 'unit_price')
    op.drop_column('order_lines', 'color')
    op.add_column('order_parts', sa.Column('order_id', sa.Integer(), nullable=False, comment='订单ID'))
    op.create_foreign_key(None, 'order_parts', 'orders', ['order_id'], ['id'])
    op.add_column('orders', sa.Column('skc_no', sa.String(length=100), nullable=False, comment='款号'))
    op.add_column('orders', sa.Column('external_skc_no', sa.String(length=100), nullable=True, comment='外部款号'))
    op.add_column('orders', sa.Column('external_order_no', sa.String(length=100), nullable=True, comment='外部订单号'))
    op.add_column('orders', sa.Column('external_order_no2', sa.String(length=100), nullable=True, comment='外部订单号2'))
    op.add_column('orders', sa.Column('total_amount', sa.Integer(), nullable=False, comment='总数量'))
    op.add_column('orders', sa.Column('cost', sa.Numeric(precision=10, scale=2), nullable=True, comment='成本'))
    op.add_column('orders', sa.Column('price', sa.Numeric(precision=10, scale=2), nullable=True, comment='价格'))
    op.add_column('orders', sa.Column('expect_finished_at', sa.DateTime(), nullable=True, comment='预期完成时间'))
    op.add_column('orders', sa.Column('finished_at', sa.DateTime(), nullable=True, comment='实际完成时间'))
    op.add_column('orders', sa.Column('owner_user_id', sa.Integer(), nullable=True, comment='负责人用户ID'))
    op.add_column('orders', sa.Column('current_craft', sa.String(length=50), nullable=True, comment='当前工艺'))
    op.add_column('orders', sa.Column('current_craft_route', sa.Integer(), nullable=True, comment='当前工艺路线'))
    op.add_column('orders', sa.Column('description', sa.Text(), nullable=True, comment='订单描述'))
    op.alter_column('orders', 'created_at',
               existing_type=mysql.DATETIME(),
               comment=None,
               existing_comment='创建时间',
               existing_nullable=False)
    op.alter_column('orders', 'updated_at',
               existing_type=mysql.DATETIME(),
               comment=None,
               existing_comment='更新时间',
               existing_nullable=False)
    op.alter_column('orders', 'status',
               existing_type=mysql.ENUM('PENDING', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED'),
               comment=None,
               existing_comment='订单状态',
               existing_nullable=False)
    op.create_index(op.f('ix_orders_current_craft'), 'orders', ['current_craft'], unique=False)
    op.create_index(op.f('ix_orders_current_craft_route'), 'orders', ['current_craft_route'], unique=False)
    op.create_index(op.f('ix_orders_external_order_no'), 'orders', ['external_order_no'], unique=False)
    op.create_index(op.f('ix_orders_external_order_no2'), 'orders', ['external_order_no2'], unique=False)
    op.create_index(op.f('ix_orders_external_skc_no'), 'orders', ['external_skc_no'], unique=False)
    op.create_index(op.f('ix_orders_owner_user_id'), 'orders', ['owner_user_id'], unique=False)
    op.create_index(op.f('ix_orders_skc_no'), 'orders', ['skc_no'], unique=False)
    op.create_index(op.f('ix_orders_status'), 'orders', ['status'], unique=False)
    op.create_foreign_key(None, 'orders', 'craft_routes', ['current_craft_route'], ['id'])
    op.create_foreign_key(None, 'orders', 'users', ['owner_user_id'], ['id'])
    op.create_foreign_key(None, 'orders', 'crafts', ['current_craft'], ['code'])
    op.drop_column('orders', 'completed_quantity')
    op.drop_column('orders', 'delivery_date')
    op.drop_column('orders', 'order_date')
    op.drop_column('orders', 'product_name')
    op.drop_column('orders', 'customer_name')
    op.drop_column('orders', 'total_quantity')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('orders', sa.Column('total_quantity', mysql.INTEGER(), autoincrement=False, nullable=False, comment='订单总数量'))
    op.add_column('orders', sa.Column('customer_name', mysql.VARCHAR(length=200), nullable=False, comment='客户名称'))
    op.add_column('orders', sa.Column('product_name', mysql.VARCHAR(length=200), nullable=False, comment='产品名称'))
    op.add_column('orders', sa.Column('order_date', sa.DATE(), nullable=False, comment='下单日期'))
    op.add_column('orders', sa.Column('delivery_date', sa.DATE(), nullable=True, comment='交货日期'))
    op.add_column('orders', sa.Column('completed_quantity', mysql.INTEGER(), autoincrement=False, nullable=False, comment='已完成数量'))
    op.drop_constraint(None, 'orders', type_='foreignkey')
    op.drop_constraint(None, 'orders', type_='foreignkey')
    op.drop_constraint(None, 'orders', type_='foreignkey')
    op.drop_index(op.f('ix_orders_status'), table_name='orders')
    op.drop_index(op.f('ix_orders_skc_no'), table_name='orders')
    op.drop_index(op.f('ix_orders_owner_user_id'), table_name='orders')
    op.drop_index(op.f('ix_orders_external_skc_no'), table_name='orders')
    op.drop_index(op.f('ix_orders_external_order_no2'), table_name='orders')
    op.drop_index(op.f('ix_orders_external_order_no'), table_name='orders')
    op.drop_index(op.f('ix_orders_current_craft_route'), table_name='orders')
    op.drop_index(op.f('ix_orders_current_craft'), table_name='orders')
    op.alter_column('orders', 'status',
               existing_type=mysql.ENUM('PENDING', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED'),
               comment='订单状态',
               existing_nullable=False)
    op.alter_column('orders', 'updated_at',
               existing_type=mysql.DATETIME(),
               comment='更新时间',
               existing_nullable=False)
    op.alter_column('orders', 'created_at',
               existing_type=mysql.DATETIME(),
               comment='创建时间',
               existing_nullable=False)
    op.drop_column('orders', 'description')
    op.drop_column('orders', 'current_craft_route')
    op.drop_column('orders', 'current_craft')
    op.drop_column('orders', 'owner_user_id')
    op.drop_column('orders', 'finished_at')
    op.drop_column('orders', 'expect_finished_at')
    op.drop_column('orders', 'price')
    op.drop_column('orders', 'cost')
    op.drop_column('orders', 'total_amount')
    op.drop_column('orders', 'external_order_no2')
    op.drop_column('orders', 'external_order_no')
    op.drop_column('orders', 'external_skc_no')
    op.drop_column('orders', 'skc_no')
    op.drop_constraint(None, 'order_parts', type_='foreignkey')
    op.drop_column('order_parts', 'order_id')
    op.add_column('order_lines', sa.Column('color', mysql.VARCHAR(length=50), nullable=False, comment='颜色'))
    op.add_column('order_lines', sa.Column('unit_price', mysql.DECIMAL(precision=10, scale=2), nullable=True, comment='单价'))
    op.add_column('order_lines', sa.Column('skc_no', mysql.VARCHAR(length=100), nullable=False, comment='款号'))
    op.add_column('order_lines', sa.Column('total_price', mysql.DECIMAL(precision=10, scale=2), nullable=True, comment='总价'))
    op.add_column('order_lines', sa.Column('quantity', mysql.INTEGER(), autoincrement=False, nullable=False, comment='数量'))
    op.drop_index(op.f('ix_order_lines_size'), table_name='order_lines')
    op.alter_column('order_lines', 'updated_at',
               existing_type=mysql.DATETIME(),
               comment='更新时间',
               existing_nullable=False)
    op.alter_column('order_lines', 'created_at',
               existing_type=mysql.DATETIME(),
               comment='创建时间',
               existing_nullable=False)
    op.alter_column('order_lines', 'notes',
               existing_type=sa.String(length=500),
               type_=mysql.TEXT(),
               existing_comment='备注',
               existing_nullable=True)
    op.alter_column('order_lines', 'order_line_no',
               existing_type=sa.String(length=150),
               type_=mysql.VARCHAR(length=100),
               comment='订单行号',
               existing_comment='订单行号 {order_no}_{size}',
               existing_nullable=False)
    op.drop_column('order_lines', 'completed_amount')
    op.drop_column('order_lines', 'produced_amount')
    op.drop_column('order_lines', 'amount')
    op.alter_column('craft_routes', 'name',
               existing_type=mysql.VARCHAR(length=100),
               comment=None,
               existing_comment='Display name for this route',
               existing_nullable=False)
    op.alter_column('craft_routes', 'code',
               existing_type=mysql.VARCHAR(length=50),
               comment=None,
               existing_comment='Unique code within craft',
               existing_nullable=False)
    # ### end Alembic commands ###