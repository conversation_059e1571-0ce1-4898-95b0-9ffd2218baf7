"""Add garment crafts and craft routes with complete workflow

Revision ID: d3e4f5a6b7c8
Revises: c2d3e4f5a6b7
Create Date: 2025-06-20 15:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'd3e4f5a6b7c8'
down_revision = 'c2d3e4f5a6b7'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Add garment crafts and craft routes to the database."""
    connection = op.get_bind()
    
    print("Adding garment crafts...")
    
    # Insert crafts data
    crafts_data = [
        ('CUT', '裁剪', 1, True, 'Cutting operations for garment production'),
        ('SEW', '车缝', 2, True, 'Sewing operations for garment assembly'),
        ('FIN', '后整', 3, True, 'Finishing operations for garment completion'),
    ]
    
    # Insert crafts
    for craft_code, craft_name, priority, enabled, description in crafts_data:
        try:
            connection.execute(sa.text("""
                INSERT INTO crafts (code, name, priority, enabled, description, created_at, updated_at)
                VALUES (:code, :name, :priority, :enabled, :description, NOW(), NOW())
            """), {
                'code': craft_code,
                'name': craft_name,
                'priority': priority,
                'enabled': enabled,
                'description': description
            })
            print(f"  ✅ Added craft: {craft_code} - {craft_name}")
        except Exception as e:
            print(f"  ⚠️  Craft {craft_code} may already exist: {e}")
    
    print("Adding craft routes...")
    
    # First add the code and name columns to craft_routes if they don't exist
    try:
        connection.execute(sa.text("ALTER TABLE craft_routes ADD COLUMN code VARCHAR(50) COMMENT 'Unique code within craft'"))
        connection.execute(sa.text("ALTER TABLE craft_routes ADD COLUMN name VARCHAR(100) COMMENT 'Display name for this route'"))
        connection.execute(sa.text("CREATE INDEX ix_craft_routes_code ON craft_routes (code)"))
        print("  ✅ Added code and name columns to craft_routes")
    except Exception as e:
        print(f"  ⚠️  Columns may already exist: {e}")
    
    # Insert craft routes data
    craft_routes_data = [
        # 裁剪工艺工序 (Cutting Craft Routes)
        ('CUT', 'CUT_PREP', '面料检验', 'CUT_005', 1, ['ALL', 'COUNT'], ['ALL', 'PART'], True),
        ('CUT', 'CUT_LAY', '排料铺布', 'CUT_004', 2, ['ALL', 'TIME'], ['ALL'], True),
        ('CUT', 'CUT_STR', '直刀裁剪', 'CUT_001', 3, ['COUNT', 'TIME'], ['PART', 'BUNDLER'], True),
        ('CUT', 'CUT_DET', '电剪修整', 'CUT_002', 4, ['COUNT', 'TIME'], ['BUNDLER'], True),
        ('CUT', 'CUT_LAS', '激光裁剪', 'CUT_003', 5, ['COUNT', 'TIME'], ['PART', 'BUNDLER'], True),
        
        # 车缝工艺工序 (Sewing Craft Routes)
        ('SEW', 'SEW_LOCK', '平缝拼接', 'SEW_001', 1, ['COUNT', 'TIME'], ['BUNDLER'], True),
        ('SEW', 'SEW_OVER', '包缝锁边', 'SEW_002', 2, ['COUNT', 'TIME'], ['BUNDLER'], True),
        ('SEW', 'SEW_TWIN', '双针车缝', 'SEW_003', 3, ['COUNT', 'TIME'], ['BUNDLER'], True),
        ('SEW', 'SEW_FLAT', '绷缝处理', 'SEW_004', 4, ['COUNT', 'TIME'], ['BUNDLER'], True),
        ('SEW', 'SEW_BTN', '钉扣锁眼', 'SPE_001', 5, ['COUNT', 'TIME'], ['BUNDLER'], True),
        
        # 后整工艺工序 (Finishing Craft Routes)
        ('FIN', 'FIN_PRES', '中烫整型', 'PRE_001', 1, ['COUNT', 'TIME'], ['BUNDLER'], True),
        ('FIN', 'FIN_STEAM', '蒸汽整理', 'PRE_003', 2, ['COUNT', 'TIME'], ['BUNDLER'], True),
        ('FIN', 'FIN_QC', '质量检验', 'QC_002', 3, ['ALL', 'COUNT'], ['ALL', 'PART'], True),
        ('FIN', 'FIN_SIZE', '尺寸检测', 'QC_001', 4, ['COUNT'], ['BUNDLER'], True),
        ('FIN', 'FIN_FINAL', '成品检验', 'QC_005', 5, ['ALL', 'COUNT'], ['ALL', 'PART'], True),
    ]
    
    # Insert craft routes
    for craft_code, route_code, route_name, skill_code, order, measurement_types, registration_types, is_required in craft_routes_data:
        try:
            connection.execute(sa.text("""
                INSERT INTO craft_routes (craft_code, skill_code, code, name, `order`, measurement_types, registration_types, is_required, created_at, updated_at)
                VALUES (:craft_code, :skill_code, :code, :name, :order, :measurement_types, :registration_types, :is_required, NOW(), NOW())
            """), {
                'craft_code': craft_code,
                'skill_code': skill_code,
                'code': route_code,
                'name': route_name,
                'order': order,
                'measurement_types': str(measurement_types).replace("'", '"'),  # Convert to JSON format
                'registration_types': str(registration_types).replace("'", '"'),  # Convert to JSON format
                'is_required': is_required
            })
            print(f"  ✅ Added route: {craft_code}.{route_code} - {route_name}")
        except Exception as e:
            print(f"  ⚠️  Route {craft_code}.{route_code} may already exist: {e}")
    
    # Add unique constraint on craft_code + code
    try:
        connection.execute(sa.text("ALTER TABLE craft_routes ADD CONSTRAINT uq_craft_route_craft_code_code UNIQUE (craft_code, code)"))
        print("  ✅ Added unique constraint on (craft_code, code)")
    except Exception as e:
        print(f"  ⚠️  Constraint may already exist: {e}")


def downgrade() -> None:
    """Remove garment crafts and craft routes from the database."""
    connection = op.get_bind()
    
    print("Removing craft routes...")
    
    # Remove craft routes
    craft_codes = ['CUT', 'SEW', 'FIN']
    for craft_code in craft_codes:
        try:
            connection.execute(sa.text("""
                DELETE FROM craft_routes WHERE craft_code = :craft_code
            """), {'craft_code': craft_code})
            print(f"  ✅ Removed routes for craft: {craft_code}")
        except Exception as e:
            print(f"  ⚠️  Error removing routes for {craft_code}: {e}")
    
    print("Removing crafts...")
    
    # Remove crafts
    for craft_code in craft_codes:
        try:
            connection.execute(sa.text("""
                DELETE FROM crafts WHERE code = :craft_code
            """), {'craft_code': craft_code})
            print(f"  ✅ Removed craft: {craft_code}")
        except Exception as e:
            print(f"  ⚠️  Error removing craft {craft_code}: {e}")