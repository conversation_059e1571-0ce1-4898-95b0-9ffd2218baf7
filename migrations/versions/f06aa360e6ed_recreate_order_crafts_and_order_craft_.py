"""recreate order_crafts and order_craft_routes tables

Revision ID: f06aa360e6ed
Revises: 02629239cfeb
Create Date: 2025-06-23 22:38:23.489636

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'f06aa360e6ed'
down_revision = '02629239cfeb'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    
    # Create order_crafts table
    op.create_table('order_crafts',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('order_no', sa.String(length=100), nullable=False),
        sa.Column('order_id', sa.Integer(), nullable=False, comment='Order ID this craft belongs to'),
        sa.Column('craft_code', sa.String(length=50), nullable=False),
        sa.Column('craft_name', sa.String(length=100), nullable=True, comment='Craft name for easy reference'),
        sa.Column('factory_id', sa.Integer(), nullable=False, comment='Factory ID this craft belongs to'),
        sa.Column('order', sa.Integer(), nullable=False, server_default='0', comment='Order sequence in the workflow'),
        sa.Column('is_required', sa.Boolean(), nullable=False, server_default=sa.text('1'), comment='Whether this craft is required'),
        sa.Column('is_active', sa.Boolean(), nullable=False, server_default=sa.text('1'), comment='Whether this craft is currently active'),
        sa.Column('status', sa.String(length=20), nullable=False, server_default='pending', comment='Status: pending, in_progress, completed, skipped'),
        sa.Column('started_at', sa.DateTime(), nullable=True, comment='When this craft started'),
        sa.Column('completed_at', sa.DateTime(), nullable=True, comment='When this craft was completed'),
        sa.Column('notes', sa.Text(), nullable=True, comment='Notes specific to this order-craft assignment'),
        sa.Column('estimated_duration_hours', sa.Integer(), nullable=True, comment='Estimated duration in hours'),
        sa.Column('actual_duration_hours', sa.Integer(), nullable=True, comment='Actual duration in hours'),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['order_no'], ['orders.order_no'], ),
        sa.ForeignKeyConstraint(['order_id'], ['orders.id'], ),
        sa.ForeignKeyConstraint(['craft_code'], ['crafts.code'], ),
        sa.ForeignKeyConstraint(['factory_id'], ['factories.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_order_crafts_order_no'), 'order_crafts', ['order_no'], unique=False)
    op.create_index(op.f('ix_order_crafts_order_id'), 'order_crafts', ['order_id'], unique=False)
    op.create_index(op.f('ix_order_crafts_craft_code'), 'order_crafts', ['craft_code'], unique=False)
    op.create_index(op.f('ix_order_crafts_factory_id'), 'order_crafts', ['factory_id'], unique=False)
    op.create_index(op.f('ix_order_crafts_order'), 'order_crafts', ['order'], unique=False)
    op.create_index(op.f('ix_order_crafts_status'), 'order_crafts', ['status'], unique=False)

    # Create order_craft_routes table
    op.create_table('order_craft_routes',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('factory_id', sa.Integer(), nullable=False, comment='Factory ID this route belongs to'),
        sa.Column('order_craft_id', sa.Integer(), nullable=False),
        sa.Column('skill_code', sa.String(length=50), nullable=False),
        sa.Column('name', sa.String(length=100), nullable=True, comment='Route name for easy reference'),
        sa.Column('code', sa.String(length=50), nullable=True, comment='Route code for easy reference'),
        sa.Column('order', sa.Integer(), nullable=False, server_default='0', comment='Order sequence within the craft'),
        sa.Column('measurement_types', sa.JSON(), nullable=True, comment='Available measurement types for this route'),
        sa.Column('registration_types', sa.JSON(), nullable=True, comment='Available registration types for this route'),
        sa.Column('is_required', sa.Boolean(), nullable=False, server_default=sa.text('1'), comment='Whether this route is required for this order'),
        sa.Column('is_active', sa.Boolean(), nullable=False, server_default=sa.text('1'), comment='Whether this route is currently active'),
        sa.Column('status', sa.String(length=20), nullable=False, server_default='pending', comment='Status: pending, in_progress, completed, skipped'),
        sa.Column('started_at', sa.DateTime(), nullable=True, comment='When this route started'),
        sa.Column('completed_at', sa.DateTime(), nullable=True, comment='When this route was completed'),
        sa.Column('assigned_user_id', sa.Integer(), nullable=True, comment='User assigned to this route'),
        sa.Column('estimated_duration_minutes', sa.Integer(), nullable=True, comment='Estimated duration in minutes'),
        sa.Column('actual_duration_minutes', sa.Integer(), nullable=True, comment='Actual duration in minutes'),
        sa.Column('price', sa.DECIMAL(precision=10, scale=2), nullable=True, comment='Unit price for this route'),
        sa.Column('total_cost', sa.DECIMAL(precision=10, scale=2), nullable=True, comment='Total cost for this route'),
        sa.Column('quality_score', sa.Integer(), nullable=True, comment='Quality score (0-100)'),
        sa.Column('measurement_data', sa.JSON(), nullable=True, comment='Measurement data collected during this route'),
        sa.Column('registration_data', sa.JSON(), nullable=True, comment='Registration data collected during this route'),
        sa.Column('notes', sa.Text(), nullable=True, comment='Notes specific to this order-craft-route'),
        sa.Column('completion_notes', sa.Text(), nullable=True, comment='Notes upon completion'),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['factory_id'], ['factories.id'], ),
        sa.ForeignKeyConstraint(['order_craft_id'], ['order_crafts.id'], ),
        sa.ForeignKeyConstraint(['skill_code'], ['skills.code'], ),
        sa.ForeignKeyConstraint(['assigned_user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_order_craft_routes_factory_id'), 'order_craft_routes', ['factory_id'], unique=False)
    op.create_index(op.f('ix_order_craft_routes_order_craft_id'), 'order_craft_routes', ['order_craft_id'], unique=False)
    op.create_index(op.f('ix_order_craft_routes_skill_code'), 'order_craft_routes', ['skill_code'], unique=False)
    op.create_index(op.f('ix_order_craft_routes_order'), 'order_craft_routes', ['order'], unique=False)
    op.create_index(op.f('ix_order_craft_routes_status'), 'order_craft_routes', ['status'], unique=False)
    
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    
    # Drop order_craft_routes table
    op.drop_index(op.f('ix_order_craft_routes_status'), table_name='order_craft_routes')
    op.drop_index(op.f('ix_order_craft_routes_order'), table_name='order_craft_routes')
    op.drop_index(op.f('ix_order_craft_routes_skill_code'), table_name='order_craft_routes')
    op.drop_index(op.f('ix_order_craft_routes_order_craft_id'), table_name='order_craft_routes')
    op.drop_index(op.f('ix_order_craft_routes_factory_id'), table_name='order_craft_routes')
    op.drop_table('order_craft_routes')
    
    # Drop order_crafts table
    op.drop_index(op.f('ix_order_crafts_status'), table_name='order_crafts')
    op.drop_index(op.f('ix_order_crafts_order'), table_name='order_crafts')
    op.drop_index(op.f('ix_order_crafts_factory_id'), table_name='order_crafts')
    op.drop_index(op.f('ix_order_crafts_craft_code'), table_name='order_crafts')
    op.drop_index(op.f('ix_order_crafts_order_id'), table_name='order_crafts')
    op.drop_index(op.f('ix_order_crafts_order_no'), table_name='order_crafts')
    op.drop_table('order_crafts')
    
    # ### end Alembic commands ###