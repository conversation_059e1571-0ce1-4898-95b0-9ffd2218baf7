"""Add garment manufacturing skills

Revision ID: c2d3e4f5a6b7
Revises: b1e2d3f4a5c6
Create Date: 2025-06-20 12:30:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'c2d3e4f5a6b7'
down_revision = 'b1e2d3f4a5c6'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Add garment manufacturing skills to the database."""
    connection = op.get_bind()
    
    # Insert garment manufacturing skills with Chinese names and English descriptions
    skills_data = [
        # 裁剪工技能 (Cutting Operator)
        ('CUT_001', '直刀裁剪机', 'Straight Knife Cutting Machine operation skills', True, 'cutting'),
        ('CUT_002', '电动剪刀', 'Electric Scissors operation skills', True, 'cutting'),
        ('CUT_003', '激光裁剪机', 'Laser Cutting Machine operation skills', True, 'cutting'),
        ('CUT_004', '排料技能', 'Pattern Layout Skills for efficient fabric cutting', True, 'cutting'),
        ('CUT_005', '面料检验', 'Fabric Inspection and quality assessment skills', True, 'cutting'),
        
        # 车缝工技能 (Sewing Operator)
        ('SEW_001', '平缝机', 'Lockstitch Sewing Machine operation skills', True, 'sewing'),
        ('SEW_002', '包缝机/拷边机', 'Overlock Machine operation skills', True, 'sewing'),
        ('SEW_003', '双针机', 'Double Needle Machine operation skills', True, 'sewing'),
        ('SEW_004', '绷缝机', 'Flatlock Machine operation skills', True, 'sewing'),
        ('SEW_005', '之字缝机', 'Zigzag Stitching Machine operation skills', True, 'sewing'),
        
        # 专机操作工技能 (Special Machine Operator)
        ('SPE_001', '锁眼机', 'Buttonhole Machine operation skills', True, 'machine_operation'),
        ('SPE_002', '钉扣机', 'Button Sewing Machine operation skills', True, 'machine_operation'),
        ('SPE_003', '暗缝机', 'Blind Hemming Machine operation skills', True, 'machine_operation'),
        ('SPE_004', '打枣机', 'Bartacking Machine operation skills', True, 'machine_operation'),
        ('SPE_005', '刺绣机', 'Embroidery Machine operation skills', True, 'machine_operation'),
        
        # 整烫工技能 (Pressing Operator)
        ('PRE_001', '蒸汽熨斗', 'Steam Iron operation skills', True, 'pressing'),
        ('PRE_002', '压烫机', 'Press Machine operation skills', True, 'pressing'),
        ('PRE_003', '蒸汽整理机', 'Steam Finishing Machine operation skills', True, 'pressing'),
        ('PRE_004', '隧道式整烫机', 'Tunnel Finisher operation skills', True, 'pressing'),
        ('PRE_005', '定型机', 'Shape Setting Machine operation skills', True, 'pressing'),
        
        # 质检工技能 (Quality Inspector)
        ('QC_001', '测量工具', 'Measuring Tools usage and application skills', True, 'quality_control'),
        ('QC_002', '缝制质量评估', 'Seam Quality Assessment and inspection skills', True, 'quality_control'),
        ('QC_003', '面料缺陷检测', 'Fabric Defect Detection and identification skills', True, 'quality_control'),
        ('QC_004', '尺码分级知识', 'Size Grading Knowledge and measurement standards', True, 'quality_control'),
        ('QC_005', '成品检验技能', 'Final Inspection Skills for finished garments', True, 'quality_control'),
    ]
    
    # Insert skills using raw SQL to avoid conflicts with existing data
    for skill_code, skill_name, skill_description, is_active, category in skills_data:
        try:
            connection.execute(sa.text("""
                INSERT INTO skills (code, name, description, is_active, category)
                VALUES (:code, :name, :description, :is_active, :category)
            """), {
                'code': skill_code,
                'name': skill_name,
                'description': skill_description,
                'is_active': is_active,
                'category': category
            })
        except Exception as e:
            # Skip if skill already exists (duplicate code)
            print(f"Skill {skill_code} may already exist, skipping: {e}")
            pass


def downgrade() -> None:
    """Remove garment manufacturing skills from the database."""
    connection = op.get_bind()
    
    # List of skill codes to remove
    skill_codes = [
        'CUT_001', 'CUT_002', 'CUT_003', 'CUT_004', 'CUT_005',
        'SEW_001', 'SEW_002', 'SEW_003', 'SEW_004', 'SEW_005',
        'SPE_001', 'SPE_002', 'SPE_003', 'SPE_004', 'SPE_005',
        'PRE_001', 'PRE_002', 'PRE_003', 'PRE_004', 'PRE_005',
        'QC_001', 'QC_002', 'QC_003', 'QC_004', 'QC_005'
    ]
    
    # Remove skills and related data
    for skill_code in skill_codes:
        try:
            # First remove user_factory_skills relationships
            connection.execute(sa.text("""
                DELETE FROM user_factory_skills 
                WHERE skill_code = :skill_code
            """), {'skill_code': skill_code})
            
            # Then remove the skill itself
            connection.execute(sa.text("""
                DELETE FROM skills 
                WHERE code = :skill_code
            """), {'skill_code': skill_code})
        except Exception as e:
            print(f"Error removing skill {skill_code}: {e}")
            pass