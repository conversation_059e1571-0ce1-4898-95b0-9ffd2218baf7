"""Add craft_name to order_crafts and name, code to order_craft_routes

Revision ID: 7f6b947dfba3
Revises: d3e4f5a6b7c8
Create Date: 2025-06-23 18:34:33.999968

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '7f6b947dfba3'
down_revision = 'd3e4f5a6b7c8'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Add craft_name column to order_crafts table
    op.add_column('order_crafts', sa.Column('craft_name', sa.String(length=100), nullable=True, comment='Craft name for easy reference'))
    
    # Add name and code columns to order_craft_routes table
    op.add_column('order_craft_routes', sa.Column('name', sa.String(length=100), nullable=True, comment='Route name for easy reference'))
    op.add_column('order_craft_routes', sa.Column('code', sa.String(length=50), nullable=True, comment='Route code for easy reference'))


def downgrade() -> None:
    # Remove columns added in upgrade
    op.drop_column('order_craft_routes', 'code')
    op.drop_column('order_craft_routes', 'name')
    op.drop_column('order_crafts', 'craft_name')