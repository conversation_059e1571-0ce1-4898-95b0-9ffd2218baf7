"""Add price and total_cost fields to order_craft_routes

Revision ID: 3ad629ec3967
Revises: 7f6b947dfba3
Create Date: 2025-06-23 18:38:44.514974

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '3ad629ec3967'
down_revision = '7f6b947dfba3'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Add price and total_cost columns to order_craft_routes table
    op.add_column('order_craft_routes', sa.Column('price', sa.DECIMAL(precision=10, scale=2), nullable=True, comment='Unit price for this route'))
    op.add_column('order_craft_routes', sa.Column('total_cost', sa.DECIMAL(precision=10, scale=2), nullable=True, comment='Total cost for this route'))


def downgrade() -> None:
    # Remove columns added in upgrade
    op.drop_column('order_craft_routes', 'total_cost')
    op.drop_column('order_craft_routes', 'price')