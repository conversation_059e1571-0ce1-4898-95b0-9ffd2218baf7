"""add comprehensive permissions for skills crafts orders and production

Revision ID: 14b0ab39c725
Revises: a363679100cb
Create Date: 2025-06-19 22:35:04.105722

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '14b0ab39c725'
down_revision = 'a363679100cb'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create tables metadata for raw SQL operations
    connection = op.get_bind()
    
    # Insert top-level permission categories
    connection.execute(sa.text("""
        INSERT IGNORE INTO permissions (code, name, parent_id) VALUES
        ('skills_management', '技能管理权限', NULL),
        ('crafts_management', '工艺管理权限', NULL),
        ('craft_routes_management', '工艺路线管理权限', NULL),
        ('orders_management', '订单管理权限', NULL),
        ('production_management', '生产管理权限', NULL),
        ('statistics_management', '统计管理权限', NULL),
        ('dashboard_management', '仪表板管理权限', NULL),
        ('system_management', '系统管理权限', NULL),
        ('api_access', 'API访问权限', NULL)
    """))
    
    # Insert skills-related permissions under skills_management
    connection.execute(sa.text("""
        INSERT IGNORE INTO permissions (code, name, parent_id) 
        SELECT 'skills.view', '查看技能', p.id FROM permissions p WHERE p.code = 'skills_management'
        UNION ALL
        SELECT 'skills.create', '创建技能', p.id FROM permissions p WHERE p.code = 'skills_management'
        UNION ALL
        SELECT 'skills.update', '修改技能', p.id FROM permissions p WHERE p.code = 'skills_management'
        UNION ALL
        SELECT 'skills.delete', '删除技能', p.id FROM permissions p WHERE p.code = 'skills_management'
        UNION ALL
        SELECT 'skills.manage', '管理技能', p.id FROM permissions p WHERE p.code = 'skills_management'
        UNION ALL
        SELECT 'skills.certify', '认证用户技能', p.id FROM permissions p WHERE p.code = 'skills_management'
    """))
    
    # Insert crafts-related permissions under crafts_management
    connection.execute(sa.text("""
        INSERT IGNORE INTO permissions (code, name, parent_id) 
        SELECT 'crafts.view', '查看工艺', p.id FROM permissions p WHERE p.code = 'crafts_management'
        UNION ALL
        SELECT 'crafts.create', '创建工艺', p.id FROM permissions p WHERE p.code = 'crafts_management'
        UNION ALL
        SELECT 'crafts.update', '修改工艺', p.id FROM permissions p WHERE p.code = 'crafts_management'
        UNION ALL
        SELECT 'crafts.delete', '删除工艺', p.id FROM permissions p WHERE p.code = 'crafts_management'
        UNION ALL
        SELECT 'crafts.manage', '管理工艺', p.id FROM permissions p WHERE p.code = 'crafts_management'
    """))
    
    # Insert craft routes-related permissions under craft_routes_management
    connection.execute(sa.text("""
        INSERT IGNORE INTO permissions (code, name, parent_id) 
        SELECT 'craft_routes.view', '查看工艺路线', p.id FROM permissions p WHERE p.code = 'craft_routes_management'
        UNION ALL
        SELECT 'craft_routes.create', '创建工艺路线', p.id FROM permissions p WHERE p.code = 'craft_routes_management'
        UNION ALL
        SELECT 'craft_routes.update', '修改工艺路线', p.id FROM permissions p WHERE p.code = 'craft_routes_management'
        UNION ALL
        SELECT 'craft_routes.delete', '删除工艺路线', p.id FROM permissions p WHERE p.code = 'craft_routes_management'
        UNION ALL
        SELECT 'craft_routes.manage', '管理工艺路线', p.id FROM permissions p WHERE p.code = 'craft_routes_management'
    """))
    
    # Insert orders-related permissions under orders_management
    connection.execute(sa.text("""
        INSERT IGNORE INTO permissions (code, name, parent_id) 
        SELECT 'orders.view', '查看订单', p.id FROM permissions p WHERE p.code = 'orders_management'
        UNION ALL
        SELECT 'orders.create', '创建订单', p.id FROM permissions p WHERE p.code = 'orders_management'
        UNION ALL
        SELECT 'orders.update', '修改订单', p.id FROM permissions p WHERE p.code = 'orders_management'
        UNION ALL
        SELECT 'orders.delete', '删除订单', p.id FROM permissions p WHERE p.code = 'orders_management'
        UNION ALL
        SELECT 'orders.manage', '管理订单', p.id FROM permissions p WHERE p.code = 'orders_management'
        UNION ALL
        SELECT 'orders.view_all', '查看所有订单', p.id FROM permissions p WHERE p.code = 'orders_management'
        UNION ALL
        SELECT 'orders.manage_status', '管理订单状态', p.id FROM permissions p WHERE p.code = 'orders_management'
        UNION ALL
        SELECT 'orders.manage_craft_progress', '管理工艺进度', p.id FROM permissions p WHERE p.code = 'orders_management'
    """))
    
    # Insert production-related permissions under production_management
    connection.execute(sa.text("""
        INSERT IGNORE INTO permissions (code, name, parent_id) 
        SELECT 'production.view', '查看生产', p.id FROM permissions p WHERE p.code = 'production_management'
        UNION ALL
        SELECT 'production.manage', '管理生产', p.id FROM permissions p WHERE p.code = 'production_management'
        UNION ALL
        SELECT 'production.update_quantities', '更新生产数量', p.id FROM permissions p WHERE p.code = 'production_management'
        UNION ALL
        SELECT 'production.track_progress', '跟踪生产进度', p.id FROM permissions p WHERE p.code = 'production_management'
    """))
    
    # Insert statistics-related permissions under statistics_management
    connection.execute(sa.text("""
        INSERT IGNORE INTO permissions (code, name, parent_id) 
        SELECT 'statistics.view', '查看统计', p.id FROM permissions p WHERE p.code = 'statistics_management'
        UNION ALL
        SELECT 'statistics.view_all', '查看所有统计', p.id FROM permissions p WHERE p.code = 'statistics_management'
        UNION ALL
        SELECT 'statistics.export', '导出统计', p.id FROM permissions p WHERE p.code = 'statistics_management'
    """))
    
    # Insert dashboard-related permissions under dashboard_management
    connection.execute(sa.text("""
        INSERT IGNORE INTO permissions (code, name, parent_id) 
        SELECT 'dashboard.view', '查看仪表板', p.id FROM permissions p WHERE p.code = 'dashboard_management'
        UNION ALL
        SELECT 'dashboard.view_all', '查看所有仪表板数据', p.id FROM permissions p WHERE p.code = 'dashboard_management'
        UNION ALL
        SELECT 'dashboard.manage', '管理仪表板', p.id FROM permissions p WHERE p.code = 'dashboard_management'
    """))
    
    # Insert factory-related permissions (additional ones not in previous migration)
    connection.execute(sa.text("""
        INSERT IGNORE INTO permissions (code, name, parent_id) 
        SELECT 'factory.view', '查看工厂信息', p.id FROM permissions p WHERE p.code = 'factory_management'
        UNION ALL
        SELECT 'factory.manage', '管理工厂', p.id FROM permissions p WHERE p.code = 'factory_management'
        UNION ALL
        SELECT 'factory.view_users', '查看工厂用户', p.id FROM permissions p WHERE p.code = 'factory_management'
        UNION ALL
        SELECT 'factory.manage_users', '管理工厂用户', p.id FROM permissions p WHERE p.code = 'factory_management'
        UNION ALL
        SELECT 'factory.certify_skills', '认证用户技能', p.id FROM permissions p WHERE p.code = 'factory_management'
    """))
    
    # Insert system-related permissions under system_management
    connection.execute(sa.text("""
        INSERT IGNORE INTO permissions (code, name, parent_id) 
        SELECT 'system.view', '查看系统设置', p.id FROM permissions p WHERE p.code = 'system_management'
        UNION ALL
        SELECT 'system.configure', '配置系统', p.id FROM permissions p WHERE p.code = 'system_management'
        UNION ALL
        SELECT 'system.backup', '系统备份', p.id FROM permissions p WHERE p.code = 'system_management'
        UNION ALL
        SELECT 'system.restore', '系统恢复', p.id FROM permissions p WHERE p.code = 'system_management'
    """))
    
    # Insert API-related permissions under api_access
    connection.execute(sa.text("""
        INSERT IGNORE INTO permissions (code, name, parent_id) 
        SELECT 'api.read', 'API读取权限', p.id FROM permissions p WHERE p.code = 'api_access'
        UNION ALL
        SELECT 'api.write', 'API写入权限', p.id FROM permissions p WHERE p.code = 'api_access'
        UNION ALL
        SELECT 'api.admin', 'API管理员权限', p.id FROM permissions p WHERE p.code = 'api_access'
    """))
    
    # Insert admin permission at root level
    connection.execute(sa.text("""
        INSERT IGNORE INTO permissions (code, name, parent_id) VALUES
        ('ADMIN', '系统管理员权限', NULL)
    """))


def downgrade() -> None:
    # Remove new permissions
    connection = op.get_bind()
    
    # Remove child permissions first (due to foreign key constraint)
    connection.execute(sa.text("""
        DELETE FROM permissions WHERE code IN (
            'skills.view', 'skills.create', 'skills.update', 'skills.delete', 'skills.manage', 'skills.certify',
            'crafts.view', 'crafts.create', 'crafts.update', 'crafts.delete', 'crafts.manage',
            'craft_routes.view', 'craft_routes.create', 'craft_routes.update', 'craft_routes.delete', 'craft_routes.manage',
            'orders.view', 'orders.create', 'orders.update', 'orders.delete', 'orders.manage', 'orders.view_all', 'orders.manage_status', 'orders.manage_craft_progress',
            'production.view', 'production.manage', 'production.update_quantities', 'production.track_progress',
            'statistics.view', 'statistics.view_all', 'statistics.export',
            'dashboard.view', 'dashboard.view_all', 'dashboard.manage',
            'factory.view', 'factory.manage', 'factory.view_users', 'factory.manage_users', 'factory.certify_skills',
            'system.view', 'system.configure', 'system.backup', 'system.restore',
            'api.read', 'api.write', 'api.admin',
            'ADMIN'
        )
    """))
    
    # Remove parent permissions
    connection.execute(sa.text("""
        DELETE FROM permissions WHERE code IN (
            'skills_management', 'crafts_management', 'craft_routes_management', 'orders_management', 
            'production_management', 'statistics_management', 'dashboard_management', 'system_management', 'api_access'
        )
    """))