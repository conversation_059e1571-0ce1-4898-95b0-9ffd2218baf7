"""add order craft route instance and craft route codes

Revision ID: 7c7eec947099
Revises: abf1c622ba8c
Create Date: 2025-06-24 11:06:57.157726

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '7c7eec947099'
down_revision = 'abf1c622ba8c'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create order_craft_route_instances table
    op.create_table('order_craft_route_instances',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False, comment='ID'),
        sa.Column('factory_id', sa.Integer(), nullable=False, comment='工厂ID'),
        sa.Column('order_craft_route_id', sa.Integer(), nullable=False, comment='订单工艺路线ID'),
        sa.Column('completion_granularity', sa.Enum('BUNDLE', 'BED', 'ORDER', name='completiongranularity'), nullable=False, comment='完成粒度: bundle(按扎), bed(按床), order(整单)'),
        sa.Column('order_no', sa.String(length=100), nullable=False, comment='订单号'),
        sa.Column('order_part_no', sa.String(length=100), nullable=True, comment='订单部位号 (床级别或扎级别时填写)'),
        sa.Column('order_bundle_no', sa.String(length=100), nullable=True, comment='订单扎号 (扎级别时填写)'),
        sa.Column('worker_user_id', sa.Integer(), nullable=False, comment='完成工人ID'),
        sa.Column('completed_quantity', sa.Integer(), nullable=False, comment='完成数量'),
        sa.Column('quality_level', sa.String(length=10), nullable=True, comment='质量等级 A/B/C'),
        sa.Column('status', sa.String(length=20), nullable=False, comment='状态: completed, verified, rejected'),
        sa.Column('started_at', sa.DateTime(), nullable=True, comment='开始时间'),
        sa.Column('completed_at', sa.DateTime(), nullable=False, comment='完成时间'),
        sa.Column('qr_code_scanned', sa.String(length=200), nullable=True, comment='扫描的二维码内容'),
        sa.Column('scan_location', sa.String(length=100), nullable=True, comment='扫码位置'),
        sa.Column('device_info', sa.JSON(), nullable=True, comment='设备信息'),
        sa.Column('notes', sa.Text(), nullable=True, comment='备注'),
        sa.Column('measurement_data', sa.JSON(), nullable=True, comment='测量数据'),
        sa.Column('registration_data', sa.JSON(), nullable=True, comment='登记数据'),
        sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
        sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新时间'),
        sa.ForeignKeyConstraint(['factory_id'], ['factories.id'], ),
        sa.ForeignKeyConstraint(['order_craft_route_id'], ['order_craft_routes.id'], ),
        sa.ForeignKeyConstraint(['worker_user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes for order_craft_route_instances
    op.create_index(op.f('ix_order_craft_route_instances_completion_granularity'), 'order_craft_route_instances', ['completion_granularity'], unique=False)
    op.create_index(op.f('ix_order_craft_route_instances_factory_id'), 'order_craft_route_instances', ['factory_id'], unique=False)
    op.create_index(op.f('ix_order_craft_route_instances_order_bundle_no'), 'order_craft_route_instances', ['order_bundle_no'], unique=False)
    op.create_index(op.f('ix_order_craft_route_instances_order_craft_route_id'), 'order_craft_route_instances', ['order_craft_route_id'], unique=False)
    op.create_index(op.f('ix_order_craft_route_instances_order_no'), 'order_craft_route_instances', ['order_no'], unique=False)
    op.create_index(op.f('ix_order_craft_route_instances_order_part_no'), 'order_craft_route_instances', ['order_part_no'], unique=False)
    op.create_index(op.f('ix_order_craft_route_instances_status'), 'order_craft_route_instances', ['status'], unique=False)
    op.create_index(op.f('ix_order_craft_route_instances_worker_user_id'), 'order_craft_route_instances', ['worker_user_id'], unique=False)
    
    # Add current_craft_route_code column to order_bundles
    op.add_column('order_bundles', sa.Column('current_craft_route_code', sa.String(length=50), nullable=True, comment='当前工艺路线代码'))
    op.create_index(op.f('ix_order_bundles_current_craft_route_code'), 'order_bundles', ['current_craft_route_code'], unique=False)
    
    # Add current_craft_route_code column to order_parts
    op.add_column('order_parts', sa.Column('current_craft_route_code', sa.String(length=50), nullable=True, comment='当前工艺路线代码'))
    op.create_index(op.f('ix_order_parts_current_craft_route_code'), 'order_parts', ['current_craft_route_code'], unique=False)
    
    # Add current_craft_route_code column to order_lines
    op.add_column('order_lines', sa.Column('current_craft_route_code', sa.String(length=50), nullable=True, comment='当前工艺路线代码'))
    op.create_index(op.f('ix_order_lines_current_craft_route_code'), 'order_lines', ['current_craft_route_code'], unique=False)


def downgrade() -> None:
    # Remove current_craft_route_code columns and indexes
    op.drop_index(op.f('ix_order_lines_current_craft_route_code'), table_name='order_lines')
    op.drop_column('order_lines', 'current_craft_route_code')
    
    op.drop_index(op.f('ix_order_parts_current_craft_route_code'), table_name='order_parts')
    op.drop_column('order_parts', 'current_craft_route_code')
    
    op.drop_index(op.f('ix_order_bundles_current_craft_route_code'), table_name='order_bundles')
    op.drop_column('order_bundles', 'current_craft_route_code')
    
    # Drop order_craft_route_instances table
    op.drop_index(op.f('ix_order_craft_route_instances_worker_user_id'), table_name='order_craft_route_instances')
    op.drop_index(op.f('ix_order_craft_route_instances_status'), table_name='order_craft_route_instances')
    op.drop_index(op.f('ix_order_craft_route_instances_order_part_no'), table_name='order_craft_route_instances')
    op.drop_index(op.f('ix_order_craft_route_instances_order_no'), table_name='order_craft_route_instances')
    op.drop_index(op.f('ix_order_craft_route_instances_order_craft_route_id'), table_name='order_craft_route_instances')
    op.drop_index(op.f('ix_order_craft_route_instances_order_bundle_no'), table_name='order_craft_route_instances')
    op.drop_index(op.f('ix_order_craft_route_instances_factory_id'), table_name='order_craft_route_instances')
    op.drop_index(op.f('ix_order_craft_route_instances_completion_granularity'), table_name='order_craft_route_instances')
    op.drop_table('order_craft_route_instances')