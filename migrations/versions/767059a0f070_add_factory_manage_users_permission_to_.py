"""Add factory manage users permission to test admin role

Revision ID: 767059a0f070
Revises: fa9a4723ef3c
Create Date: 2025-06-19 18:54:01.881382

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '767059a0f070'
down_revision = 'fa9a4723ef3c'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create tables metadata for raw SQL operations
    connection = op.get_bind()
    
    # Add factory.manage_users permission to factory_management parent
    connection.execute(sa.text("""
        INSERT INTO permissions (code, name, parent_id) 
        SELECT 'factory.manage_users', '管理工厂用户', p.id 
        FROM permissions p WHERE p.code = 'factory_management'
    """))
    
    # Assign this permission to Test Admin Role
    connection.execute(sa.text("""
        INSERT INTO role_permissions (role_id, permission_id, created_at, updated_at)
        SELECT r.id, p.id, NOW(), NOW()
        FROM roles r, permissions p
        WHERE r.name = 'Test Admin Role' AND p.code = 'factory.manage_users'
    """))


def downgrade() -> None:
    # Remove the permission
    connection = op.get_bind()
    
    # Remove role permission first
    connection.execute(sa.text("""
        DELETE FROM role_permissions 
        WHERE permission_id IN (SELECT id FROM permissions WHERE code = 'factory.manage_users')
    """))
    
    # Remove permission
    connection.execute(sa.text("""
        DELETE FROM permissions WHERE code = 'factory.manage_users'
    """))