"""Add orders and order_lines tables

Revision ID: a363679100cb
Revises: 94d0d98c46c3
Create Date: 2025-06-19 22:17:32.658922

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'a363679100cb'
down_revision = '94d0d98c46c3'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('orders',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('style_no', sa.String(length=100), nullable=False, comment='款号'),
    sa.Column('external_style_no', sa.String(length=100), nullable=True, comment='外部款号'),
    sa.Column('order_no', sa.String(length=100), nullable=False, comment='订单号'),
    sa.Column('external_order_no', sa.String(length=100), nullable=True, comment='外部订单号'),
    sa.Column('external_order_no2', sa.String(length=100), nullable=True, comment='外部订单号2'),
    sa.Column('total_amount', sa.Integer(), nullable=False, comment='总数量'),
    sa.Column('cost', sa.Numeric(precision=10, scale=2), nullable=True, comment='成本'),
    sa.Column('price', sa.Numeric(precision=10, scale=2), nullable=True, comment='价格'),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('expect_finished_at', sa.DateTime(), nullable=True, comment='预期完成时间'),
    sa.Column('finished_at', sa.DateTime(), nullable=True, comment='实际完成时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('owner_user_id', sa.Integer(), nullable=True, comment='负责人用户ID'),
    sa.Column('status', sa.Enum('PENDING', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED', 'ON_HOLD', 'DELAYED', name='orderstatus'), nullable=False),
    sa.Column('current_craft', sa.String(length=50), nullable=True, comment='当前工艺'),
    sa.Column('current_craft_route', sa.Integer(), nullable=True, comment='当前工艺路线'),
    sa.Column('description', sa.Text(), nullable=True, comment='订单描述'),
    sa.Column('notes', sa.Text(), nullable=True, comment='备注'),
    sa.ForeignKeyConstraint(['current_craft'], ['crafts.code'], ),
    sa.ForeignKeyConstraint(['current_craft_route'], ['craft_routes.id'], ),
    sa.ForeignKeyConstraint(['owner_user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_orders_current_craft'), 'orders', ['current_craft'], unique=False)
    op.create_index(op.f('ix_orders_current_craft_route'), 'orders', ['current_craft_route'], unique=False)
    op.create_index(op.f('ix_orders_external_order_no'), 'orders', ['external_order_no'], unique=False)
    op.create_index(op.f('ix_orders_external_order_no2'), 'orders', ['external_order_no2'], unique=False)
    op.create_index(op.f('ix_orders_external_style_no'), 'orders', ['external_style_no'], unique=False)
    op.create_index(op.f('ix_orders_order_no'), 'orders', ['order_no'], unique=True)
    op.create_index(op.f('ix_orders_owner_user_id'), 'orders', ['owner_user_id'], unique=False)
    op.create_index(op.f('ix_orders_status'), 'orders', ['status'], unique=False)
    op.create_index(op.f('ix_orders_style_no'), 'orders', ['style_no'], unique=False)
    op.create_table('order_lines',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('order_no', sa.String(length=100), nullable=False, comment='订单号'),
    sa.Column('order_line_no', sa.String(length=150), nullable=False, comment='订单行号 {order_no}_{size}'),
    sa.Column('size', sa.String(length=20), nullable=False, comment='尺码'),
    sa.Column('amount', sa.Integer(), nullable=False, comment='数量'),
    sa.Column('produced_amount', sa.Integer(), nullable=False, comment='已生产数量'),
    sa.Column('completed_amount', sa.Integer(), nullable=False, comment='已完成数量'),
    sa.Column('notes', sa.String(length=500), nullable=True, comment='备注'),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['order_no'], ['orders.order_no'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_order_lines_order_line_no'), 'order_lines', ['order_line_no'], unique=True)
    op.create_index(op.f('ix_order_lines_order_no'), 'order_lines', ['order_no'], unique=False)
    op.create_index(op.f('ix_order_lines_size'), 'order_lines', ['size'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_order_lines_size'), table_name='order_lines')
    op.drop_index(op.f('ix_order_lines_order_no'), table_name='order_lines')
    op.drop_index(op.f('ix_order_lines_order_line_no'), table_name='order_lines')
    op.drop_table('order_lines')
    op.drop_index(op.f('ix_orders_style_no'), table_name='orders')
    op.drop_index(op.f('ix_orders_status'), table_name='orders')
    op.drop_index(op.f('ix_orders_owner_user_id'), table_name='orders')
    op.drop_index(op.f('ix_orders_order_no'), table_name='orders')
    op.drop_index(op.f('ix_orders_external_style_no'), table_name='orders')
    op.drop_index(op.f('ix_orders_external_order_no2'), table_name='orders')
    op.drop_index(op.f('ix_orders_external_order_no'), table_name='orders')
    op.drop_index(op.f('ix_orders_current_craft_route'), table_name='orders')
    op.drop_index(op.f('ix_orders_current_craft'), table_name='orders')
    op.drop_table('orders')
    # ### end Alembic commands ###