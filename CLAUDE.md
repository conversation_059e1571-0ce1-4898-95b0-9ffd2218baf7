# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Architecture Overview

This is a **Clean Architecture** FastAPI application with Domain-Driven Design (DDD) principles. The codebase is organized into four distinct layers:

- **Domain Layer** (`src/domain/`): Core business entities and value objects
- **Application Layer** (`src/application/`): Use cases, DTOs, and repository interfaces  
- **Infrastructure Layer** (`src/infrastructure/`): Database, external services, DI container
- **Presentation Layer** (`src/presentation/`): API endpoints and middleware

## Key Architectural Patterns

### Dependency Injection
- Uses `dependency-injector` library with a centralized container in `src/infrastructure/containers.py`
- All dependencies are wired through the container and injected via `@inject` decorator
- New services must be registered in the container to be available

### Repository Pattern
- Abstract interfaces defined in `src/application/interfaces/`
- Concrete implementations in `src/infrastructure/repositories/`
- All database operations are async with proper session management

### Use Case Pattern
- Business logic encapsulated in use cases (`src/application/use_cases/`)
- Use cases depend on repository interfaces, not implementations
- Handle authentication, validation, and business rules

## Development Commands

### Database Operations
```bash
# Create new migration
alembic revision --autogenerate -m "Description"

# Apply migrations
alembic upgrade head

# Rollback migration  
alembic downgrade -1
```

### Running the Application
```bash
# Activate virtual environment first
source venv/bin/activate  # macOS/Linux
# venv\Scripts\activate   # Windows

# Development server
python main.py

# Production server
uvicorn main:app --host 0.0.0.0 --port 8000

# Start Celery worker
celery -A src.infrastructure.external_services.celery_app worker --loglevel=info
# Or use the helper script
python scripts/start_celery.py

# Start Celery beat scheduler
celery -A src.infrastructure.external_services.celery_app beat --loglevel=info
```

### Testing
```bash
# Run all tests
pytest

# Run with coverage (HTML report in htmlcov/)
pytest --cov=src --cov-report=html

# Run specific test types
pytest -m unit          # Unit tests only
pytest -m integration   # Integration tests only
pytest -m "not slow"    # Skip slow tests

# Run single test file
pytest tests/unit/test_user_use_cases.py

# Run single test function
pytest tests/unit/test_user_use_cases.py::test_create_user

# Run tests in verbose mode with output
pytest -v -s
```

### Code Quality
```bash
# Format code
black .
isort .

# Lint code
flake8 .
mypy .
```

## Configuration Management

Configuration is handled through Pydantic settings in `config/settings.py`:
- Environment variables automatically loaded from `.env` file
- Modular settings classes for different concerns (Database, Redis, JWT, etc.)
- Type-safe configuration with validation

## Authentication System

JWT-based authentication with:
- OAuth2 password flow (`/api/v1/auth/token`)
- Current user dependency injection (`get_current_active_user`)
- Bcrypt password hashing
- Role-based access with `is_active` and `is_superuser` flags

## External Services

### Redis
- Async Redis client with lazy connection initialization
- Used for caching and Celery backend
- JSON serialization/deserialization built-in

### Celery
- Background task processing with Redis broker
- Task definitions in `src/infrastructure/external_services/tasks.py`
- Includes QR code generation, email notifications, and file processing tasks

## Database Layer

- **SQLAlchemy 2.0** with async operations
- **MySQL** with `aiomysql` driver
- Connection pooling and proper session cleanup
- Alembic migrations with async support

## Development Guidelines

### Layer Dependencies
- Domain layer should not depend on any other layer
- Application layer can only depend on domain layer
- Infrastructure layer can depend on application and domain layers
- Presentation layer can depend on all other layers

### Adding New Features
1. Define domain entities in `src/domain/entities/`
2. Create repository interface in `src/application/interfaces/`
3. Implement repository in `src/infrastructure/repositories/`
4. Create use cases in `src/application/use_cases/`
5. Add DTOs in `src/application/dto/`
6. Implement API endpoints in `src/presentation/api/v1/`
7. Register dependencies in `src/infrastructure/containers.py`
8. Create and run database migrations if needed

### API Design
- All endpoints use Pydantic models for request/response validation
- Never expose domain entities directly - always use DTOs
- Proper HTTP status codes and error handling
- API documentation auto-generated at `/docs`

## Environment Setup

```bash
# 1. Activate virtual environment
source venv/bin/activate  # macOS/Linux
# venv\Scripts\activate   # Windows

# 2. Install dependencies
pip install -r requirements.txt

# 3. Set up environment variables
cp .env.example .env
# Edit .env with your database/Redis URLs and secret keys

# 4. Set up database
alembic revision --autogenerate -m "Initial migration"
alembic upgrade head

# 5. Start the application
python main.py
```

## Logging Configuration

Structured logging is configured in `src/infrastructure/logging/logger.py`:
- JSON format for production (`LOG_FORMAT=json`)
- Human-readable format for development
- Use `get_logger(__name__)` or inherit from `LoggerMixin`
- Log levels controlled by `LOG_LEVEL` environment variable

## Virtual Environment

- The virtual environment is located at `.venv`
- Always activate the virtual environment before running any project commands

API documentation available at http://localhost:8000/docs