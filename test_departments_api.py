#!/usr/bin/env python3
"""
Simple test script to verify the Department API functionality.
This script tests the basic CRUD operations for departments.
"""

import asyncio
import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.application.use_cases.department_use_cases import DepartmentUseCases
from src.infrastructure.repositories.department_repository import DepartmentRepository
from src.application.dto.department_dto import DepartmentCreateDTO, DepartmentUpdateDTO
from src.infrastructure.database.database import Database
from config import settings


async def test_department_use_cases():
    """Test department use cases functionality."""
    print("Testing Department Use Cases...")
    
    # Initialize database
    db = Database(db_url=settings.database.url, echo=False)
    
    # Create repository and use cases
    department_repository = DepartmentRepository(db.get_session)
    department_use_cases = DepartmentUseCases(department_repository)
    
    try:
        # Test 1: Create a department
        print("\n1. Testing department creation...")
        create_dto = DepartmentCreateDTO(
            name="Test Department",
            code="TEST001",
            manager_name="<PERSON>",
            phone="************",
            email="<EMAIL>",
            location="Building A, Floor 2",
            description="Test department for API validation",
            is_active=True,
            operator_id=1  # Assuming user ID 1 exists
        )
        
        created_department = await department_use_cases.create_department(create_dto, factory_id=1)
        print(f"✓ Created department: {created_department.name} (ID: {created_department.id})")
        
        # Test 2: Get department by ID
        print("\n2. Testing get department by ID...")
        retrieved_department = await department_use_cases.get_department_by_id(created_department.id)
        if retrieved_department:
            print(f"✓ Retrieved department: {retrieved_department.name}")
        else:
            print("✗ Failed to retrieve department")
            return
        
        # Test 3: Get departments by factory
        print("\n3. Testing get departments by factory...")
        departments_list = await department_use_cases.get_departments_by_factory(factory_id=1)
        print(f"✓ Found {len(departments_list.departments)} departments in factory")
        
        # Test 4: Update department
        print("\n4. Testing department update...")
        update_dto = DepartmentUpdateDTO(
            name="Updated Test Department",
            description="Updated description for test department",
            operator_id=1
        )
        
        updated_department = await department_use_cases.update_department(
            created_department.id, update_dto
        )
        if updated_department:
            print(f"✓ Updated department: {updated_department.name}")
        else:
            print("✗ Failed to update department")
            return
        
        # Test 5: Search departments
        print("\n5. Testing department search...")
        from src.application.dto.department_dto import DepartmentSearchDTO
        search_dto = DepartmentSearchDTO(
            search_term="Updated",
            is_active=True
        )
        
        search_results = await department_use_cases.search_departments(
            factory_id=1, search_criteria=search_dto
        )
        print(f"✓ Search found {len(search_results.departments)} departments")
        
        # Test 6: Delete department
        print("\n6. Testing department deletion...")
        delete_result = await department_use_cases.delete_department(
            created_department.id, operator_id=1
        )
        if delete_result.success:
            print(f"✓ Deleted department: {delete_result.message}")
        else:
            print(f"✗ Failed to delete department: {delete_result.message}")
        
        print("\n✅ All department use case tests passed!")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Clean up database connection
        await db.close()


async def test_department_api_structure():
    """Test that the department API structure is correct."""
    print("\nTesting Department API Structure...")
    
    try:
        # Test imports
        from src.presentation.api.v1.departments import router
        from src.application.dto.department_dto import (
            DepartmentCreateDTO, DepartmentUpdateDTO, DepartmentResponseDTO,
            DepartmentListDTO, DepartmentSearchDTO, DepartmentOperationResultDTO
        )
        
        print("✓ All department API imports successful")
        
        # Test router configuration
        assert router.prefix == "/departments"
        assert "departments" in router.tags
        print("✓ Router configuration is correct")
        
        # Test DTO structure
        create_dto = DepartmentCreateDTO(
            name="Test",
            code="TEST",
            operator_id=1
        )
        assert create_dto.name == "Test"
        assert create_dto.code == "TEST"
        assert create_dto.operator_id == 1
        print("✓ DTO structure is correct")
        
        print("✅ All API structure tests passed!")
        
    except Exception as e:
        print(f"❌ API structure test failed: {str(e)}")
        import traceback
        traceback.print_exc()


async def main():
    """Run all tests."""
    print("🚀 Starting Department API Tests")
    print("=" * 50)
    
    # Test API structure first (doesn't require database)
    await test_department_api_structure()
    
    # Test use cases (requires database)
    try:
        await test_department_use_cases()
    except Exception as e:
        print(f"\n⚠️  Database tests skipped due to connection issues: {str(e)}")
        print("This is normal if the database is not set up or accessible.")
    
    print("\n" + "=" * 50)
    print("🏁 Department API Tests Complete")


if __name__ == "__main__":
    asyncio.run(main())
