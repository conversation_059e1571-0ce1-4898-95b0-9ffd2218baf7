# Database Configuration
DATABASE_URL=mysql+aiomysql://user:password@localhost:3306/production_ticket
DATABASE_ECHO=false

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# JWT Configuration
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# API Configuration
API_V1_PREFIX=/api/v1
PROJECT_NAME=Production Ticket System
VERSION=1.0.0
DEBUG=true

# Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/2

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json