#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to add garment manufacturing skills to the database.
Run this script when the database is available and configured.

Usage:
    python scripts/add_garment_skills.py
"""

import asyncio
import sys
from pathlib import Path

# Add the src directory to the Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from infrastructure.database.database import get_async_session, async_engine
from domain.entities.skill import Skill
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession


async def add_garment_manufacturing_skills():
    """Add garment manufacturing skills to the database."""
    
    # Skill data organized by category
    skills_data = [
        # 裁剪工技能 (Cutting Operator)
        ('CUT_001', 'Straight Knife Cutting Machine', '直刀裁剪机操作技能', True, 'cutting'),
        ('CUT_002', 'Electric Scissors', '电动剪刀操作技能', True, 'cutting'),
        ('CUT_003', 'Laser Cutting Machine', '激光裁剪机操作技能', True, 'cutting'),
        ('CUT_004', 'Pattern Layout Skills', '排料技能', True, 'cutting'),
        ('CUT_005', 'Fabric Inspection', '面料检验技能', True, 'cutting'),
        
        # 车缝工技能 (Sewing Operator)
        ('SEW_001', 'Lockstitch Sewing Machine', '平缝机操作技能', True, 'sewing'),
        ('SEW_002', 'Overlock Machine', '包缝机/拷边机操作技能', True, 'sewing'),
        ('SEW_003', 'Double Needle Machine', '双针机操作技能', True, 'sewing'),
        ('SEW_004', 'Flatlock Machine', '绷缝机操作技能', True, 'sewing'),
        ('SEW_005', 'Zigzag Stitching Machine', '之字缝机操作技能', True, 'sewing'),
        
        # 专机操作工技能 (Special Machine Operator)
        ('SPE_001', 'Buttonhole Machine', '锁眼机操作技能', True, 'machine_operation'),
        ('SPE_002', 'Button Sewing Machine', '钉扣机操作技能', True, 'machine_operation'),
        ('SPE_003', 'Blind Hemming Machine', '暗缝机操作技能', True, 'machine_operation'),
        ('SPE_004', 'Bartacking Machine', '打枣机操作技能', True, 'machine_operation'),
        ('SPE_005', 'Embroidery Machine', '刺绣机操作技能', True, 'machine_operation'),
        
        # 整烫工技能 (Pressing Operator)
        ('PRE_001', 'Steam Iron', '蒸汽熨斗操作技能', True, 'pressing'),
        ('PRE_002', 'Press Machine', '压烫机操作技能', True, 'pressing'),
        ('PRE_003', 'Steam Finishing Machine', '蒸汽整理机操作技能', True, 'pressing'),
        ('PRE_004', 'Tunnel Finisher', '隧道式整烫机操作技能', True, 'pressing'),
        ('PRE_005', 'Shape Setting Machine', '定型机操作技能', True, 'pressing'),
        
        # 质检工技能 (Quality Inspector)
        ('QC_001', 'Measuring Tools', '测量工具使用技能', True, 'quality_control'),
        ('QC_002', 'Seam Quality Assessment', '缝制质量评估技能', True, 'quality_control'),
        ('QC_003', 'Fabric Defect Detection', '面料缺陷检测技能', True, 'quality_control'),
        ('QC_004', 'Size Grading Knowledge', '尺码分级知识', True, 'quality_control'),
        ('QC_005', 'Final Inspection Skills', '成品检验技能', True, 'quality_control'),
    ]
    
    print("Adding garment manufacturing skills to the database...")
    
    try:
        async with async_engine.begin() as conn:
            added_count = 0
            skipped_count = 0
            
            for skill_code, skill_name, skill_description, is_active, category in skills_data:
                try:
                    # Check if skill already exists
                    result = await conn.execute(
                        text("SELECT COUNT(*) FROM skills WHERE code = :code"),
                        {"code": skill_code}
                    )
                    
                    if result.scalar() > 0:
                        print(f"  Skill {skill_code} already exists, skipping...")
                        skipped_count += 1
                        continue
                    
                    # Insert the skill
                    await conn.execute(
                        text("""
                            INSERT INTO skills (code, name, description, is_active, category)
                            VALUES (:code, :name, :description, :is_active, :category)
                        """),
                        {
                            'code': skill_code,
                            'name': skill_name,
                            'description': skill_description,
                            'is_active': is_active,
                            'category': category
                        }
                    )
                    
                    print(f"  Added skill: {skill_code} - {skill_name}")
                    added_count += 1
                    
                except Exception as e:
                    print(f"  Error adding skill {skill_code}: {e}")
                    continue
            
            print(f"\nCompleted! Added {added_count} skills, skipped {skipped_count} existing skills.")
            
    except Exception as e:
        print(f"Database connection error: {e}")
        print("Make sure the database is running and properly configured in your .env file.")
        return False
    
    return True


async def main():
    """Main function to run the skill addition script."""
    print("=== Garment Manufacturing Skills Addition Script ===")
    print()
    
    success = await add_garment_manufacturing_skills()
    
    if success:
        print("\n✅ Skills have been successfully added to the database!")
        print("\nSkill Categories Added:")
        print("  - cutting (裁剪工技能)")
        print("  - sewing (车缝工技能)")
        print("  - machine_operation (专机操作工技能)")
        print("  - pressing (整烫工技能)")
        print("  - quality_control (质检工技能)")
    else:
        print("\n❌ Failed to add skills. Please check the error messages above.")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())