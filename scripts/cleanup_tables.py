#!/usr/bin/env python3
"""
Script to clean up tables and reset migration state.
This script will:
1. Truncate craft_routes, crafts, and skills tables
2. Remove the extra columns from craft_routes
3. Reset migration state to e88016ba7aef
"""

import asyncio
import sys
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.infrastructure.database.database import Database
from config.settings import settings
from sqlalchemy import text


async def cleanup_database():
    """Clean up the database tables and schema."""
    print("=== Database Cleanup Script ===")
    print()
    
    # Initialize database
    db = Database(settings.database.url, settings.database.echo)
    
    try:
        async with db.engine.begin() as conn:
            print("1. Disabling foreign key checks...")
            await conn.execute(text("SET FOREIGN_KEY_CHECKS = 0"))
            
            print("2. Truncating tables...")
            
            # Truncate tables in correct order (respecting foreign keys)
            tables_to_truncate = [
                'craft_routes',
                'crafts', 
                'skills'
            ]
            
            for table in tables_to_truncate:
                try:
                    await conn.execute(text(f"TRUNCATE TABLE {table}"))
                    print(f"   ✅ Truncated {table}")
                except Exception as e:
                    print(f"   ⚠️  Could not truncate {table}: {e}")
            
            print("3. Cleaning up craft_routes schema...")
            
            # Drop unique constraint if it exists
            try:
                await conn.execute(text("ALTER TABLE craft_routes DROP INDEX uq_craft_route_craft_code_code"))
                print("   ✅ Dropped unique constraint")
            except Exception as e:
                print(f"   ⚠️  Could not drop constraint: {e}")
            
            # Drop index if it exists
            try:
                await conn.execute(text("ALTER TABLE craft_routes DROP INDEX ix_craft_routes_code"))
                print("   ✅ Dropped code index")
            except Exception as e:
                print(f"   ⚠️  Could not drop index: {e}")
            
            # Drop columns if they exist
            try:
                await conn.execute(text("ALTER TABLE craft_routes DROP COLUMN code"))
                print("   ✅ Dropped code column")
            except Exception as e:
                print(f"   ⚠️  Could not drop code column: {e}")
                
            try:
                await conn.execute(text("ALTER TABLE craft_routes DROP COLUMN name"))
                print("   ✅ Dropped name column")
            except Exception as e:
                print(f"   ⚠️  Could not drop name column: {e}")
            
            print("4. Re-enabling foreign key checks...")
            await conn.execute(text("SET FOREIGN_KEY_CHECKS = 1"))
            
            print("5. Updating migration state...")
            # Set migration state back to e88016ba7aef
            await conn.execute(text("""
                UPDATE alembic_version SET version_num = 'e88016ba7aef'
            """))
            print("   ✅ Reset migration state to e88016ba7aef")
            
        print("\n✅ Database cleanup completed successfully!")
        return True
        
    except Exception as e:
        print(f"\n❌ Database cleanup failed: {e}")
        return False
    finally:
        await db.close()


async def main():
    """Main function."""
    success = await cleanup_database()
    
    if success:
        print("\n🎉 Ready to run new migrations with Chinese names!")
        print("Next steps:")
        print("1. Run the updated migration script")
        print("2. Apply the new skills with Chinese names")
    else:
        print("\n💥 Cleanup failed. Please check the errors above.")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())