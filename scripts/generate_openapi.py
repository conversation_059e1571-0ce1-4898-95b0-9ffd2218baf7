#!/usr/bin/env python3
"""
Script to generate OpenAPI JSON documentation for the FastAPI application.
Usage: python scripts/generate_openapi.py
"""

import sys
import json
import os
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Set environment variables for configuration
os.environ.setdefault("DATABASE_URL", "mysql+aiomysql://user:password@localhost:3306/production_ticket")
os.environ.setdefault("REDIS_URL", "redis://localhost:6379/0")
os.environ.setdefault("SECRET_KEY", "your-secret-key-for-openapi-generation")
os.environ.setdefault("DEBUG", "false")
os.environ.setdefault("DATABASE_ECHO", "false")
os.environ.setdefault("ALGORITHM", "HS256")
os.environ.setdefault("ACCESS_TOKEN_EXPIRE_MINUTES", "30")
os.environ.setdefault("API_V1_PREFIX", "/api/v1")
os.environ.setdefault("PROJECT_NAME", "Production Ticket System")
os.environ.setdefault("VERSION", "1.0.0")
os.environ.setdefault("CELERY_BROKER_URL", "redis://localhost:6379/1")
os.environ.setdefault("CELERY_RESULT_BACKEND", "redis://localhost:6379/2")
os.environ.setdefault("LOG_LEVEL", "INFO")
os.environ.setdefault("LOG_FORMAT", "json")

try:
    from main import app
    
    def generate_openapi_json():
        """Generate OpenAPI JSON schema."""
        print("Generating OpenAPI JSON documentation...")
        
        # Get OpenAPI schema
        openapi_schema = app.openapi()
        
        # Output file path
        output_file = project_root / "openapi.json"
        
        # Write to file with pretty formatting
        with open(output_file, "w", encoding="utf-8") as f:
            json.dump(openapi_schema, f, indent=2, ensure_ascii=False)
        
        print(f"✅ OpenAPI JSON generated successfully: {output_file}")
        print(f"📄 API Title: {openapi_schema.get('info', {}).get('title', 'Unknown')}")
        print(f"🔢 Version: {openapi_schema.get('info', {}).get('version', 'Unknown')}")
        
        # Count endpoints
        paths = openapi_schema.get('paths', {})
        endpoint_count = sum(len(methods) for methods in paths.values())
        print(f"🛠️  Total endpoints: {endpoint_count}")
        
        # List main tags/groups
        tags = set()
        for path_data in paths.values():
            for method_data in path_data.values():
                if isinstance(method_data, dict) and 'tags' in method_data:
                    tags.update(method_data['tags'])
        
        if tags:
            print(f"🏷️  API groups: {', '.join(sorted(tags))}")
        
        return output_file

    def generate_openapi_yaml():
        """Generate OpenAPI YAML schema (optional)."""
        try:
            import yaml
            
            print("Generating OpenAPI YAML documentation...")
            
            # Get OpenAPI schema
            openapi_schema = app.openapi()
            
            # Output file path
            output_file = project_root / "openapi.yaml"
            
            # Write to YAML file
            with open(output_file, "w", encoding="utf-8") as f:
                yaml.dump(openapi_schema, f, default_flow_style=False, allow_unicode=True)
            
            print(f"✅ OpenAPI YAML generated successfully: {output_file}")
            return output_file
            
        except ImportError:
            print("⚠️  PyYAML not installed, skipping YAML generation")
            print("💡 Install with: pip install PyYAML")
            return None

    def validate_openapi_schema():
        """Basic validation of the generated schema."""
        try:
            openapi_schema = app.openapi()
            
            # Check required fields
            required_fields = ['openapi', 'info', 'paths']
            missing_fields = [field for field in required_fields if field not in openapi_schema]
            
            if missing_fields:
                print(f"❌ Missing required fields: {missing_fields}")
                return False
            
            # Check info section
            info = openapi_schema.get('info', {})
            if not info.get('title') or not info.get('version'):
                print("❌ Missing title or version in info section")
                return False
            
            print("✅ OpenAPI schema validation passed")
            return True
            
        except Exception as e:
            print(f"❌ Schema validation failed: {e}")
            return False

    def print_api_summary():
        """Print a summary of the API endpoints."""
        openapi_schema = app.openapi()
        paths = openapi_schema.get('paths', {})
        
        print("\n📋 API Endpoints Summary:")
        print("=" * 50)
        
        for path, methods in paths.items():
            for method, details in methods.items():
                if isinstance(details, dict):
                    summary = details.get('summary', 'No summary')
                    tags = ', '.join(details.get('tags', ['untagged']))
                    print(f"{method.upper():6} {path:30} [{tags}]")
                    print(f"       {summary}")

    if __name__ == "__main__":
        print("🚀 FastAPI OpenAPI Documentation Generator")
        print("=" * 50)
        
        # Validate schema first
        if not validate_openapi_schema():
            sys.exit(1)
        
        # Generate JSON
        json_file = generate_openapi_json()
        
        # Generate YAML (optional)
        yaml_file = generate_openapi_yaml()
        
        # Print API summary
        print_api_summary()
        
        print("\n" + "=" * 50)
        print("🎉 Documentation generation completed!")
        print(f"📁 Files generated in: {project_root}")
        print("💡 You can now use these files with:")
        print("   • Swagger UI: https://editor.swagger.io/")
        print("   • Redoc: https://redocly.github.io/redoc/")
        print("   • Postman: Import the JSON file")
        print("   • OpenAPI tools and generators")

except ImportError as e:
    print(f"❌ Import error: {e}")
    print("💡 Make sure you're running this script from the project root")
    print("💡 And that all dependencies are installed: pip install -r requirements.txt")
    sys.exit(1)

except Exception as e:
    print(f"❌ Error generating OpenAPI documentation: {e}")
    sys.exit(1)