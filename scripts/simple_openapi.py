#!/usr/bin/env python3
"""
Simple script to generate OpenAPI JSON documentation for the FastAPI application.
Usage: python scripts/simple_openapi.py
"""

import sys
import json
import os
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Set minimal environment variables
os.environ.setdefault("DATABASE_URL", "sqlite:///./temp.db")
os.environ.setdefault("SECRET_KEY", "temp-key-for-docs")

try:
    from fastapi import FastAPI
    from fastapi.middleware.cors import CORSMiddleware
    
    # Create a minimal FastAPI app with just the API structure
    app = FastAPI(
        title="Production Ticket System",
        version="1.0.0",
        debug=False
    )
    
    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Import and include routers directly
    from src.presentation.api.v1.auth import router as auth_router
    from src.presentation.api.v1.auth_phone import router as auth_phone_router
    from src.presentation.api.v1.users import router as users_router
    from src.presentation.api.v1.factory_management import router as factory_management_router
    from src.presentation.api.v1.session import router as session_router
    
    app.include_router(auth_router, prefix="/api/v1")
    app.include_router(auth_phone_router, prefix="/api/v1")
    app.include_router(users_router, prefix="/api/v1")
    app.include_router(factory_management_router, prefix="/api/v1")
    app.include_router(session_router, prefix="/api/v1")
    
    # Add basic endpoints
    @app.get("/")
    async def root():
        return {"message": "Welcome to Production Ticket System API v1.0.0"}
    
    @app.get("/health")
    async def health_check():
        return {"status": "healthy"}
    
    def generate_openapi_json():
        """Generate OpenAPI JSON schema."""
        print("Generating OpenAPI JSON documentation...")
        
        # Get OpenAPI schema
        openapi_schema = app.openapi()
        
        # Output file path
        output_file = project_root / "openapi.json"
        
        # Write to file with pretty formatting
        with open(output_file, "w", encoding="utf-8") as f:
            json.dump(openapi_schema, f, indent=2, ensure_ascii=False)
        
        print(f"✅ OpenAPI JSON generated successfully: {output_file}")
        print(f"📄 API Title: {openapi_schema.get('info', {}).get('title', 'Unknown')}")
        print(f"🔢 Version: {openapi_schema.get('info', {}).get('version', 'Unknown')}")
        
        # Count endpoints
        paths = openapi_schema.get('paths', {})
        endpoint_count = sum(len(methods) for methods in paths.values())
        print(f"🛠️  Total endpoints: {endpoint_count}")
        
        # List main tags/groups
        tags = set()
        for path_data in paths.values():
            for method_data in path_data.values():
                if isinstance(method_data, dict) and 'tags' in method_data:
                    tags.update(method_data['tags'])
        
        if tags:
            print(f"🏷️  API groups: {', '.join(sorted(tags))}")
        
        return output_file

    def print_api_summary():
        """Print a summary of the API endpoints."""
        openapi_schema = app.openapi()
        paths = openapi_schema.get('paths', {})
        
        print("\n📋 API Endpoints Summary:")
        print("=" * 50)
        
        for path, methods in paths.items():
            for method, details in methods.items():
                if isinstance(details, dict):
                    summary = details.get('summary', 'No summary')
                    tags = ', '.join(details.get('tags', ['untagged']))
                    print(f"{method.upper():6} {path:30} [{tags}]")
                    print(f"       {summary}")

    if __name__ == "__main__":
        print("🚀 FastAPI OpenAPI Documentation Generator (Simplified)")
        print("=" * 50)
        
        # Generate JSON
        json_file = generate_openapi_json()
        
        # Print API summary
        print_api_summary()
        
        print("\n" + "=" * 50)
        print("🎉 Documentation generation completed!")
        print(f"📁 Files generated in: {project_root}")
        print("💡 You can now use these files with:")
        print("   • Swagger UI: https://editor.swagger.io/")
        print("   • Redoc: https://redocly.github.io/redoc/")
        print("   • Postman: Import the JSON file")
        print("   • OpenAPI tools and generators")

except ImportError as e:
    print(f"❌ Import error: {e}")
    print("💡 This script requires minimal dependencies:")
    print("   pip install fastapi")
    sys.exit(1)

except Exception as e:
    print(f"❌ Error generating OpenAPI documentation: {e}")
    import traceback
    print("Full traceback:")
    traceback.print_exc()
    sys.exit(1)