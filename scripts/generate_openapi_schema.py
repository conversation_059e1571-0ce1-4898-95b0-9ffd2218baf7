#!/usr/bin/env python3
"""
Script to generate OpenAPI schema for the Production Ticket System API.
"""

import json
import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from main import app


def generate_openapi_schema():
    """Generate and save the OpenAPI schema to openapi.json."""
    try:
        # Get the OpenAPI schema
        openapi_schema = app.openapi()
        
        # Write to openapi.json file
        output_file = project_root / "openapi.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(openapi_schema, f, indent=2, ensure_ascii=False)
        
        print(f"✅ OpenAPI schema exported to {output_file}")
        print(f"📄 Schema contains {len(openapi_schema.get('paths', {}))} endpoints")
        
        return True
        
    except Exception as e:
        print(f"❌ Error generating OpenAPI schema: {e}")
        return False


if __name__ == "__main__":
    success = generate_openapi_schema()
    sys.exit(0 if success else 1)
