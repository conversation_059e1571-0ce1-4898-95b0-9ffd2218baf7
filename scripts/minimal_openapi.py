#!/usr/bin/env python3
"""
Minimal script to generate OpenAPI JSON documentation without full app dependencies.
"""

import json
import os
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent

# Set minimal environment variables
os.environ.setdefault("DATABASE_URL", "mysql+aiomysql://user:password@localhost:3306/production_ticket")
os.environ.setdefault("SECRET_KEY", "temp-key-for-docs")
os.environ.setdefault("REDIS_URL", "redis://localhost:6379/0")

def create_openapi_schema():
    """Create OpenAPI schema manually based on our API structure."""
    return {
        "openapi": "3.1.0",
        "info": {
            "title": "Production Ticket System",
            "version": "1.0.0",
            "description": "FastAPI backend for production ticket management system with factory management, user authentication, and session handling."
        },
        "servers": [
            {"url": "http://localhost:8000", "description": "Development server"}
        ],
        "paths": {
            "/": {
                "get": {
                    "summary": "Root endpoint",
                    "tags": ["General"],
                    "responses": {
                        "200": {
                            "description": "Welcome message",
                            "content": {
                                "application/json": {
                                    "schema": {"type": "object"}
                                }
                            }
                        }
                    }
                }
            },
            "/health": {
                "get": {
                    "summary": "Health check endpoint",
                    "tags": ["General"],
                    "responses": {
                        "200": {
                            "description": "Health status",
                            "content": {
                                "application/json": {
                                    "schema": {"type": "object"}
                                }
                            }
                        }
                    }
                }
            },
            "/api/v1/auth/register": {
                "post": {
                    "summary": "Register new user",
                    "tags": ["Authentication"],
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "phone": {"type": "string"},
                                        "password": {"type": "string"},
                                        "username": {"type": "string"}
                                    }
                                }
                            }
                        }
                    },
                    "responses": {
                        "201": {"description": "User registered successfully"},
                        "400": {"description": "Invalid input"}
                    }
                }
            },
            "/api/v1/auth/login": {
                "post": {
                    "summary": "Login user",
                    "tags": ["Authentication"],
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "phone": {"type": "string"},
                                        "password": {"type": "string"}
                                    }
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {"description": "Login successful"},
                        "401": {"description": "Invalid credentials"}
                    }
                }
            },
            "/api/v1/auth-phone/login-phone-password": {
                "post": {
                    "summary": "Login with phone and password",
                    "tags": ["Phone Authentication"],
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "phone": {"type": "string"},
                                        "password": {"type": "string"},
                                        "image_code": {"type": "string"}
                                    }
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {"description": "Login successful"},
                        "401": {"description": "Invalid credentials"}
                    }
                }
            },
            "/api/v1/auth-phone/login-phone-sms": {
                "post": {
                    "summary": "Login with phone and SMS code",
                    "tags": ["Phone Authentication"],
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "phone": {"type": "string"},
                                        "sms_code": {"type": "string"},
                                        "image_code": {"type": "string"}
                                    }
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {"description": "Login successful"},
                        "401": {"description": "Invalid credentials"}
                    }
                }
            },
            "/api/v1/factory-management/join-request": {
                "post": {
                    "summary": "Request to join a factory",
                    "tags": ["Factory Management"],
                    "security": [{"BearerAuth": []}],
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "factory_id": {"type": "integer"},
                                        "desired_role": {"type": "string", "enum": ["WORKER", "SUPERVISOR"]}
                                    }
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {"description": "Join request submitted"},
                        "400": {"description": "Invalid request"}
                    }
                }
            },
            "/api/v1/factory-management/approve-request": {
                "post": {
                    "summary": "Approve or reject factory join request",
                    "tags": ["Factory Management"],
                    "security": [{"BearerAuth": []}],
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "user_factory_id": {"type": "integer"},
                                        "approved": {"type": "boolean"},
                                        "role": {"type": "string", "enum": ["WORKER", "SUPERVISOR", "MANAGER"]}
                                    }
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {"description": "Request processed"},
                        "403": {"description": "Permission denied"}
                    }
                }
            },
            "/api/v1/factory-management/pending-requests": {
                "get": {
                    "summary": "Get pending factory join requests",
                    "tags": ["Factory Management"],
                    "security": [{"BearerAuth": []}],
                    "parameters": [
                        {
                            "name": "X-Session-ID",
                            "in": "header",
                            "required": True,
                            "schema": {"type": "string"}
                        }
                    ],
                    "responses": {
                        "200": {"description": "List of pending requests"},
                        "403": {"description": "Permission denied"}
                    }
                }
            },
            "/api/v1/factory-management/members": {
                "get": {
                    "summary": "Get factory members",
                    "tags": ["Factory Management"],
                    "security": [{"BearerAuth": []}],
                    "parameters": [
                        {
                            "name": "X-Session-ID",
                            "in": "header",
                            "required": True,
                            "schema": {"type": "string"}
                        }
                    ],
                    "responses": {
                        "200": {"description": "List of factory members"},
                        "403": {"description": "Permission denied"}
                    }
                }
            },
            "/api/v1/session/switch-factory": {
                "post": {
                    "summary": "Switch factory context in session",
                    "tags": ["Session Management"],
                    "security": [{"BearerAuth": []}],
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "factory_id": {"type": "integer"}
                                    }
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {"description": "Factory context switched"},
                        "400": {"description": "Invalid factory ID"}
                    }
                }
            },
            "/api/v1/session/available-factories": {
                "get": {
                    "summary": "Get available factories for user",
                    "tags": ["Session Management"],
                    "security": [{"BearerAuth": []}],
                    "responses": {
                        "200": {"description": "List of available factories"},
                        "401": {"description": "Unauthorized"}
                    }
                }
            },
            "/api/v1/users/me": {
                "get": {
                    "summary": "Get current user information",
                    "tags": ["Users"],
                    "security": [{"BearerAuth": []}],
                    "responses": {
                        "200": {"description": "User information"},
                        "401": {"description": "Unauthorized"}
                    }
                }
            },
            "/api/v1/permissions/tree": {
                "get": {
                    "summary": "Get hierarchical permission tree",
                    "tags": ["Permissions"],
                    "security": [{"BearerAuth": []}],
                    "responses": {
                        "200": {"description": "Permission tree structure"},
                        "403": {"description": "Permission denied"}
                    }
                }
            },
            "/api/v1/permissions/list": {
                "get": {
                    "summary": "Get all permissions as flat list",
                    "tags": ["Permissions"],
                    "security": [{"BearerAuth": []}],
                    "responses": {
                        "200": {"description": "List of all permissions"},
                        "403": {"description": "Permission denied"}
                    }
                }
            },
            "/api/v1/roles": {
                "post": {
                    "summary": "Create new role",
                    "tags": ["Roles"],
                    "security": [{"BearerAuth": []}],
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "name": {"type": "string"},
                                        "description": {"type": "string"},
                                        "permission_ids": {"type": "array", "items": {"type": "integer"}}
                                    }
                                }
                            }
                        }
                    },
                    "responses": {
                        "201": {"description": "Role created successfully"},
                        "403": {"description": "Permission denied"}
                    }
                },
                "get": {
                    "summary": "Get all roles",
                    "tags": ["Roles"],
                    "security": [{"BearerAuth": []}],
                    "responses": {
                        "200": {"description": "List of all roles"},
                        "403": {"description": "Permission denied"}
                    }
                }
            },
            "/api/v1/roles/{role_id}": {
                "get": {
                    "summary": "Get role by ID",
                    "tags": ["Roles"],
                    "security": [{"BearerAuth": []}],
                    "parameters": [
                        {"name": "role_id", "in": "path", "required": True, "schema": {"type": "integer"}}
                    ],
                    "responses": {
                        "200": {"description": "Role information"},
                        "404": {"description": "Role not found"}
                    }
                },
                "put": {
                    "summary": "Update role",
                    "tags": ["Roles"],
                    "security": [{"BearerAuth": []}],
                    "parameters": [
                        {"name": "role_id", "in": "path", "required": True, "schema": {"type": "integer"}}
                    ],
                    "responses": {
                        "200": {"description": "Role updated successfully"},
                        "404": {"description": "Role not found"}
                    }
                },
                "delete": {
                    "summary": "Delete role",
                    "tags": ["Roles"],
                    "security": [{"BearerAuth": []}],
                    "parameters": [
                        {"name": "role_id", "in": "path", "required": True, "schema": {"type": "integer"}}
                    ],
                    "responses": {
                        "204": {"description": "Role deleted successfully"},
                        "404": {"description": "Role not found"}
                    }
                }
            },
            "/api/v1/roles/{role_id}/permissions": {
                "post": {
                    "summary": "Assign permissions to role",
                    "tags": ["Roles"],
                    "security": [{"BearerAuth": []}],
                    "parameters": [
                        {"name": "role_id", "in": "path", "required": True, "schema": {"type": "integer"}}
                    ],
                    "responses": {
                        "200": {"description": "Permissions assigned successfully"},
                        "404": {"description": "Role not found"}
                    }
                },
                "delete": {
                    "summary": "Remove permissions from role",
                    "tags": ["Roles"],
                    "security": [{"BearerAuth": []}],
                    "parameters": [
                        {"name": "role_id", "in": "path", "required": True, "schema": {"type": "integer"}}
                    ],
                    "responses": {
                        "200": {"description": "Permissions removed successfully"},
                        "404": {"description": "Role not found"}
                    }
                },
                "get": {
                    "summary": "Get role permissions",
                    "tags": ["Roles"],
                    "security": [{"BearerAuth": []}],
                    "parameters": [
                        {"name": "role_id", "in": "path", "required": True, "schema": {"type": "integer"}}
                    ],
                    "responses": {
                        "200": {"description": "List of role permissions"},
                        "404": {"description": "Role not found"}
                    }
                }
            },
            "/api/v1/user-management/user-list": {
                "get": {
                    "summary": "Get factory user list with skills",
                    "tags": ["User Management"],
                    "security": [{"BearerAuth": []}],
                    "parameters": [
                        {"name": "X-Session-ID", "in": "header", "required": True, "schema": {"type": "string"}}
                    ],
                    "responses": {
                        "200": {"description": "List of factory users with skills"},
                        "403": {"description": "Permission denied"}
                    }
                }
            },
            "/api/v1/user-management/available-users": {
                "get": {
                    "summary": "Get users available to add to factory",
                    "tags": ["User Management"],
                    "security": [{"BearerAuth": []}],
                    "parameters": [
                        {"name": "X-Session-ID", "in": "header", "required": True, "schema": {"type": "string"}},
                        {"name": "search_term", "in": "query", "schema": {"type": "string"}},
                        {"name": "role_id", "in": "query", "schema": {"type": "integer"}},
                        {"name": "is_active", "in": "query", "schema": {"type": "boolean"}}
                    ],
                    "responses": {
                        "200": {"description": "List of available users"},
                        "403": {"description": "Permission denied"}
                    }
                }
            },
            "/api/v1/user-management/add-users": {
                "post": {
                    "summary": "Add multiple users to factory",
                    "tags": ["User Management"],
                    "security": [{"BearerAuth": []}],
                    "parameters": [
                        {"name": "X-Session-ID", "in": "header", "required": True, "schema": {"type": "string"}}
                    ],
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "users": {
                                            "type": "array",
                                            "items": {
                                                "type": "object",
                                                "properties": {
                                                    "user_id": {"type": "integer"},
                                                    "factory_role": {"type": "string"},
                                                    "start_date": {"type": "string"}
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {"description": "Users added successfully"},
                        "403": {"description": "Permission denied"}
                    }
                }
            },
            "/api/v1/user-management/bind-roles": {
                "post": {
                    "summary": "Bind system role to user",
                    "tags": ["User Management"],
                    "security": [{"BearerAuth": []}],
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "user_id": {"type": "integer"},
                                        "role_id": {"type": "integer"}
                                    }
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {"description": "Role bound successfully"},
                        "403": {"description": "Permission denied"}
                    }
                }
            },
            "/api/v1/user-management/suspend": {
                "post": {
                    "summary": "Suspend user in factory",
                    "tags": ["User Management"],
                    "security": [{"BearerAuth": []}],
                    "parameters": [
                        {"name": "X-Session-ID", "in": "header", "required": True, "schema": {"type": "string"}}
                    ],
                    "responses": {
                        "200": {"description": "User suspended successfully"},
                        "403": {"description": "Permission denied"}
                    }
                }
            },
            "/api/v1/user-management/remove-user": {
                "delete": {
                    "summary": "Remove user from factory",
                    "tags": ["User Management"],
                    "security": [{"BearerAuth": []}],
                    "parameters": [
                        {"name": "X-Session-ID", "in": "header", "required": True, "schema": {"type": "string"}}
                    ],
                    "responses": {
                        "200": {"description": "User removed successfully"},
                        "403": {"description": "Permission denied"}
                    }
                }
            },
            "/api/v1/skills": {
                "post": {
                    "summary": "Create new skill",
                    "tags": ["Skills"],
                    "security": [{"BearerAuth": []}],
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "code": {"type": "string"},
                                        "name": {"type": "string"},
                                        "description": {"type": "string"},
                                        "category": {"type": "string"}
                                    }
                                }
                            }
                        }
                    },
                    "responses": {
                        "201": {"description": "Skill created successfully"},
                        "403": {"description": "Permission denied"}
                    }
                },
                "get": {
                    "summary": "Get all skills with optional filters",
                    "tags": ["Skills"],
                    "security": [{"BearerAuth": []}],
                    "parameters": [
                        {"name": "search_term", "in": "query", "schema": {"type": "string"}},
                        {"name": "category", "in": "query", "schema": {"type": "string"}},
                        {"name": "is_active", "in": "query", "schema": {"type": "boolean"}}
                    ],
                    "responses": {
                        "200": {"description": "List of skills"},
                        "403": {"description": "Permission denied"}
                    }
                }
            },
            "/api/v1/skills/{skill_id}": {
                "get": {
                    "summary": "Get skill by ID",
                    "tags": ["Skills"],
                    "security": [{"BearerAuth": []}],
                    "parameters": [
                        {"name": "skill_id", "in": "path", "required": True, "schema": {"type": "integer"}}
                    ],
                    "responses": {
                        "200": {"description": "Skill information"},
                        "404": {"description": "Skill not found"}
                    }
                },
                "put": {
                    "summary": "Update skill",
                    "tags": ["Skills"],
                    "security": [{"BearerAuth": []}],
                    "parameters": [
                        {"name": "skill_id", "in": "path", "required": True, "schema": {"type": "integer"}}
                    ],
                    "responses": {
                        "200": {"description": "Skill updated successfully"},
                        "404": {"description": "Skill not found"}
                    }
                },
                "delete": {
                    "summary": "Delete skill",
                    "tags": ["Skills"],
                    "security": [{"BearerAuth": []}],
                    "parameters": [
                        {"name": "skill_id", "in": "path", "required": True, "schema": {"type": "integer"}}
                    ],
                    "responses": {
                        "204": {"description": "Skill deleted successfully"},
                        "404": {"description": "Skill not found"}
                    }
                }
            },
            "/api/v1/skills/user/{user_id}/skills": {
                "get": {
                    "summary": "Get user's skills in current factory",
                    "tags": ["Skills"],
                    "security": [{"BearerAuth": []}],
                    "parameters": [
                        {"name": "user_id", "in": "path", "required": True, "schema": {"type": "integer"}},
                        {"name": "X-Session-ID", "in": "header", "required": True, "schema": {"type": "string"}}
                    ],
                    "responses": {
                        "200": {"description": "User's skills with proficiency and certification"},
                        "404": {"description": "User not found in factory"}
                    }
                }
            },
            "/api/v1/skills/assign": {
                "post": {
                    "summary": "Assign skills to user in factory",
                    "tags": ["Skills"],
                    "security": [{"BearerAuth": []}],
                    "parameters": [
                        {"name": "X-Session-ID", "in": "header", "required": True, "schema": {"type": "string"}}
                    ],
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "user_id": {"type": "integer"},
                                        "skills": {
                                            "type": "array",
                                            "items": {
                                                "type": "object",
                                                "properties": {
                                                    "skill_id": {"type": "integer"},
                                                    "proficiency_level": {"type": "string", "enum": ["BEGINNER", "INTERMEDIATE", "ADVANCED", "EXPERT"]}
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {"description": "Skills assigned successfully"},
                        "403": {"description": "Permission denied"}
                    }
                }
            },
            "/api/v1/skills/modify-proficiency": {
                "put": {
                    "summary": "Modify user's skill proficiency",
                    "tags": ["Skills"],
                    "security": [{"BearerAuth": []}],
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "user_factory_skill_id": {"type": "integer"},
                                        "proficiency_level": {"type": "string", "enum": ["BEGINNER", "INTERMEDIATE", "ADVANCED", "EXPERT"]},
                                        "notes": {"type": "string"}
                                    }
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {"description": "Skill proficiency updated successfully"},
                        "403": {"description": "Permission denied"}
                    }
                }
            },
            "/api/v1/skills/certify": {
                "post": {
                    "summary": "Certify user in skill",
                    "tags": ["Skills"],
                    "security": [{"BearerAuth": []}],
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "user_factory_skill_id": {"type": "integer"},
                                        "certification_expires": {"type": "string", "format": "date-time"},
                                        "notes": {"type": "string"}
                                    }
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {"description": "User certified successfully"},
                        "403": {"description": "Permission denied"}
                    }
                }
            },
            "/api/v1/skills/remove": {
                "delete": {
                    "summary": "Remove skill from user",
                    "tags": ["Skills"],
                    "security": [{"BearerAuth": []}],
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "user_factory_skill_id": {"type": "integer"},
                                        "reason": {"type": "string"}
                                    }
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {"description": "Skill removed successfully"},
                        "403": {"description": "Permission denied"}
                    }
                }
            }
        },
        "components": {
            "securitySchemes": {
                "BearerAuth": {
                    "type": "http",
                    "scheme": "bearer",
                    "bearerFormat": "JWT"
                }
            }
        },
        "tags": [
            {"name": "General", "description": "General endpoints"},
            {"name": "Authentication", "description": "User authentication"},
            {"name": "Phone Authentication", "description": "Phone-based authentication methods"},
            {"name": "Factory Management", "description": "Factory joining and management operations"},
            {"name": "Session Management", "description": "User session and factory context management"},
            {"name": "Users", "description": "User management operations"},
            {"name": "Permissions", "description": "Permission management and hierarchy"},
            {"name": "Roles", "description": "Role-based access control management"},
            {"name": "User Management", "description": "Factory user management and administration"},
            {"name": "Skills", "description": "Skill management and user skill assignments"}
        ]
    }

def generate_openapi_json():
    """Generate OpenAPI JSON schema."""
    print("🚀 Generating OpenAPI JSON documentation...")
    
    # Get OpenAPI schema
    openapi_schema = create_openapi_schema()
    
    # Output file path
    output_file = project_root / "openapi.json"
    
    # Write to file with pretty formatting
    with open(output_file, "w", encoding="utf-8") as f:
        json.dump(openapi_schema, f, indent=2, ensure_ascii=False)
    
    print(f"✅ OpenAPI JSON generated successfully: {output_file}")
    print(f"📄 API Title: {openapi_schema.get('info', {}).get('title', 'Unknown')}")
    print(f"🔢 Version: {openapi_schema.get('info', {}).get('version', 'Unknown')}")
    
    # Count endpoints
    paths = openapi_schema.get('paths', {})
    endpoint_count = sum(len(methods) for methods in paths.values() if isinstance(methods, dict))
    print(f"🛠️  Total endpoints: {endpoint_count}")
    
    # List main tags/groups
    tags = openapi_schema.get('tags', [])
    if tags:
        tag_names = [tag['name'] for tag in tags]
        print(f"🏷️  API groups: {', '.join(tag_names)}")
    
    return output_file

def print_api_summary():
    """Print a summary of the API endpoints."""
    openapi_schema = create_openapi_schema()
    paths = openapi_schema.get('paths', {})
    
    print("\n📋 API Endpoints Summary:")
    print("=" * 70)
    
    for path, methods in paths.items():
        for method, details in methods.items():
            if isinstance(details, dict):
                summary = details.get('summary', 'No summary')
                tags = ', '.join(details.get('tags', ['untagged']))
                print(f"{method.upper():6} {path:35} [{tags}]")
                print(f"       {summary}")

if __name__ == "__main__":
    print("🚀 FastAPI OpenAPI Documentation Generator (Minimal)")
    print("=" * 60)
    
    # Generate JSON
    json_file = generate_openapi_json()
    
    # Print API summary
    print_api_summary()
    
    print("\n" + "=" * 60)
    print("🎉 Documentation generation completed!")
    print(f"📁 Files generated in: {project_root}")
    print("💡 You can now use these files with:")
    print("   • Swagger UI: https://editor.swagger.io/")
    print("   • Redoc: https://redocly.github.io/redoc/")
    print("   • Postman: Import the JSON file")
    print("   • OpenAPI tools and generators")