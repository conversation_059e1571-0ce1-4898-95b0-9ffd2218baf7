from dataclasses import dataclass
from typing import Optional
from datetime import datetime


@dataclass
class UserSession:
    """Value object representing user session context."""
    
    user_id: int
    username: str
    current_factory_id: Optional[int] = None
    current_factory_name: Optional[str] = None
    current_department_id: Optional[int] = None
    current_role: Optional[str] = None
    is_factory_manager: bool = False
    session_created_at: datetime = None
    
    def __post_init__(self):
        if self.session_created_at is None:
            self.session_created_at = datetime.utcnow()
    
    def has_factory_context(self) -> bool:
        """Check if user has a factory context set."""
        return self.current_factory_id is not None
    
    def can_manage_factory(self) -> bool:
        """Check if user can manage the current factory."""
        return self.is_factory_manager and self.has_factory_context()
    
    def switch_factory_context(
        self, 
        factory_id: int, 
        factory_name: str, 
        department_id: Optional[int] = None,
        role: Optional[str] = None,
        is_manager: bool = False
    ) -> "UserSession":
        """Create new session with different factory context."""
        return UserSession(
            user_id=self.user_id,
            username=self.username,
            current_factory_id=factory_id,
            current_factory_name=factory_name,
            current_department_id=department_id,
            current_role=role,
            is_factory_manager=is_manager,
            session_created_at=self.session_created_at
        )
    
    def clear_factory_context(self) -> "UserSession":
        """Create new session with cleared factory context."""
        return UserSession(
            user_id=self.user_id,
            username=self.username,
            current_factory_id=None,
            current_factory_name=None,
            current_department_id=None,
            current_role=None,
            is_factory_manager=False,
            session_created_at=self.session_created_at
        )