from enum import Enum
from dataclasses import dataclass
from datetime import datetime, timedelta
from typing import Optional


class ValidationCodeType(Enum):
    """Types of validation codes."""
    SMS = "sms"
    IMAGE = "image"


@dataclass(frozen=True)
class ValidationCode:
    """Value object for validation codes."""
    
    code: str
    code_type: ValidationCodeType
    identifier: str  # phone number for SMS, session_id for image
    expires_at: datetime
    created_at: datetime
    
    @classmethod
    def create_sms_code(cls, phone: str, code: str, expires_in_minutes: int = 5) -> "ValidationCode":
        """Create SMS validation code."""
        now = datetime.utcnow()
        return cls(
            code=code,
            code_type=ValidationCodeType.SMS,
            identifier=phone,
            expires_at=now + timedelta(minutes=expires_in_minutes),
            created_at=now
        )
    
    @classmethod
    def create_image_code(cls, session_id: str, code: str, expires_in_minutes: int = 10) -> "ValidationCode":
        """Create image validation code."""
        now = datetime.utcnow()
        return cls(
            code=code,
            code_type=ValidationCodeType.IMAGE,
            identifier=session_id,
            expires_at=now + timedelta(minutes=expires_in_minutes),
            created_at=now
        )
    
    def is_expired(self) -> bool:
        """Check if validation code is expired."""
        return datetime.utcnow() > self.expires_at
    
    def is_valid_for(self, identifier: str, code: str) -> bool:
        """Check if code is valid for given identifier."""
        return (
            not self.is_expired() and
            self.identifier == identifier and
            self.code.lower() == code.lower()
        )