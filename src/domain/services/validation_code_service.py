import random
import string
from abc import ABC, abstractmethod
from typing import Optional
from src.domain.value_objects.validation_code import ValidationCode, ValidationCodeType


class ValidationCodeGeneratorInterface(ABC):
    """Abstract interface for validation code generation."""
    
    @abstractmethod
    def generate_sms_code(self, length: int = 6) -> str:
        """Generate SMS validation code."""
        pass
    
    @abstractmethod
    def generate_image_code(self, length: int = 4) -> str:
        """Generate image validation code."""
        pass


class ValidationCodeGenerator(ValidationCodeGeneratorInterface):
    """Implementation of validation code generator."""
    
    def generate_sms_code(self, length: int = 6) -> str:
        """Generate numeric SMS validation code."""
        return ''.join(random.choices(string.digits, k=length))
    
    def generate_image_code(self, length: int = 4) -> str:
        """Generate alphanumeric image validation code."""
        # Use uppercase letters and digits, excluding confusing characters
        chars = "ABCDEFGHJKLMNPQRSTUVWXYZ23456789"
        return ''.join(random.choices(chars, k=length))


class ValidationCodeService:
    """Domain service for validation code operations."""
    
    def __init__(self, generator: ValidationCodeGeneratorInterface):
        self.generator = generator
    
    def create_sms_validation_code(self, phone: str, expires_in_minutes: int = 5) -> ValidationCode:
        """Create SMS validation code."""
        code = self.generator.generate_sms_code()
        return ValidationCode.create_sms_code(phone, code, expires_in_minutes)
    
    def create_image_validation_code(self, session_id: str, expires_in_minutes: int = 10) -> ValidationCode:
        """Create image validation code."""
        code = self.generator.generate_image_code()
        return ValidationCode.create_image_code(session_id, code, expires_in_minutes)
    
    def validate_phone_format(self, phone: str) -> bool:
        """Validate phone number format."""
        # Basic phone validation - can be enhanced based on requirements
        import re
        pattern = r'^1[3-9]\d{9}$'  # Chinese mobile number format
        return bool(re.match(pattern, phone))
    
    def normalize_phone(self, phone: str) -> str:
        """Normalize phone number format."""
        # Remove any non-digit characters
        return ''.join(filter(str.isdigit, phone))