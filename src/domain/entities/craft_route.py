from datetime import datetime
from typing import Optional, TYPE_CHECKING, List, Dict, Any
from sqlalchemy import String, Integer, DateTime, ForeignKey, Text, JSON, UniqueConstraint
from sqlalchemy.orm import Mapped, mapped_column, relationship
from .base import Base

if TYPE_CHECKING:
    from .craft import Craft
    from .skill import Skill


class CraftRoute(Base):
    """CraftRoute entity representing the relationship between skills and crafts with workflow information."""
    
    __tablename__ = "craft_routes"
    __table_args__ = (
        UniqueConstraint('craft_code', 'code', name='uq_craft_route_craft_code_code'),
    )
    
    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True)
    craft_code: Mapped[str] = mapped_column(String(50), ForeignKey("crafts.code"), index=True)
    skill_code: Mapped[str] = mapped_column(String(50), ForeignKey("skills.code"), index=True)
    code: Mapped[str] = mapped_column(String(50), index=True, comment="Unique code within craft")
    name: Mapped[str] = mapped_column(String(100), comment="Display name for this route")
    order: Mapped[int] = mapped_column(Integer, default=0, index=True)
    
    # JSON arrays for measurement and registration types
    measurement_types: Mapped[Optional[List[str]]] = mapped_column(JSON, comment="Available measurement types (JSON array)")
    registration_types: Mapped[Optional[List[str]]] = mapped_column(JSON, comment="Available registration types (JSON array)")
    
    # Optional additional configuration
    notes: Mapped[Optional[str]] = mapped_column(Text)
    is_required: Mapped[bool] = mapped_column(default=True)
    
    # Audit fields
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    craft: Mapped["Craft"] = relationship("Craft", back_populates="craft_routes")
    skill: Mapped["Skill"] = relationship("Skill")
    
    def __repr__(self) -> str:
        return f"<CraftRoute(craft_code='{self.craft_code}', code='{self.code}', name='{self.name}', skill_code='{self.skill_code}', order={self.order})>"
    
    def get_measurement_types(self) -> List[str]:
        """Get list of available measurement types."""
        return self.measurement_types or []
    
    def get_registration_types(self) -> List[str]:
        """Get list of available registration types."""
        return self.registration_types or []
    
    def set_measurement_types(self, types: List[str]) -> None:
        """Set measurement types."""
        self.measurement_types = types
    
    def set_registration_types(self, types: List[str]) -> None:
        """Set registration types."""
        self.registration_types = types
    
    def add_measurement_type(self, measurement_type: str) -> None:
        """Add a measurement type if not already present."""
        current_types = self.get_measurement_types()
        if measurement_type not in current_types:
            current_types.append(measurement_type)
            self.measurement_types = current_types
    
    def add_registration_type(self, registration_type: str) -> None:
        """Add a registration type if not already present."""
        current_types = self.get_registration_types()
        if registration_type not in current_types:
            current_types.append(registration_type)
            self.registration_types = current_types
    
    def remove_measurement_type(self, measurement_type: str) -> bool:
        """Remove a measurement type. Returns True if removed, False if not found."""
        current_types = self.get_measurement_types()
        if measurement_type in current_types:
            current_types.remove(measurement_type)
            self.measurement_types = current_types
            return True
        return False
    
    def remove_registration_type(self, registration_type: str) -> bool:
        """Remove a registration type. Returns True if removed, False if not found."""
        current_types = self.get_registration_types()
        if registration_type in current_types:
            current_types.remove(registration_type)
            self.registration_types = current_types
            return True
        return False