from datetime import datetime
from typing import Optional, List, TYPE_CHECKING
from sqlalchemy import String, DateTime, Boolean, Text, Integer, ForeignKey
from sqlalchemy.orm import Mapped, mapped_column, relationship
from src.infrastructure.database.database import Base

if TYPE_CHECKING:
    from .role import Role
    from .user_factory import UserFactory, UserFactoryRole


class User(Base):
    """User entity representing a system user."""
    
    __tablename__ = "users"
    
    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True)
    username: Mapped[str] = mapped_column(String(50), unique=True, index=True)
    email: Mapped[str] = mapped_column(String(100), unique=True, index=True)
    hashed_password: Mapped[str] = mapped_column(String(255))
    full_name: Mapped[Optional[str]] = mapped_column(String(100))
    phone: Mapped[Optional[str]] = mapped_column(String(20), unique=True, index=True)
    is_active: Mapped[bool] = mapped_column(Boolean, default=True)
    is_superuser: Mapped[bool] = mapped_column(Boolean, default=False)
    avatar_url: Mapped[Optional[str]] = mapped_column(Text)
    role_id: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey("roles.id"), nullable=True)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Many-to-one relationship with role
    role: Mapped[Optional["Role"]] = relationship("Role", back_populates="users")
    
    # Many-to-many relationship with factories
    user_factories: Mapped[List["UserFactory"]] = relationship("UserFactory", foreign_keys="UserFactory.user_id", back_populates="user")
    
    def __repr__(self) -> str:
        return f"<User(id={self.id}, username='{self.username}', email='{self.email}')>"
    
    def get_active_factories(self) -> List["UserFactory"]:
        """Get all factories where user is actively working."""
        return [uf for uf in self.user_factories if uf.is_active()]
    
    def get_factories_by_role(self, role: "UserFactoryRole") -> List["UserFactory"]:
        """Get factories where user has specific role."""
        return [uf for uf in self.user_factories if uf.is_active() and uf.role == role]
    
    def get_managed_factories(self) -> List["UserFactory"]:
        """Get factories where user is a manager."""
        return [uf for uf in self.user_factories if uf.can_approve_requests()]
    
    def is_factory_manager(self, factory_id: int) -> bool:
        """Check if user is a manager in specific factory."""
        for uf in self.user_factories:
            if uf.factory_id == factory_id and uf.can_approve_requests():
                return True
        return False
    
    def has_permission(self, permission_code: str) -> bool:
        """Check if user has a specific permission through their role."""
        try:
            if not self.role or not self.role.is_active:
                return False
            return self.role.has_permission(permission_code)
        except Exception:
            # If we can't access role (detached instance), return False
            return False
    
    def has_any_permission(self, permission_codes: List[str]) -> bool:
        """Check if user has any of the specified permissions."""
        try:
            if not self.role or not self.role.is_active:
                return False
            return self.role.has_any_permission(permission_codes)
        except Exception:
            # If we can't access role (detached instance), return False
            return False
    
    def has_all_permissions(self, permission_codes: List[str]) -> bool:
        """Check if user has all of the specified permissions."""
        try:
            if not self.role or not self.role.is_active:
                return False
            return self.role.has_all_permissions(permission_codes)
        except Exception:
            # If we can't access role (detached instance), return False
            return False
    
    def get_permission_codes(self) -> List[str]:
        """Get all permission codes assigned to this user through their role."""
        try:
            if not self.role or not self.role.is_active:
                return []
            return self.role.get_permission_codes()
        except Exception:
            # If we can't access role (detached instance), return empty list
            return []
    
    def assign_role(self, role: "Role") -> None:
        """Assign a role to this user."""
        self.role = role
        self.role_id = role.id if role else None
    
    def remove_role(self) -> None:
        """Remove role from this user."""
        self.role = None
        self.role_id = None