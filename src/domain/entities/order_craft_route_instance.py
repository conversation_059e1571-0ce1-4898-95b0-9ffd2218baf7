from datetime import datetime, timezone
from typing import Optional, TYPE_CHECKING, Dict, Any, List
from enum import Enum
from sqlalchemy import String, Integer, DateTime, Text, Enum as SQLEnum, ForeignKey, JSON
from sqlalchemy.orm import Mapped, mapped_column, relationship
from .base import Base

if TYPE_CHECKING:
    from .order_craft_route import OrderCraftRoute
    from .user import User


class CompletionGranularity(Enum):
    """扫码登记粒度枚举"""
    BUNDLE = "bundle"     # 按扎登记
    BED = "bed"          # 按床登记  
    ORDER = "order"      # 整单登记


class SettlementStatus(Enum):
    """结算状态枚举"""
    PENDING = "pending"       # 待结算
    INCLUDED = "included"     # 已纳入账单
    SETTLED = "settled"       # 已结算
    DISPUTED = "disputed"     # 有争议
    EXCLUDED = "excluded"     # 排除结算


class OrderCraftRouteInstance(Base):
    """订单工艺路线实例 - 记录工人完成工艺路线的具体实例"""
    
    __tablename__ = "order_craft_route_instances"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    
    # 工厂信息
    factory_id: Mapped[int] = mapped_column(ForeignKey("factories.id"), index=True, comment="工厂ID")
    
    # 父级工艺路线
    order_craft_route_id: Mapped[int] = mapped_column(
        ForeignKey("order_craft_routes.id"), 
        index=True, 
        comment="订单工艺路线ID"
    )
    
    # 完成粒度
    completion_granularity: Mapped[CompletionGranularity] = mapped_column(
        SQLEnum(CompletionGranularity), 
        index=True,
        comment="完成粒度: bundle(按扎), bed(按床), order(整单)"
    )
    
    # 目标实体引用 (根据完成粒度)
    order_no: Mapped[str] = mapped_column(String(100), index=True, comment="订单号")
    order_part_nos: Mapped[Optional[List[str]]] = mapped_column(
        JSON, 
        nullable=True, 
        comment="订单部位号列表 (床级别或扎级别时填写)"
    )
    order_bundle_nos: Mapped[Optional[List[str]]] = mapped_column(
        JSON, 
        nullable=True, 
        comment="订单扎号列表 (扎级别时填写)"
    )
    
    # 兼容性字段 - 保留单个part_no和bundle_no用于查询和向后兼容
    order_part_no: Mapped[Optional[str]] = mapped_column(
        String(100), 
        index=True, 
        nullable=True, 
        comment="主要订单部位号 (兼容性字段)"
    )
    order_bundle_no: Mapped[Optional[str]] = mapped_column(
        String(100), 
        index=True, 
        nullable=True, 
        comment="主要订单扎号 (兼容性字段)"
    )
    
    # 工人和完成详情
    worker_user_id: Mapped[int] = mapped_column(
        ForeignKey("users.id"), 
        index=True, 
        comment="完成工人ID"
    )
    completed_quantity: Mapped[int] = mapped_column(
        Integer, 
        default=0, 
        comment="完成数量"
    )
    quality_level: Mapped[Optional[str]] = mapped_column(
        String(10), 
        nullable=True, 
        comment="质量等级 A/B/C"
    )
    
    # 状态和时间
    status: Mapped[str] = mapped_column(
        String(20), 
        default="completed", 
        index=True, 
        comment="状态: completed, verified, rejected"
    )
    settlement_status: Mapped[SettlementStatus] = mapped_column(
        SQLEnum(SettlementStatus),
        default=SettlementStatus.PENDING,
        index=True,
        comment="结算状态: pending, included, settled, disputed, excluded"
    )
    started_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime, 
        nullable=True, 
        comment="开始时间"
    )
    completed_at: Mapped[datetime] = mapped_column(
        DateTime, 
        default=lambda: datetime.now(timezone.utc), 
        comment="完成时间"
    )
    
    # 二维码和扫码数据
    qr_code_scanned: Mapped[Optional[str]] = mapped_column(
        String(200), 
        nullable=True, 
        comment="扫描的二维码内容"
    )
    scan_location: Mapped[Optional[str]] = mapped_column(
        String(100), 
        nullable=True, 
        comment="扫码位置"
    )
    device_info: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON, 
        nullable=True, 
        comment="设备信息"
    )
    
    # 附加数据
    notes: Mapped[Optional[str]] = mapped_column(Text, nullable=True, comment="备注")
    measurement_data: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON, 
        nullable=True, 
        comment="测量数据"
    )
    registration_data: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON, 
        nullable=True, 
        comment="登记数据"
    )
    
    # 系统字段
    created_at: Mapped[datetime] = mapped_column(
        DateTime, 
        default=lambda: datetime.now(timezone.utc), 
        comment="创建时间"
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, 
        default=lambda: datetime.now(timezone.utc), 
        onupdate=lambda: datetime.now(timezone.utc), 
        comment="更新时间"
    )
    
    # 关系
    order_craft_route: Mapped["OrderCraftRoute"] = relationship("OrderCraftRoute")
    worker: Mapped["User"] = relationship("User", foreign_keys=[worker_user_id])
    
    def __repr__(self) -> str:
        return f"<OrderCraftRouteInstance(id={self.id}, granularity='{self.completion_granularity.value}', worker_id={self.worker_user_id}, quantity={self.completed_quantity})>"
    
    def verify_completion(self, verified_by_user_id: int, notes: Optional[str] = None) -> None:
        """验证完成"""
        if self.status == "completed":
            self.status = "verified"
            self.updated_at = datetime.now(timezone.utc)
            if notes:
                self.notes = f"{self.notes or ''}\n[验证] {notes}".strip()
    
    def reject_completion(self, rejected_by_user_id: int, reason: str) -> None:
        """拒绝完成"""
        if self.status in ["completed", "verified"]:
            self.status = "rejected"
            self.updated_at = datetime.now(timezone.utc)
            self.notes = f"{self.notes or ''}\n[拒绝] {reason}".strip()
    
    def get_target_entity_key(self) -> str:
        """获取目标实体的键"""
        if self.completion_granularity == CompletionGranularity.BUNDLE:
            if self.order_bundle_nos:
                return f"bundles:{','.join(self.order_bundle_nos)}"
            return f"bundle:{self.order_bundle_no}" if self.order_bundle_no else "bundle:unknown"
        elif self.completion_granularity == CompletionGranularity.BED:
            if self.order_part_nos:
                return f"parts:{','.join(self.order_part_nos)}"
            return f"bed:{self.order_part_no}" if self.order_part_no else "bed:unknown"
        else:  # ORDER
            return f"order:{self.order_no}"
    
    def is_bundle_level(self) -> bool:
        """是否为扎级别登记"""
        return self.completion_granularity == CompletionGranularity.BUNDLE
    
    def is_bed_level(self) -> bool:
        """是否为床级别登记"""
        return self.completion_granularity == CompletionGranularity.BED
    
    def is_order_level(self) -> bool:
        """是否为整单级别登记"""
        return self.completion_granularity == CompletionGranularity.ORDER
    
    def set_measurement_data(self, data: Dict[str, Any]) -> None:
        """设置测量数据"""
        self.measurement_data = data
        self.updated_at = datetime.now(timezone.utc)
    
    def set_registration_data(self, data: Dict[str, Any]) -> None:
        """设置登记数据"""
        self.registration_data = data
        self.updated_at = datetime.now(timezone.utc)
    
    def get_measurement_data(self) -> Dict[str, Any]:
        """获取测量数据"""
        return self.measurement_data or {}
    
    def get_registration_data(self) -> Dict[str, Any]:
        """获取登记数据"""
        return self.registration_data or {}
    
    def calculate_work_duration(self) -> Optional[int]:
        """计算工作时长(分钟)"""
        if self.started_at and self.completed_at:
            return int((self.completed_at - self.started_at).total_seconds() / 60)
        return None
    
    def is_verified(self) -> bool:
        """是否已验证"""
        return self.status == "verified"
    
    def is_rejected(self) -> bool:
        """是否已拒绝"""
        return self.status == "rejected"
    
    def is_completed(self) -> bool:
        """是否已完成"""
        return self.status == "completed"
    
    # Settlement status methods
    def include_in_bill(self) -> None:
        """纳入账单"""
        if self.settlement_status == SettlementStatus.PENDING:
            self.settlement_status = SettlementStatus.INCLUDED
            self.updated_at = datetime.now(timezone.utc)
    
    def mark_as_settled(self) -> None:
        """标记为已结算"""
        if self.settlement_status == SettlementStatus.INCLUDED:
            self.settlement_status = SettlementStatus.SETTLED
            self.updated_at = datetime.now(timezone.utc)
    
    def mark_as_disputed(self, reason: Optional[str] = None) -> None:
        """标记为有争议"""
        self.settlement_status = SettlementStatus.DISPUTED
        self.updated_at = datetime.now(timezone.utc)
        if reason:
            self.notes = f"{self.notes or ''}\n[争议] {reason}".strip()
    
    def exclude_from_settlement(self, reason: str) -> None:
        """排除结算"""
        self.settlement_status = SettlementStatus.EXCLUDED
        self.updated_at = datetime.now(timezone.utc)
        self.notes = f"{self.notes or ''}\n[排除] {reason}".strip()
    
    def reset_settlement_status(self) -> None:
        """重置结算状态"""
        self.settlement_status = SettlementStatus.PENDING
        self.updated_at = datetime.now(timezone.utc)
    
    def is_pending_settlement(self) -> bool:
        """是否待结算"""
        return self.settlement_status == SettlementStatus.PENDING
    
    def is_included_in_bill(self) -> bool:
        """是否已纳入账单"""
        return self.settlement_status == SettlementStatus.INCLUDED
    
    def is_settled(self) -> bool:
        """是否已结算"""
        return self.settlement_status == SettlementStatus.SETTLED
    
    def is_disputed(self) -> bool:
        """是否有争议"""
        return self.settlement_status == SettlementStatus.DISPUTED
    
    def is_excluded(self) -> bool:
        """是否排除结算"""
        return self.settlement_status == SettlementStatus.EXCLUDED
    
    # Multiple parts/bundles management methods
    def add_part_no(self, part_no: str) -> None:
        """添加部位号"""
        if not self.order_part_nos:
            self.order_part_nos = []
        if part_no not in self.order_part_nos:
            self.order_part_nos.append(part_no)
            # Update main part_no for compatibility
            if not self.order_part_no:
                self.order_part_no = part_no
    
    def add_bundle_no(self, bundle_no: str) -> None:
        """添加扎号"""
        if not self.order_bundle_nos:
            self.order_bundle_nos = []
        if bundle_no not in self.order_bundle_nos:
            self.order_bundle_nos.append(bundle_no)
            # Update main bundle_no for compatibility
            if not self.order_bundle_no:
                self.order_bundle_no = bundle_no
    
    def set_part_nos(self, part_nos: List[str]) -> None:
        """设置部位号列表"""
        self.order_part_nos = part_nos
        # Set main part_no for compatibility
        self.order_part_no = part_nos[0] if part_nos else None
    
    def set_bundle_nos(self, bundle_nos: List[str]) -> None:
        """设置扎号列表"""
        self.order_bundle_nos = bundle_nos
        # Set main bundle_no for compatibility
        self.order_bundle_no = bundle_nos[0] if bundle_nos else None
    
    def get_all_part_nos(self) -> List[str]:
        """获取所有部位号"""
        part_nos = []
        if self.order_part_nos:
            part_nos.extend(self.order_part_nos)
        elif self.order_part_no:
            part_nos.append(self.order_part_no)
        return list(set(part_nos))  # Remove duplicates
    
    def get_all_bundle_nos(self) -> List[str]:
        """获取所有扎号"""
        bundle_nos = []
        if self.order_bundle_nos:
            bundle_nos.extend(self.order_bundle_nos)
        elif self.order_bundle_no:
            bundle_nos.append(self.order_bundle_no)
        return list(set(bundle_nos))  # Remove duplicates
    
    def has_part_no(self, part_no: str) -> bool:
        """检查是否包含指定部位号"""
        return part_no in self.get_all_part_nos()
    
    def has_bundle_no(self, bundle_no: str) -> bool:
        """检查是否包含指定扎号"""
        return bundle_no in self.get_all_bundle_nos()