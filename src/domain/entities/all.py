"""
Import all entities to ensure they are loaded and available for SQLAlchemy relationships.
This module should be imported before any database operations to ensure all entity
mappings are properly initialized.
"""

# Import all entities to ensure they are loaded
from .user import User
from .factory import Factory
from .department import Department
from .validation_code_record import ValidationCodeRecord
from .user_factory import UserFactory
from .user_factory_skill import UserFactorySkill
from .skill import Skill
from .role import Role
from .permission import Permission
from .role_permission import role_permissions
from .craft import Craft
from .craft_route import CraftRoute
from .order import Order
from .order_line import OrderLine
from .order_part import OrderPart
from .order_bundle import OrderBundle

# Export all entities for convenience
__all__ = [
    "User",
    "Factory", 
    "Department",
    "ValidationCodeRecord",
    "UserFactory",
    "UserFactorySkill",
    "Skill",
    "Role",
    "Permission",
    "role_permissions",
    "Craft",
    "CraftRoute",
    "Order",
    "OrderLine",
    "OrderPart",
    "OrderBundle",
]
