from typing import List, Optional
from sqlalchemy import String, Integer, Text, Boolean
from sqlalchemy.orm import Mapped, mapped_column, relationship
from .base import Base


class Skill(Base):
    """Skill entity representing job skills that workers can have."""
    
    __tablename__ = "skills"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    code: Mapped[str] = mapped_column(String(50), unique=True, index=True, nullable=False)
    name: Mapped[str] = mapped_column(String(100), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    is_active: Mapped[bool] = mapped_column(<PERSON>olean, default=True, nullable=False)
    category: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)  # e.g., "machine_operation", "quality_control", "maintenance"
    
    # Many-to-many relationship with user-factory through UserFactorySkill
    user_factory_skills: Mapped[List["UserFactorySkill"]] = relationship(
        "UserFactorySkill", 
        back_populates="skill"
    )
    
    def __repr__(self) -> str:
        return f"<Skill(id={self.id}, code='{self.code}', name='{self.name}')>"
    
    def activate(self) -> None:
        """Activate the skill."""
        self.is_active = True
    
    def deactivate(self) -> None:
        """Deactivate the skill."""
        self.is_active = False