from typing import Optional, List
from sqlalchemy import String, <PERSON>te<PERSON>, Foreign<PERSON><PERSON>
from sqlalchemy.orm import Mapped, mapped_column, relationship
from .base import Base


class Permission(Base):
    """Permission entity for role-based access control."""
    
    __tablename__ = "permissions"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    code: Mapped[str] = mapped_column(String(100), unique=True, index=True, nullable=False)
    name: Mapped[str] = mapped_column(String(200), nullable=False)
    parent_id: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey("permissions.id"), nullable=True)
    
    # Self-referential relationship for hierarchical permissions
    parent: Mapped[Optional["Permission"]] = relationship(
        "Permission", 
        remote_side=[id], 
        back_populates="children"
    )
    children: Mapped[List["Permission"]] = relationship(
        "Permission", 
        back_populates="parent",
        cascade="all, delete-orphan"
    )
    
    # Many-to-many relationship with roles
    roles: Mapped[List["Role"]] = relationship(
        "Role",
        secondary="role_permissions",
        back_populates="permissions"
    )
    
    def __repr__(self) -> str:
        return f"<Permission(id={self.id}, code='{self.code}', name='{self.name}')>"
    
    def is_child_of(self, permission: "Permission") -> bool:
        """Check if this permission is a child of another permission."""
        current = self.parent
        while current:
            if current.id == permission.id:
                return True
            current = current.parent
        return False
    
    def get_all_children(self) -> List["Permission"]:
        """Get all descendant permissions recursively."""
        all_children = []
        try:
            for child in self.children:
                all_children.append(child)
                all_children.extend(child.get_all_children())
        except Exception:
            # If we can't access children (detached instance), return empty list
            pass
        return all_children
    
    def get_permission_tree(self) -> dict:
        """Get permission tree structure."""
        return {
            "id": self.id,
            "code": self.code,
            "name": self.name,
            "parent_id": self.parent_id,
            "children": [child.get_permission_tree() for child in self.children]
        }