from datetime import datetime, timezone, date
from typing import Optional, TYPE_CHECKING, List, Dict, Any
from enum import Enum
from decimal import Decimal
from sqlalchemy import String, Integer, DateTime, Text, Enum as SQLEnum, ForeignKey, JSON, DECIMAL, Date, Boolean, UniqueConstraint
from sqlalchemy.orm import Mapped, mapped_column, relationship
from .base import Base

if TYPE_CHECKING:
    from .user import User
    from .order_craft_route_instance import OrderCraftRouteInstance


class BillStatus(Enum):
    """账单状态枚举"""
    DRAFT = "draft"           # 草稿
    PENDING = "pending"       # 待审核
    APPROVED = "approved"     # 已审核
    PAID = "paid"            # 已支付
    REJECTED = "rejected"     # 已拒绝
    CANCELLED = "cancelled"   # 已取消


class PaymentMethod(Enum):
    """支付方式枚举"""
    CASH = "cash"            # 现金
    BANK_TRANSFER = "bank_transfer"  # 银行转账
    MOBILE_PAY = "mobile_pay"        # 移动支付
    CHECK = "check"          # 支票
    OTHER = "other"          # 其他


class DailyWorkerBill(Base):
    """日结工人账单实体"""
    
    __tablename__ = "daily_worker_bills"
    __table_args__ = (
        UniqueConstraint('worker_user_id', 'factory_id', 'bill_date', name='uq_worker_factory_date'),
    )
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    
    # 基本信息
    factory_id: Mapped[int] = mapped_column(ForeignKey("factories.id"), index=True, comment="工厂ID")
    worker_user_id: Mapped[int] = mapped_column(ForeignKey("users.id"), index=True, comment="工人ID")
    bill_date: Mapped[date] = mapped_column(Date, index=True, comment="账单日期")
    bill_no: Mapped[str] = mapped_column(String(100), unique=True, index=True, comment="账单号")
    
    # 统计信息
    total_completed_quantity: Mapped[int] = mapped_column(Integer, default=0, comment="总完成数量")
    total_work_instances: Mapped[int] = mapped_column(Integer, default=0, comment="总工作实例数")
    total_work_duration_minutes: Mapped[int] = mapped_column(Integer, default=0, comment="总工作时长(分钟)")
    
    # 金额信息
    base_amount: Mapped[Decimal] = mapped_column(DECIMAL(10, 2), default=0, comment="基础金额")
    bonus_amount: Mapped[Decimal] = mapped_column(DECIMAL(10, 2), default=0, comment="奖金金额")
    deduction_amount: Mapped[Decimal] = mapped_column(DECIMAL(10, 2), default=0, comment="扣除金额")
    total_amount: Mapped[Decimal] = mapped_column(DECIMAL(10, 2), default=0, comment="总金额")
    
    # 状态和审核
    status: Mapped[BillStatus] = mapped_column(SQLEnum(BillStatus), default=BillStatus.DRAFT, index=True, comment="账单状态")
    
    # 审核信息
    reviewed_by_user_id: Mapped[Optional[int]] = mapped_column(ForeignKey("users.id"), nullable=True, comment="审核人ID")
    reviewed_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True, comment="审核时间")
    review_notes: Mapped[Optional[str]] = mapped_column(Text, nullable=True, comment="审核备注")
    
    # 支付信息
    payment_method: Mapped[Optional[PaymentMethod]] = mapped_column(SQLEnum(PaymentMethod), nullable=True, comment="支付方式")
    paid_by_user_id: Mapped[Optional[int]] = mapped_column(ForeignKey("users.id"), nullable=True, comment="支付人ID")
    paid_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True, comment="支付时间")
    payment_reference: Mapped[Optional[str]] = mapped_column(String(200), nullable=True, comment="支付凭证号")
    payment_notes: Mapped[Optional[str]] = mapped_column(Text, nullable=True, comment="支付备注")
    
    # 质量统计
    quality_score_average: Mapped[Optional[Decimal]] = mapped_column(DECIMAL(5, 2), nullable=True, comment="平均质量分数")
    defect_count: Mapped[int] = mapped_column(Integer, default=0, comment="次品数量")
    rework_count: Mapped[int] = mapped_column(Integer, default=0, comment="返工数量")
    
    # 分解数据
    breakdown_by_granularity: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON, nullable=True, comment="按粒度分解的统计")
    breakdown_by_craft_route: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON, nullable=True, comment="按工艺路线分解的统计")
    breakdown_by_order: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON, nullable=True, comment="按订单分解的统计")
    
    # 自动生成标识
    is_auto_generated: Mapped[bool] = mapped_column(Boolean, default=True, comment="是否自动生成")
    generated_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True, comment="生成时间")
    
    # 备注
    notes: Mapped[Optional[str]] = mapped_column(Text, nullable=True, comment="备注")
    
    # 系统字段
    created_at: Mapped[datetime] = mapped_column(DateTime, default=lambda: datetime.now(timezone.utc), comment="创建时间")
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc), comment="更新时间")
    
    # 关系
    worker: Mapped["User"] = relationship("User", foreign_keys=[worker_user_id])
    reviewer: Mapped[Optional["User"]] = relationship("User", foreign_keys=[reviewed_by_user_id])
    payer: Mapped[Optional["User"]] = relationship("User", foreign_keys=[paid_by_user_id])
    
    def __repr__(self) -> str:
        return f"<DailyWorkerBill(bill_no='{self.bill_no}', worker_id={self.worker_user_id}, date={self.bill_date}, amount={self.total_amount})>"
    
    @classmethod
    def generate_bill_no(cls, factory_id: int, worker_user_id: int, bill_date: date) -> str:
        """生成账单号: F{factory_id}_W{worker_id}_{YYYYMMDD}"""
        date_str = bill_date.strftime("%Y%m%d")
        return f"F{factory_id}_W{worker_user_id}_{date_str}"
    
    def calculate_total_amount(self) -> Decimal:
        """计算总金额"""
        return self.base_amount + self.bonus_amount - self.deduction_amount
    
    def update_total_amount(self) -> None:
        """更新总金额"""
        self.total_amount = self.calculate_total_amount()
        self.updated_at = datetime.now(timezone.utc)
    
    def submit_for_review(self) -> None:
        """提交审核"""
        if self.status == BillStatus.DRAFT:
            self.status = BillStatus.PENDING
            self.updated_at = datetime.now(timezone.utc)
    
    def approve_bill(self, reviewer_user_id: int, notes: Optional[str] = None) -> None:
        """审核通过"""
        if self.status == BillStatus.PENDING:
            self.status = BillStatus.APPROVED
            self.reviewed_by_user_id = reviewer_user_id
            self.reviewed_at = datetime.now(timezone.utc)
            self.review_notes = notes
            self.updated_at = datetime.now(timezone.utc)
    
    def reject_bill(self, reviewer_user_id: int, reason: str) -> None:
        """拒绝账单"""
        if self.status == BillStatus.PENDING:
            self.status = BillStatus.REJECTED
            self.reviewed_by_user_id = reviewer_user_id
            self.reviewed_at = datetime.now(timezone.utc)
            self.review_notes = reason
            self.updated_at = datetime.now(timezone.utc)
    
    def mark_as_paid(
        self, 
        payer_user_id: int, 
        payment_method: PaymentMethod,
        payment_reference: Optional[str] = None,
        payment_notes: Optional[str] = None
    ) -> None:
        """标记为已支付"""
        if self.status == BillStatus.APPROVED:
            self.status = BillStatus.PAID
            self.paid_by_user_id = payer_user_id
            self.paid_at = datetime.now(timezone.utc)
            self.payment_method = payment_method
            self.payment_reference = payment_reference
            self.payment_notes = payment_notes
            self.updated_at = datetime.now(timezone.utc)
    
    def cancel_bill(self, reason: Optional[str] = None) -> None:
        """取消账单"""
        if self.status in [BillStatus.DRAFT, BillStatus.PENDING, BillStatus.REJECTED]:
            self.status = BillStatus.CANCELLED
            if reason:
                self.notes = f"{self.notes or ''}\n[取消] {reason}".strip()
            self.updated_at = datetime.now(timezone.utc)
    
    def is_editable(self) -> bool:
        """检查是否可编辑"""
        return self.status in [BillStatus.DRAFT, BillStatus.REJECTED]
    
    def is_pending_review(self) -> bool:
        """检查是否待审核"""
        return self.status == BillStatus.PENDING
    
    def is_approved(self) -> bool:
        """检查是否已审核"""
        return self.status == BillStatus.APPROVED
    
    def is_paid(self) -> bool:
        """检查是否已支付"""
        return self.status == BillStatus.PAID
    
    def get_breakdown_by_granularity(self) -> Dict[str, Any]:
        """获取按粒度分解的统计"""
        return self.breakdown_by_granularity or {}
    
    def get_breakdown_by_craft_route(self) -> Dict[str, Any]:
        """获取按工艺路线分解的统计"""
        return self.breakdown_by_craft_route or {}
    
    def get_breakdown_by_order(self) -> Dict[str, Any]:
        """获取按订单分解的统计"""
        return self.breakdown_by_order or {}
    
    def set_breakdown_data(
        self, 
        granularity_breakdown: Dict[str, Any],
        craft_route_breakdown: Dict[str, Any],
        order_breakdown: Dict[str, Any]
    ) -> None:
        """设置分解数据"""
        self.breakdown_by_granularity = granularity_breakdown
        self.breakdown_by_craft_route = craft_route_breakdown
        self.breakdown_by_order = order_breakdown
        self.updated_at = datetime.now(timezone.utc)
    
    def add_bonus(self, amount: Decimal, reason: str) -> None:
        """添加奖金"""
        self.bonus_amount += amount
        self.update_total_amount()
        self.notes = f"{self.notes or ''}\n[奖金] +{amount}: {reason}".strip()
    
    def add_deduction(self, amount: Decimal, reason: str) -> None:
        """添加扣除"""
        self.deduction_amount += amount
        self.update_total_amount()
        self.notes = f"{self.notes or ''}\n[扣除] -{amount}: {reason}".strip()
    
    def get_efficiency_score(self) -> Optional[float]:
        """计算效率得分"""
        if self.total_work_duration_minutes == 0:
            return None
        # 每分钟完成的数量
        return float(self.total_completed_quantity) / self.total_work_duration_minutes
    
    def get_average_work_duration_per_instance(self) -> Optional[float]:
        """获取平均每个实例工作时长"""
        if self.total_work_instances == 0:
            return None
        return float(self.total_work_duration_minutes) / self.total_work_instances