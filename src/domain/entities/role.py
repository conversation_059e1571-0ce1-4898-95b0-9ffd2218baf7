from typing import List, TYPE_CHECKING
from sqlalchemy import String, Integer, Text, Boolean
from sqlalchemy.orm import Mapped, mapped_column, relationship
from .base import Base

if TYPE_CHECKING:
    from .permission import Permission
    from .user import User


class Role(Base):
    """Role entity for role-based access control."""
    
    __tablename__ = "roles"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    name: Mapped[str] = mapped_column(String(100), unique=True, index=True, nullable=False)
    description: Mapped[str] = mapped_column(Text, nullable=True)
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    
    # Many-to-many relationship with permissions
    permissions: Mapped[List["Permission"]] = relationship(
        "Permission",
        secondary="role_permissions",
        back_populates="roles"
    )
    
    # One-to-many relationship with users
    users: Mapped[List["User"]] = relationship(
        "User",
        back_populates="role"
    )
    
    def __repr__(self) -> str:
        return f"<Role(id={self.id}, name='{self.name}', is_active={self.is_active})>"
    
    def has_permission(self, permission_code: str) -> bool:
        """Check if role has a specific permission by code."""
        try:
            for permission in self.permissions:
                if permission.code == permission_code:
                    return True
                # For hierarchical permissions, check common patterns
                # If we have a parent permission, it grants access to child permissions
                if permission_code.startswith(permission.code + "."):
                    return True
        except Exception:
            # If we can't access permissions at all (detached instance), return False
            return False
        return False
    
    def has_any_permission(self, permission_codes: List[str]) -> bool:
        """Check if role has any of the specified permissions."""
        return any(self.has_permission(code) for code in permission_codes)
    
    def has_all_permissions(self, permission_codes: List[str]) -> bool:
        """Check if role has all of the specified permissions."""
        return all(self.has_permission(code) for code in permission_codes)
    
    def get_permission_codes(self) -> List[str]:
        """Get all permission codes assigned to this role."""
        codes = []
        try:
            for permission in self.permissions:
                codes.append(permission.code)
        except Exception:
            # If we can't access permissions at all (detached instance), return empty list
            pass
        return list(set(codes))  # Remove duplicates
    
    def add_permission(self, permission: "Permission") -> None:
        """Add a permission to this role."""
        if permission not in self.permissions:
            self.permissions.append(permission)
    
    def remove_permission(self, permission: "Permission") -> None:
        """Remove a permission from this role."""
        if permission in self.permissions:
            self.permissions.remove(permission)
    
    def activate(self) -> None:
        """Activate the role."""
        self.is_active = True
    
    def deactivate(self) -> None:
        """Deactivate the role."""
        self.is_active = False