from datetime import datetime
from typing import Optional, List
from sqlalchemy import String, DateTime, Boolean, Text
from sqlalchemy.orm import Mapped, mapped_column, relationship
from src.infrastructure.database.database import Base


class Factory(Base):
    """Factory entity representing a production facility."""
    
    __tablename__ = "factories"
    
    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True)
    name: Mapped[str] = mapped_column(String(100), unique=True, index=True)
    code: Mapped[str] = mapped_column(String(20), unique=True, index=True)
    address: Mapped[Optional[str]] = mapped_column(Text)
    phone: Mapped[Optional[str]] = mapped_column(String(20))
    email: Mapped[Optional[str]] = mapped_column(String(100))
    manager_name: Mapped[Optional[str]] = mapped_column(String(100))
    is_active: Mapped[bool] = mapped_column(Boolean, default=True)
    description: Mapped[Optional[str]] = mapped_column(Text)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    departments: Mapped[List["Department"]] = relationship("Department", back_populates="factory", cascade="all, delete-orphan")
    user_factories: Mapped[List["UserFactory"]] = relationship("UserFactory", back_populates="factory", cascade="all, delete-orphan")
    
    def __repr__(self) -> str:
        return f"<Factory(id={self.id}, name='{self.name}', code='{self.code}')>"
    
    def get_active_users(self) -> List["UserFactory"]:
        """Get all active users in this factory."""
        return [uf for uf in self.user_factories if uf.is_active()]
    
    def get_managers(self) -> List["UserFactory"]:
        """Get all managers in this factory."""
        return [uf for uf in self.user_factories if uf.can_approve_requests()]
    
    def get_pending_requests(self) -> List["UserFactory"]:
        """Get all pending join requests."""
        from src.domain.entities.user_factory import UserFactoryStatus
        return [uf for uf in self.user_factories if uf.status == UserFactoryStatus.PENDING]
    
    def get_users_by_role(self, role: "UserFactoryRole") -> List["UserFactory"]:
        """Get users by their role in this factory."""
        return [uf for uf in self.user_factories if uf.is_active() and uf.role == role]
    
    def has_manager(self, user_id: int) -> bool:
        """Check if user is a manager in this factory."""
        for uf in self.user_factories:
            if uf.user_id == user_id and uf.can_approve_requests():
                return True
        return False