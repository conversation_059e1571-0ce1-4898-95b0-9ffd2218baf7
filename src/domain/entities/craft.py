from datetime import datetime
from typing import Optional, TYPE_CHECKING, List
from sqlalchemy import String, <PERSON><PERSON><PERSON>, Integer, DateTime, Text
from sqlalchemy.orm import Mapped, mapped_column, relationship
from .base import Base

if TYPE_CHECKING:
    from .craft_route import CraftRoute


class Craft(Base):
    """Craft entity representing manufacturing processes or workflows."""
    
    __tablename__ = "crafts"
    
    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True)
    code: Mapped[str] = mapped_column(String(50), unique=True, index=True)
    name: Mapped[str] = mapped_column(String(100))
    priority: Mapped[int] = mapped_column(Integer, default=0, index=True)
    enabled: Mapped[bool] = mapped_column(Boolean, default=True, index=True)
    description: Mapped[Optional[str]] = mapped_column(Text)
    
    # Audit fields
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    craft_routes: Mapped[List["CraftRoute"]] = relationship("CraftRoute", back_populates="craft", cascade="all, delete-orphan")
    
    def __repr__(self) -> str:
        return f"<Craft(code='{self.code}', name='{self.name}', priority={self.priority})>"
    
    def is_active(self) -> bool:
        """Check if craft is active/enabled."""
        return self.enabled
    
    def get_ordered_routes(self) -> List["CraftRoute"]:
        """Get craft routes ordered by their order field."""
        return sorted(self.craft_routes, key=lambda r: r.order)