from datetime import datetime
from typing import Optional
from sqlalchemy import String, DateTime, Enum as SQLEnum, Text
from sqlalchemy.orm import Mapped, mapped_column
from src.infrastructure.database.database import Base
from src.domain.value_objects.validation_code import ValidationCodeType


class ValidationCodeRecord(Base):
    """Entity for storing validation codes in database."""
    
    __tablename__ = "validation_codes"
    
    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True)
    code: Mapped[str] = mapped_column(String(10), index=True)
    code_type: Mapped[ValidationCodeType] = mapped_column(SQLEnum(ValidationCodeType), index=True)
    identifier: Mapped[str] = mapped_column(String(100), index=True)  # phone or session_id
    expires_at: Mapped[datetime] = mapped_column(DateTime, index=True)
    is_used: Mapped[bool] = mapped_column(default=False)
    used_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    
    def __repr__(self) -> str:
        return f"<ValidationCodeRecord(id={self.id}, type='{self.code_type}', identifier='{self.identifier}')>"
    
    def is_expired(self) -> bool:
        """Check if validation code is expired."""
        return datetime.utcnow() > self.expires_at
    
    def mark_as_used(self) -> None:
        """Mark validation code as used."""
        self.is_used = True
        self.used_at = datetime.utcnow()
    
    def is_valid(self, code: str) -> bool:
        """Check if code is valid."""
        return (
            not self.is_expired() and
            not self.is_used and
            self.code.lower() == code.lower()
        )