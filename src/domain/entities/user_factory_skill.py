from datetime import datetime
from typing import Optional
from sqlalchemy import Integer, ForeignKey, DateTime, Text, String
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.sql import func
from .base import Base


class UserFactorySkill(Base):
    """Association entity for user-factory-skill relationship with proficiency level."""
    
    __tablename__ = "user_factory_skills"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    user_factory_id: Mapped[int] = mapped_column(Integer, ForeignKey("user_factories.id"), nullable=False)
    skill_id: Mapped[int] = mapped_column(Integer, ForeignKey("skills.id"), nullable=False)
    proficiency_level: Mapped[str] = mapped_column(String(20), default="BEGINNER", nullable=False)  # BEGINNER, INTERMEDIATE, ADVANCED, EXPERT
    certified: Mapped[bool] = mapped_column(default=False, nullable=False)  # Whether user is certified in this skill
    certification_date: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    certification_expires: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    notes: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    assigned_by: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey("users.id"), nullable=True)
    assigned_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    
    # Relationships
    user_factory: Mapped["UserFactory"] = relationship("UserFactory", back_populates="skills")
    skill: Mapped["Skill"] = relationship("Skill", back_populates="user_factory_skills")
    assigned_by_user: Mapped[Optional["User"]] = relationship("User", foreign_keys=[assigned_by])
    
    def __repr__(self) -> str:
        return f"<UserFactorySkill(user_factory_id={self.user_factory_id}, skill_id={self.skill_id}, proficiency='{self.proficiency_level}')>"
    
    def is_certified(self) -> bool:
        """Check if user is currently certified in this skill."""
        if not self.certified:
            return False
        if not self.certification_expires:
            return True
        return self.certification_expires > datetime.utcnow()
    
    def update_proficiency(self, level: str, updated_by: int) -> None:
        """Update proficiency level."""
        valid_levels = ["BEGINNER", "INTERMEDIATE", "ADVANCED", "EXPERT"]
        if level not in valid_levels:
            raise ValueError(f"Invalid proficiency level. Must be one of: {valid_levels}")
        
        self.proficiency_level = level
        self.assigned_by = updated_by
        self.updated_at = datetime.utcnow()
    
    def certify(self, certified_by: int, expires_at: Optional[datetime] = None) -> None:
        """Certify user in this skill."""
        self.certified = True
        self.certification_date = datetime.utcnow()
        self.certification_expires = expires_at
        self.assigned_by = certified_by
        self.updated_at = datetime.utcnow()
    
    def revoke_certification(self, revoked_by: int) -> None:
        """Revoke certification for this skill."""
        self.certified = False
        self.certification_date = None
        self.certification_expires = None
        self.assigned_by = revoked_by
        self.updated_at = datetime.utcnow()