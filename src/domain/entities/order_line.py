from datetime import datetime, timezone
from typing import Optional, TYPE_CHECKING
from sqlalchemy import String, DateTime, ForeignKey, Integer, UniqueConstraint
from sqlalchemy.orm import Mapped, mapped_column, relationship
from .base import Base

if TYPE_CHECKING:
    from .order import Order


class OrderLine(Base):
    """OrderLine entity representing individual size/quantity lines within an order."""
    
    __tablename__ = "order_lines"
    __table_args__ = (
        UniqueConstraint('order_line_no', 'factory_id', name='uq_order_line_no_factory'),
    )
    
    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True)
    
    # Factory and order relationship
    factory_id: Mapped[int] = mapped_column(ForeignKey("factories.id"), index=True, comment="工厂ID")
    order_id: Mapped[int] = mapped_column(ForeignKey("orders.id"), index=True, comment="订单ID")
    order_no: Mapped[str] = mapped_column(String(100), index=True, comment="订单号")
    
    # Line identification
    order_line_no: Mapped[str] = mapped_column(String(150), index=True, comment="订单行号 {order_no}_{size}")
    size: Mapped[str] = mapped_column(String(20), index=True, comment="尺码")
    amount: Mapped[int] = mapped_column(Integer, default=0, comment="数量")
    
    # Production tracking
    produced_amount: Mapped[int] = mapped_column(Integer, default=0, comment="已生产数量")
    completed_amount: Mapped[int] = mapped_column(Integer, default=0, comment="已完成数量")
    current_craft_route_code: Mapped[Optional[str]] = mapped_column(String(50), nullable=True, index=True, comment="当前工艺路线代码")
    
    # Additional information
    notes: Mapped[Optional[str]] = mapped_column(String(500), comment="备注")
    
    # Audit fields
    created_at: Mapped[datetime] = mapped_column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
    
    # Relationships
    order: Mapped["Order"] = relationship("Order", back_populates="order_lines", foreign_keys=[order_id])
    
    def __repr__(self) -> str:
        return f"<OrderLine(order_line_no='{self.order_line_no}', size='{self.size}', amount={self.amount})>"
    
    @classmethod
    def generate_order_line_no(cls, order_no: str, size: str) -> str:
        """Generate order line number in format {order_no}_{size}."""
        return f"{order_no}_{size}"
    
    def is_completed(self) -> bool:
        """Check if this order line is fully completed."""
        return self.completed_amount >= self.amount
    
    def is_in_progress(self) -> bool:
        """Check if this order line is in progress."""
        return self.produced_amount > 0 and not self.is_completed()
    
    def get_completion_percentage(self) -> float:
        """Get completion percentage for this order line."""
        if self.amount == 0:
            return 0.0
        return (self.completed_amount / self.amount) * 100.0
    
    def get_production_percentage(self) -> float:
        """Get production percentage for this order line."""
        if self.amount == 0:
            return 0.0
        return (self.produced_amount / self.amount) * 100.0
    
    def update_production(self, produced_qty: int) -> None:
        """Update produced quantity."""
        self.produced_amount = min(produced_qty, self.amount)
        self.updated_at = datetime.now(timezone.utc)
    
    def update_completion(self, completed_qty: int) -> None:
        """Update completed quantity."""
        self.completed_amount = min(completed_qty, self.amount)
        # Ensure completed amount doesn't exceed produced amount
        if self.completed_amount > self.produced_amount:
            self.produced_amount = self.completed_amount
        self.updated_at = datetime.now(timezone.utc)
    
    def add_production(self, additional_qty: int) -> None:
        """Add to produced quantity."""
        new_produced = self.produced_amount + additional_qty
        self.update_production(new_produced)
    
    def add_completion(self, additional_qty: int) -> None:
        """Add to completed quantity."""
        new_completed = self.completed_amount + additional_qty
        self.update_completion(new_completed)
    
    def get_remaining_amount(self) -> int:
        """Get remaining amount to be produced."""
        return max(0, self.amount - self.produced_amount)
    
    def get_remaining_completion_amount(self) -> int:
        """Get remaining amount to be completed."""
        return max(0, self.amount - self.completed_amount)