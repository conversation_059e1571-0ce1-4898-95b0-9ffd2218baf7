from datetime import datetime
from typing import Optional, List
from sqlalchemy import String, DateTime, Boolean, Text, ForeignKey
from sqlalchemy.orm import Mapped, mapped_column, relationship
from src.infrastructure.database.database import Base


class Department(Base):
    """Department entity representing a department within a factory."""
    
    __tablename__ = "departments"
    
    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True)
    name: Mapped[str] = mapped_column(String(100), index=True)
    code: Mapped[str] = mapped_column(String(20), index=True)
    factory_id: Mapped[int] = mapped_column(ForeignKey("factories.id"), index=True)
    manager_name: Mapped[Optional[str]] = mapped_column(String(100))
    phone: Mapped[Optional[str]] = mapped_column(String(20))
    email: Mapped[Optional[str]] = mapped_column(String(100))
    location: Mapped[Optional[str]] = mapped_column(String(200))
    is_active: Mapped[bool] = mapped_column(Boolean, default=True)
    description: Mapped[Optional[str]] = mapped_column(Text)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    factory: Mapped["Factory"] = relationship("Factory", back_populates="departments")
    
    def __repr__(self) -> str:
        return f"<Department(id={self.id}, name='{self.name}', code='{self.code}', factory_id={self.factory_id})>"
    
    def get_users(self) -> List["UserFactory"]:
        """Get all users in this department through UserFactory association."""
        # This will be implemented through UserFactory queries
        pass