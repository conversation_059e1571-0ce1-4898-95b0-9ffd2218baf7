from datetime import datetime
from typing import Optional, TYPE_CHECKING
from enum import Enum
from sqlalchemy import String, DateTime, ForeignKey, Text, Enum as SQLEnum
from sqlalchemy.orm import Mapped, mapped_column, relationship
from src.infrastructure.database.database import Base

if TYPE_CHECKING:
    from .user import User
    from .factory import Factory
    from .department import Department
    from .user_factory_skill import UserFactorySkill


class UserFactoryStatus(Enum):
    """Status of user's relationship with factory."""
    PENDING = "pending"      # Waiting for manager approval
    APPROVED = "approved"    # Approved by manager, active worker
    REJECTED = "rejected"    # Rejected by manager
    SUSPENDED = "suspended"  # Temporarily suspended
    RESIGNED = "resigned"    # User resigned from factory


class UserFactoryRole(Enum):
    """User's role in the factory."""
    WORKER = "worker"           # Regular worker
    SUPERVISOR = "supervisor"   # Supervisor
    MANAGER = "manager"         # Factory manager
    ADMIN = "admin"            # Factory admin


class UserFactory(Base):
    """Association entity for many-to-many relationship between User and Factory."""
    
    __tablename__ = "user_factories"
    
    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True)
    user_id: Mapped[int] = mapped_column(ForeignKey("users.id"), index=True)
    factory_id: Mapped[int] = mapped_column(ForeignKey("factories.id"), index=True)
    department_id: Mapped[Optional[int]] = mapped_column(ForeignKey("departments.id"), index=True)
    
    # Status and role information
    status: Mapped[UserFactoryStatus] = mapped_column(SQLEnum(UserFactoryStatus), default=UserFactoryStatus.PENDING, index=True)
    role: Mapped[UserFactoryRole] = mapped_column(SQLEnum(UserFactoryRole), default=UserFactoryRole.WORKER)
    
    # Employment details
    employee_id: Mapped[Optional[str]] = mapped_column(String(50), index=True)  # Factory-specific employee ID
    position: Mapped[Optional[str]] = mapped_column(String(100))
    start_date: Mapped[Optional[datetime]] = mapped_column(DateTime)
    end_date: Mapped[Optional[datetime]] = mapped_column(DateTime)
    
    # Approval workflow
    requested_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    approved_by: Mapped[Optional[int]] = mapped_column(ForeignKey("users.id"))  # Manager who approved
    approved_at: Mapped[Optional[datetime]] = mapped_column(DateTime)
    rejected_reason: Mapped[Optional[str]] = mapped_column(Text)
    
    # Audit fields
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    user: Mapped["User"] = relationship("User", foreign_keys=[user_id], back_populates="user_factories")
    factory: Mapped["Factory"] = relationship("Factory", back_populates="user_factories")
    department: Mapped[Optional["Department"]] = relationship("Department")
    approver: Mapped[Optional["User"]] = relationship("User", foreign_keys=[approved_by])
    skills: Mapped[list["UserFactorySkill"]] = relationship("UserFactorySkill", back_populates="user_factory")
    
    def __repr__(self) -> str:
        return f"<UserFactory(user_id={self.user_id}, factory_id={self.factory_id}, status='{self.status}')>"
    
    def is_active(self) -> bool:
        """Check if user is actively working in this factory."""
        return self.status == UserFactoryStatus.APPROVED
    
    def is_manager(self) -> bool:
        """Check if user is a manager in this factory."""
        return self.role in [UserFactoryRole.MANAGER, UserFactoryRole.ADMIN]
    
    def can_approve_requests(self) -> bool:
        """Check if user can approve join requests."""
        return self.is_active() and self.is_manager()
    
    def approve(self, approver_id: int, start_date: Optional[datetime] = None) -> None:
        """Approve the factory join request."""
        self.status = UserFactoryStatus.APPROVED
        self.approved_by = approver_id
        self.approved_at = datetime.utcnow()
        self.start_date = start_date or datetime.utcnow()
        self.updated_at = datetime.utcnow()
    
    def reject(self, approver_id: int, reason: str) -> None:
        """Reject the factory join request."""
        self.status = UserFactoryStatus.REJECTED
        self.approved_by = approver_id
        self.approved_at = datetime.utcnow()
        self.rejected_reason = reason
        self.updated_at = datetime.utcnow()
    
    def suspend(self, reason: Optional[str] = None) -> None:
        """Suspend user from factory."""
        self.status = UserFactoryStatus.SUSPENDED
        if reason:
            self.rejected_reason = reason
        self.updated_at = datetime.utcnow()
    
    def resign(self, end_date: Optional[datetime] = None) -> None:
        """User resigns from factory."""
        self.status = UserFactoryStatus.RESIGNED
        self.end_date = end_date or datetime.utcnow()
        self.updated_at = datetime.utcnow()