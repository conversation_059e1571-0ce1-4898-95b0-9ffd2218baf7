from datetime import datetime, timezone
from typing import Optional, TYPE_CHECKING
from enum import Enum
from sqlalchemy import String, Integer, DateTime, Text, Enum as SQLEnum, ForeignKey, UniqueConstraint
from sqlalchemy.orm import Mapped, mapped_column, relationship
from .base import Base

if TYPE_CHECKING:
    from .order import Order
    from .user import User
    from .order_part import OrderPart


class BundleStatus(Enum):
    """扎生产状态枚举"""
    PLANNED = "planned"           # 计划中
    CUTTING = "cutting"           # 裁剪中
    CUT_COMPLETED = "cut_completed"  # 裁剪完成
    SEWING = "sewing"            # 缝制中
    SEW_COMPLETED = "sew_completed"  # 缝制完成
    QUALITY_CHECK = "quality_check"  # 质检中
    COMPLETED = "completed"       # 已完成
    REWORK = "rework"            # 返工
    ON_HOLD = "on_hold"          # 暂停
    CANCELLED = "cancelled"       # 已取消


class OrderBundle(Base):
    """订单扎实体 - 单一尺码的生产单位"""
    
    __tablename__ = "order_bundles"
    __table_args__ = (
        UniqueConstraint('order_bundle_no', 'factory_id', 'order_no', name='uq_order_bundle_no_factory_order'),
    )
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    
    # Factory and order relationship
    factory_id: Mapped[int] = mapped_column(ForeignKey("factories.id"), index=True, comment="工厂ID")
    order_no: Mapped[str] = mapped_column(String(100), index=True, comment="订单号")
    
    # 基本信息
    order_bundle_no: Mapped[str] = mapped_column(String(100), index=True, nullable=False, comment="订单扎号")
    order_part_no: Mapped[str] = mapped_column(String(100), index=True, nullable=False, comment="订单部位号")
    order_part_id: Mapped[int] = mapped_column(Integer, ForeignKey("order_parts.id"), comment="订单部位ID")
    skc_no: Mapped[str] = mapped_column(String(100), nullable=False, comment="款号")
    color: Mapped[str] = mapped_column(String(50), nullable=False, comment="颜色")
    
    # 扎信息
    size: Mapped[str] = mapped_column(String(20), nullable=False, comment="尺码")
    bundle_sequence: Mapped[int] = mapped_column(Integer, nullable=False, comment="扎序号(同一部位、同一尺码内)")
    
    # 数量信息
    quantity: Mapped[int] = mapped_column(Integer, nullable=False, comment="扎件数")
    completed_quantity: Mapped[int] = mapped_column(Integer, default=0, comment="已完成件数")
    defective_quantity: Mapped[int] = mapped_column(Integer, default=0, comment="次品件数")
    rework_quantity: Mapped[int] = mapped_column(Integer, default=0, comment="返工件数")
    
    # 状态和进度
    status: Mapped[BundleStatus] = mapped_column(SQLEnum(BundleStatus), default=BundleStatus.PLANNED, comment="扎状态")
    progress_percentage: Mapped[int] = mapped_column(Integer, default=0, comment="完成百分比")
    current_craft_route_code: Mapped[Optional[str]] = mapped_column(String(50), nullable=True, index=True, comment="当前工艺路线代码")
    
    # 时间信息
    planned_start_date: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True, comment="计划开始时间")
    actual_start_date: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True, comment="实际开始时间")
    planned_end_date: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True, comment="计划完成时间")
    actual_end_date: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True, comment="实际完成时间")
    
    # 切割完成时间
    cut_start_date: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True, comment="裁剪开始时间")
    cut_end_date: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True, comment="裁剪完成时间")
    
    # 缝制时间
    sew_start_date: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True, comment="缝制开始时间")
    sew_end_date: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True, comment="缝制完成时间")
    
    # 质检时间
    qc_start_date: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True, comment="质检开始时间")
    qc_end_date: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True, comment="质检完成时间")
    
    # 工作人员
    cutter_user_id: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey("users.id"), nullable=True, comment="裁剪工ID")
    sewer_user_id: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey("users.id"), nullable=True, comment="缝制工ID")
    qc_user_id: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey("users.id"), nullable=True, comment="质检员ID")
    
    # 机床信息
    cutting_machine: Mapped[Optional[str]] = mapped_column(String(50), nullable=True, comment="裁剪机床")
    sewing_machine: Mapped[Optional[str]] = mapped_column(String(50), nullable=True, comment="缝制机床")
    
    # 质量信息
    quality_level: Mapped[Optional[str]] = mapped_column(String(20), nullable=True, comment="质量等级(A/B/C)")
    quality_notes: Mapped[Optional[str]] = mapped_column(Text, nullable=True, comment="质量备注")
    
    # 描述和备注
    notes: Mapped[Optional[str]] = mapped_column(Text, nullable=True, comment="备注")
    
    # 系统字段
    created_at: Mapped[datetime] = mapped_column(DateTime, default=lambda: datetime.now(timezone.utc), comment="创建时间")
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc), comment="更新时间")
    
    # 关系
    order_part: Mapped["OrderPart"] = relationship("OrderPart", back_populates="order_bundles")
    cutter: Mapped[Optional["User"]] = relationship("User", foreign_keys=[cutter_user_id])
    sewer: Mapped[Optional["User"]] = relationship("User", foreign_keys=[sewer_user_id])
    qc_user: Mapped[Optional["User"]] = relationship("User", foreign_keys=[qc_user_id])
    
    def __repr__(self) -> str:
        return f"<OrderBundle(id={self.id}, order_bundle_no='{self.order_bundle_no}', size='{self.size}', quantity={self.quantity})>"
    
    @classmethod
    def generate_order_bundle_no(cls, order_part_no: str, size: str, bundle_sequence: int) -> str:
        """生成订单扎号: {订单部位号}_{尺码}_{扎序号}"""
        return f"{order_part_no}_{size}_{bundle_sequence:02d}"
    
    def calculate_progress(self) -> int:
        """计算完成进度百分比"""
        if self.quantity == 0:
            return 0
        return min(100, int((self.completed_quantity / self.quantity) * 100))
    
    def update_progress(self) -> None:
        """更新进度百分比"""
        self.progress_percentage = self.calculate_progress()
    
    def is_completed(self) -> bool:
        """检查扎是否已完成"""
        return self.status == BundleStatus.COMPLETED or self.completed_quantity >= self.quantity
    
    def can_start_cutting(self) -> bool:
        """检查是否可以开始裁剪"""
        return self.status == BundleStatus.PLANNED and self.quantity > 0
    
    def start_cutting(self, cutter_user_id: Optional[int] = None, cutting_machine: Optional[str] = None) -> None:
        """开始裁剪"""
        if self.can_start_cutting():
            self.status = BundleStatus.CUTTING
            self.actual_start_date = datetime.now(timezone.utc)
            self.cut_start_date = datetime.now(timezone.utc)
            if cutter_user_id:
                self.cutter_user_id = cutter_user_id
            if cutting_machine:
                self.cutting_machine = cutting_machine
    
    def complete_cutting(self) -> None:
        """完成裁剪"""
        if self.status == BundleStatus.CUTTING:
            self.status = BundleStatus.CUT_COMPLETED
            self.cut_end_date = datetime.now(timezone.utc)
    
    def start_sewing(self, sewer_user_id: Optional[int] = None, sewing_machine: Optional[str] = None) -> None:
        """开始缝制"""
        if self.status == BundleStatus.CUT_COMPLETED:
            self.status = BundleStatus.SEWING
            self.sew_start_date = datetime.now(timezone.utc)
            if sewer_user_id:
                self.sewer_user_id = sewer_user_id
            if sewing_machine:
                self.sewing_machine = sewing_machine
    
    def complete_sewing(self) -> None:
        """完成缝制"""
        if self.status == BundleStatus.SEWING:
            self.status = BundleStatus.SEW_COMPLETED
            self.sew_end_date = datetime.now(timezone.utc)
    
    def start_quality_check(self, qc_user_id: Optional[int] = None) -> None:
        """开始质检"""
        if self.status == BundleStatus.SEW_COMPLETED:
            self.status = BundleStatus.QUALITY_CHECK
            self.qc_start_date = datetime.now(timezone.utc)
            if qc_user_id:
                self.qc_user_id = qc_user_id
    
    def complete_quality_check(self, quality_level: str, defective_quantity: int = 0, quality_notes: Optional[str] = None) -> None:
        """完成质检"""
        if self.status == BundleStatus.QUALITY_CHECK:
            self.status = BundleStatus.COMPLETED
            self.qc_end_date = datetime.now(timezone.utc)
            self.actual_end_date = datetime.now(timezone.utc)
            self.quality_level = quality_level
            self.defective_quantity = defective_quantity
            self.completed_quantity = self.quantity - defective_quantity
            self.quality_notes = quality_notes
            self.update_progress()
    
    def send_to_rework(self, rework_quantity: int, notes: Optional[str] = None) -> None:
        """发送返工"""
        self.status = BundleStatus.REWORK
        self.rework_quantity = rework_quantity
        if notes:
            self.notes = notes
    
    def get_good_quantity(self) -> int:
        """获取良品数量"""
        return self.completed_quantity - self.defective_quantity
    
    def get_processing_time(self) -> Optional[int]:
        """获取总加工时长(分钟)"""
        if self.actual_start_date and self.actual_end_date:
            return int((self.actual_end_date - self.actual_start_date).total_seconds() / 60)
        return None
    
    def get_cutting_time(self) -> Optional[int]:
        """获取裁剪时长(分钟)"""
        if self.cut_start_date and self.cut_end_date:
            return int((self.cut_end_date - self.cut_start_date).total_seconds() / 60)
        return None
    
    def get_sewing_time(self) -> Optional[int]:
        """获取缝制时长(分钟)"""
        if self.sew_start_date and self.sew_end_date:
            return int((self.sew_end_date - self.sew_start_date).total_seconds() / 60)
        return None
    
    def get_qc_time(self) -> Optional[int]:
        """获取质检时长(分钟)"""
        if self.qc_start_date and self.qc_end_date:
            return int((self.qc_end_date - self.qc_start_date).total_seconds() / 60)
        return None