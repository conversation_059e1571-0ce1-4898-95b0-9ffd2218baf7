from sqlalchemy import Table, <PERSON>umn, Integer, ForeignKey, DateTime
from sqlalchemy.sql import func
from .base import Base


# Association table for many-to-many relationship between roles and permissions
role_permissions = Table(
    "role_permissions",
    Base.metadata,
    Column("id", Integer, primary_key=True, index=True),
    Column("role_id", Integer, ForeignKey("roles.id"), nullable=False),
    <PERSON>umn("permission_id", Integer, ForeignKey("permissions.id"), nullable=False),
    Column("created_at", DateTime(timezone=True), server_default=func.now(), nullable=False),
    <PERSON>umn("updated_at", DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False),
)