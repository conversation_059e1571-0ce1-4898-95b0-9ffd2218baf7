from datetime import datetime, timezone
from typing import Optional, TYPE_CHECKING, List
from enum import Enum
from sqlalchemy import String, DateTime, ForeignKey, Text, Numeric, Enum as SQLEnum, UniqueConstraint
from sqlalchemy.orm import Mapped, mapped_column, relationship
from .base import Base

if TYPE_CHECKING:
    from .user import User
    from .craft import Craft
    from .craft_route import CraftRoute
    from .order_line import OrderLine
    from .order_part import OrderPart


class OrderStatus(Enum):
    """Order status enumeration."""
    PENDING = "pending"           # 待处理
    IN_PROGRESS = "in_progress"   # 进行中
    COMPLETED = "completed"       # 已完成
    CANCELLED = "cancelled"       # 已取消
    ON_HOLD = "on_hold"          # 暂停
    DELAYED = "delayed"          # 延期


class Order(Base):
    """Order entity representing production orders."""
    
    __tablename__ = "orders"
    __table_args__ = (
        UniqueConstraint('order_no', 'factory_id', name='uq_order_no_factory'),
    )
    
    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True)

    factory_id: Mapped[int] = mapped_column(ForeignKey("factories.id"), index=True, comment="工厂ID")
    
    # Product and order identification
    skc_no: Mapped[str] = mapped_column(String(100), index=True, comment="款号")
    external_skc_no: Mapped[Optional[str]] = mapped_column(String(100), index=True, comment="外部款号")
    order_no: Mapped[str] = mapped_column(String(100), index=True, comment="订单号")
    external_order_no: Mapped[Optional[str]] = mapped_column(String(100), index=True, comment="外部订单号")
    external_order_no2: Mapped[Optional[str]] = mapped_column(String(100), index=True, comment="外部订单号2")
    
    # Financial information
    total_amount: Mapped[int] = mapped_column(default=0, comment="总数量")
    cost: Mapped[Optional[Numeric]] = mapped_column(Numeric(10, 2), comment="成本")
    price: Mapped[Optional[Numeric]] = mapped_column(Numeric(10, 2), comment="价格")
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(DateTime, default=lambda: datetime.now(timezone.utc))
    started_at: Mapped[Optional[datetime]] = mapped_column(DateTime, comment="订单开始时间")
    expect_finished_at: Mapped[Optional[datetime]] = mapped_column(DateTime, comment="预期完成时间")
    finished_at: Mapped[Optional[datetime]] = mapped_column(DateTime, comment="实际完成时间")
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
    
    # Ownership and workflow
    owner_user_id: Mapped[Optional[int]] = mapped_column(ForeignKey("users.id"), index=True, comment="负责人用户ID")
    status: Mapped[OrderStatus] = mapped_column(SQLEnum(OrderStatus), default=OrderStatus.PENDING, index=True)
    current_craft: Mapped[Optional[str]] = mapped_column(String(50), ForeignKey("crafts.code"), index=True, comment="当前工艺")
    current_craft_route: Mapped[Optional[int]] = mapped_column(ForeignKey("craft_routes.id"), index=True, comment="当前工艺路线")
    
    # Additional information
    description: Mapped[Optional[str]] = mapped_column(Text, comment="订单描述")
    notes: Mapped[Optional[str]] = mapped_column(Text, comment="备注")
    
    # Relationships
    owner: Mapped[Optional["User"]] = relationship("User", foreign_keys=[owner_user_id])
    craft: Mapped[Optional["Craft"]] = relationship("Craft", foreign_keys=[current_craft])
    craft_route: Mapped[Optional["CraftRoute"]] = relationship("CraftRoute", foreign_keys=[current_craft_route])
    order_lines: Mapped[List["OrderLine"]] = relationship("OrderLine", back_populates="order", cascade="all, delete-orphan")
    order_parts: Mapped[List["OrderPart"]] = relationship("OrderPart", back_populates="order", cascade="all, delete-orphan")
    
    def __repr__(self) -> str:
        return f"<Order(order_no='{self.order_no}', skc_no='{self.skc_no}', status='{self.status}')>"
    
    def is_active(self) -> bool:
        """Check if order is in active status."""
        return self.status in [OrderStatus.PENDING, OrderStatus.IN_PROGRESS, OrderStatus.ON_HOLD, OrderStatus.DELAYED]
    
    def is_completed(self) -> bool:
        """Check if order is completed."""
        return self.status == OrderStatus.COMPLETED
    
    def is_cancelled(self) -> bool:
        """Check if order is cancelled."""
        return self.status == OrderStatus.CANCELLED
    
    def start_order(self) -> None:
        """Start the order."""
        if self.status == OrderStatus.PENDING:
            self.status = OrderStatus.IN_PROGRESS
            self.started_at = datetime.now(timezone.utc)
            self.updated_at = datetime.now(timezone.utc)
    
    def complete_order(self) -> None:
        """Complete the order."""
        self.status = OrderStatus.COMPLETED
        self.finished_at = datetime.now(timezone.utc)
        self.updated_at = datetime.now(timezone.utc)
    
    def cancel_order(self) -> None:
        """Cancel the order."""
        self.status = OrderStatus.CANCELLED
        self.updated_at = datetime.now(timezone.utc)
    
    def hold_order(self) -> None:
        """Put order on hold."""
        if self.status in [OrderStatus.PENDING, OrderStatus.IN_PROGRESS]:
            self.status = OrderStatus.ON_HOLD
            self.updated_at = datetime.now(timezone.utc)
    
    def resume_order(self) -> None:
        """Resume order from hold."""
        if self.status == OrderStatus.ON_HOLD:
            self.status = OrderStatus.IN_PROGRESS
            self.updated_at = datetime.now(timezone.utc)
    
    def delay_order(self) -> None:
        """Mark order as delayed."""
        if self.status == OrderStatus.IN_PROGRESS:
            self.status = OrderStatus.DELAYED
            self.updated_at = datetime.now(timezone.utc)
    
    def update_craft_progress(self, craft_code: str, craft_route_id: Optional[int] = None) -> None:
        """Update current craft and route progress."""
        self.current_craft = craft_code
        self.current_craft_route = craft_route_id
        self.updated_at = datetime.now(timezone.utc)
    
    def calculate_total_amount(self) -> int:
        """Calculate total amount from order lines."""
        return sum(line.amount for line in self.order_lines)
    
    def get_completion_percentage(self) -> float:
        """Get order completion percentage based on current craft progress."""
        # This would be implemented based on your business logic
        # For now, return based on status
        if self.status == OrderStatus.COMPLETED:
            return 100.0
        elif self.status == OrderStatus.IN_PROGRESS:
            return 50.0  # This could be calculated based on craft route progress
        elif self.status in [OrderStatus.DELAYED, OrderStatus.ON_HOLD]:
            return 25.0
        else:
            return 0.0