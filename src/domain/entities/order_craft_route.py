from datetime import datetime
from typing import Optional, TYPE_CHECKING, List
from decimal import Decimal
from sqlalchemy import String, Integer, DateTime, ForeignKey, Text, Boolean, JSON, DECIMAL
from sqlalchemy.orm import Mapped, mapped_column, relationship
from .base import Base

if TYPE_CHECKING:
    from .order_craft import OrderCraft
    from .skill import Skill
    from .user import User
    from .order_craft_route_instance import OrderCraftRouteInstance


class OrderCraftRoute(Base):
    """OrderCraftRoute entity representing specific skill routes within an order's craft workflow."""
    
    __tablename__ = "order_craft_routes"
    
    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True)

    factory_id: Mapped[int] = mapped_column(ForeignKey("factories.id"), index=True, comment="Factory ID this route belongs to")
    
    # Parent order craft
    order_craft_id: Mapped[int] = mapped_column(Integer, ForeignKey("order_crafts.id"), index=True)
    
    # Skill and workflow information
    skill_code: Mapped[str] = mapped_column(String(50), Foreign<PERSON>ey("skills.code"), index=True)
    name: Mapped[Optional[str]] = mapped_column(String(100), comment="Route name for easy reference")
    code: Mapped[Optional[str]] = mapped_column(String(50), comment="Route code for easy reference")

    order: Mapped[int] = mapped_column(Integer, default=0, index=True, comment="Order sequence within the craft")
    
    # Configuration from original craft route
    measurement_types: Mapped[Optional[List[str]]] = mapped_column(JSON, comment="Available measurement types for this route")
    registration_types: Mapped[Optional[List[str]]] = mapped_column(JSON, comment="Available registration types for this route")
    
    # Order-specific configuration
    is_required: Mapped[bool] = mapped_column(Boolean, default=True, comment="Whether this route is required for this order")
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, comment="Whether this route is currently active")
    
    # Status and progress
    status: Mapped[str] = mapped_column(String(20), default="pending", index=True, comment="Status: pending, in_progress, completed, skipped")
    started_at: Mapped[Optional[datetime]] = mapped_column(DateTime, comment="When this route started")
    completed_at: Mapped[Optional[datetime]] = mapped_column(DateTime, comment="When this route was completed")
    
    # Assignment and execution
    assigned_user_id: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey("users.id"), comment="User assigned to this route")
    estimated_duration_minutes: Mapped[Optional[int]] = mapped_column(Integer, comment="Estimated duration in minutes")
    actual_duration_minutes: Mapped[Optional[int]] = mapped_column(Integer, comment="Actual duration in minutes")
    
    # Pricing and cost information
    price: Mapped[Optional[Decimal]] = mapped_column(DECIMAL(10, 2), comment="Unit price for this route")
    total_cost: Mapped[Optional[Decimal]] = mapped_column(DECIMAL(10, 2), comment="Total cost for this route")
    
    # Quality and measurement data
    quality_score: Mapped[Optional[int]] = mapped_column(Integer, comment="Quality score (0-100)")
    measurement_data: Mapped[Optional[dict]] = mapped_column(JSON, comment="Measurement data collected during this route")
    registration_data: Mapped[Optional[dict]] = mapped_column(JSON, comment="Registration data collected during this route")
    
    # Notes and feedback
    notes: Mapped[Optional[str]] = mapped_column(Text, comment="Notes specific to this order-craft-route")
    completion_notes: Mapped[Optional[str]] = mapped_column(Text, comment="Notes upon completion")
    
    # Audit fields
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    order_craft: Mapped["OrderCraft"] = relationship("OrderCraft", back_populates="order_craft_routes")
    skill: Mapped["Skill"] = relationship("Skill", foreign_keys=[skill_code])
    assigned_user: Mapped[Optional["User"]] = relationship("User", foreign_keys=[assigned_user_id])
    
    def __repr__(self) -> str:
        return f"<OrderCraftRoute(order_craft_id={self.order_craft_id}, skill_code='{self.skill_code}', order={self.order}, status='{self.status}')>"
    
    def start_route(self, user_id: Optional[int] = None) -> None:
        """Start this craft route."""
        if self.status == "pending":
            self.status = "in_progress"
            self.started_at = datetime.utcnow()
            self.updated_at = datetime.utcnow()
            if user_id:
                self.assigned_user_id = user_id
    
    def complete_route(self, quality_score: Optional[int] = None, completion_notes: Optional[str] = None) -> None:
        """Complete this craft route."""
        if self.status == "in_progress":
            self.status = "completed"
            self.completed_at = datetime.utcnow()
            self.updated_at = datetime.utcnow()
            
            if quality_score is not None:
                self.quality_score = quality_score
            
            if completion_notes:
                self.completion_notes = completion_notes
            
            # Calculate actual duration if we have start time
            if self.started_at:
                duration = self.completed_at - self.started_at
                self.actual_duration_minutes = int(duration.total_seconds() / 60)
    
    def skip_route(self, reason: Optional[str] = None) -> None:
        """Skip this craft route."""
        self.status = "skipped"
        self.completed_at = datetime.utcnow()
        self.updated_at = datetime.utcnow()
        if reason:
            self.notes = f"{self.notes or ''}\nSkipped: {reason}".strip()
    
    def reset_route(self) -> None:
        """Reset this route back to pending status."""
        self.status = "pending"
        self.started_at = None
        self.completed_at = None
        self.actual_duration_minutes = None
        self.quality_score = None
        self.completion_notes = None
        self.measurement_data = None
        self.registration_data = None
        self.updated_at = datetime.utcnow()
    
    def assign_user(self, user_id: int) -> None:
        """Assign a user to this route."""
        self.assigned_user_id = user_id
        self.updated_at = datetime.utcnow()
    
    def unassign_user(self) -> None:
        """Unassign the current user from this route."""
        self.assigned_user_id = None
        self.updated_at = datetime.utcnow()
    
    def is_completed(self) -> bool:
        """Check if this route is completed."""
        return self.status == "completed"
    
    def is_in_progress(self) -> bool:
        """Check if this route is in progress."""
        return self.status == "in_progress"
    
    def is_pending(self) -> bool:
        """Check if this route is pending."""
        return self.status == "pending"
    
    def can_start(self) -> bool:
        """Check if this route can be started."""
        return self.is_active and self.status == "pending"
    
    def get_measurement_types(self) -> List[str]:
        """Get list of available measurement types."""
        return self.measurement_types or []
    
    def get_registration_types(self) -> List[str]:
        """Get list of available registration types."""
        return self.registration_types or []
    
    def set_measurement_data(self, data: dict) -> None:
        """Set measurement data for this route."""
        self.measurement_data = data
        self.updated_at = datetime.utcnow()
    
    def set_registration_data(self, data: dict) -> None:
        """Set registration data for this route."""
        self.registration_data = data
        self.updated_at = datetime.utcnow()
    
    def get_measurement_data(self) -> dict:
        """Get measurement data."""
        return self.measurement_data or {}
    
    def get_registration_data(self) -> dict:
        """Get registration data."""
        return self.registration_data or {}