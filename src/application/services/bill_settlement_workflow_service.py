from typing import List, Dict, Any, Optional
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from src.domain.entities.daily_worker_bill import DailyWorkerBill, BillStatus, PaymentMethod
from src.domain.entities.order_craft_route_instance import OrderCraftRouteInstance, SettlementStatus
from src.application.interfaces.daily_worker_bill_repository_interface import DailyWorkerBillRepositoryInterface
from src.application.interfaces.order_craft_route_instance_repository_interface import OrderCraftRouteInstanceRepositoryInterface


class BillSettlementWorkflowService:
    """账单结算工作流服务"""
    
    def __init__(
        self,
        daily_worker_bill_repository: DailyWorkerBillRepositoryInterface,
        order_craft_route_instance_repository: OrderCraftRouteInstanceRepositoryInterface
    ):
        self.daily_worker_bill_repository = daily_worker_bill_repository
        self.order_craft_route_instance_repository = order_craft_route_instance_repository
    
    async def submit_bill_for_review(
        self,
        bill_id: int,
        session: AsyncSession
    ) -> Dict[str, Any]:
        """提交账单审核"""
        
        result = {"success": False, "message": "", "bill": None}
        
        bill = await self.daily_worker_bill_repository.get_by_id(bill_id, session)
        if not bill:
            result["message"] = "账单不存在"
            return result
        
        if bill.status != BillStatus.DRAFT:
            result["message"] = f"账单状态为 {bill.status.value}，无法提交审核"
            return result
        
        # 验证账单数据完整性
        validation_result = await self._validate_bill_for_review(bill, session)
        if not validation_result["valid"]:
            result["message"] = validation_result["message"]
            return result
        
        # 提交审核
        bill.submit_for_review()
        updated_bill = await self.daily_worker_bill_repository.update(bill, session)
        
        result["success"] = True
        result["message"] = "账单已提交审核"
        result["bill"] = updated_bill
        
        return result
    
    async def approve_bill(
        self,
        bill_id: int,
        reviewer_user_id: int,
        review_notes: Optional[str] = None,
        session: AsyncSession = None
    ) -> Dict[str, Any]:
        """审核通过账单"""
        
        result = {"success": False, "message": "", "bill": None}
        
        bill = await self.daily_worker_bill_repository.get_by_id(bill_id, session)
        if not bill:
            result["message"] = "账单不存在"
            return result
        
        if bill.status != BillStatus.PENDING:
            result["message"] = f"账单状态为 {bill.status.value}，无法审核"
            return result
        
        # 审核通过
        bill.approve_bill(reviewer_user_id, review_notes)
        updated_bill = await self.daily_worker_bill_repository.update(bill, session)
        
        result["success"] = True
        result["message"] = "账单审核通过"
        result["bill"] = updated_bill
        
        return result
    
    async def reject_bill(
        self,
        bill_id: int,
        reviewer_user_id: int,
        rejection_reason: str,
        session: AsyncSession
    ) -> Dict[str, Any]:
        """拒绝账单"""
        
        result = {"success": False, "message": "", "bill": None}
        
        bill = await self.daily_worker_bill_repository.get_by_id(bill_id, session)
        if not bill:
            result["message"] = "账单不存在"
            return result
        
        if bill.status != BillStatus.PENDING:
            result["message"] = f"账单状态为 {bill.status.value}，无法拒绝"
            return result
        
        # 拒绝账单
        bill.reject_bill(reviewer_user_id, rejection_reason)
        updated_bill = await self.daily_worker_bill_repository.update(bill, session)
        
        # 重置相关实例的结算状态
        await self._reset_instance_settlement_status(bill, session)
        
        result["success"] = True
        result["message"] = "账单已拒绝"
        result["bill"] = updated_bill
        
        return result
    
    async def mark_bill_as_paid(
        self,
        bill_id: int,
        payer_user_id: int,
        payment_method: PaymentMethod,
        payment_reference: Optional[str] = None,
        payment_notes: Optional[str] = None,
        session: AsyncSession = None
    ) -> Dict[str, Any]:
        """标记账单为已支付"""
        
        result = {"success": False, "message": "", "bill": None}
        
        bill = await self.daily_worker_bill_repository.get_by_id(bill_id, session)
        if not bill:
            result["message"] = "账单不存在"
            return result
        
        if bill.status != BillStatus.APPROVED:
            result["message"] = f"账单状态为 {bill.status.value}，无法支付"
            return result
        
        # 标记为已支付
        bill.mark_as_paid(payer_user_id, payment_method, payment_reference, payment_notes)
        updated_bill = await self.daily_worker_bill_repository.update(bill, session)
        
        # 标记相关实例为已结算
        await self._mark_instances_as_settled(bill, session)
        
        result["success"] = True
        result["message"] = "账单已标记为已支付"
        result["bill"] = updated_bill
        
        return result
    
    async def cancel_bill(
        self,
        bill_id: int,
        cancellation_reason: str,
        session: AsyncSession
    ) -> Dict[str, Any]:
        """取消账单"""
        
        result = {"success": False, "message": "", "bill": None}
        
        bill = await self.daily_worker_bill_repository.get_by_id(bill_id, session)
        if not bill:
            result["message"] = "账单不存在"
            return result
        
        if bill.status not in [BillStatus.DRAFT, BillStatus.PENDING, BillStatus.REJECTED]:
            result["message"] = f"账单状态为 {bill.status.value}，无法取消"
            return result
        
        # 取消账单
        bill.cancel_bill(cancellation_reason)
        updated_bill = await self.daily_worker_bill_repository.update(bill, session)
        
        # 重置相关实例的结算状态
        await self._reset_instance_settlement_status(bill, session)
        
        result["success"] = True
        result["message"] = "账单已取消"
        result["bill"] = updated_bill
        
        return result
    
    async def dispute_bill_instance(
        self,
        instance_id: int,
        dispute_reason: str,
        session: AsyncSession
    ) -> Dict[str, Any]:
        """对账单中的实例提出争议"""
        
        result = {"success": False, "message": "", "instance": None}
        
        instance = await self.order_craft_route_instance_repository.get_by_id(instance_id, session)
        if not instance:
            result["message"] = "工作实例不存在"
            return result
        
        if instance.settlement_status == SettlementStatus.SETTLED:
            result["message"] = "实例已结算，无法提出争议"
            return result
        
        # 标记为争议
        instance.mark_as_disputed(dispute_reason)
        updated_instance = await self.order_craft_route_instance_repository.update(instance, session)
        
        result["success"] = True
        result["message"] = "已对实例提出争议"
        result["instance"] = updated_instance
        
        return result
    
    async def resolve_instance_dispute(
        self,
        instance_id: int,
        resolution_notes: str,
        include_in_settlement: bool,
        session: AsyncSession
    ) -> Dict[str, Any]:
        """解决实例争议"""
        
        result = {"success": False, "message": "", "instance": None}
        
        instance = await self.order_craft_route_instance_repository.get_by_id(instance_id, session)
        if not instance:
            result["message"] = "工作实例不存在"
            return result
        
        if instance.settlement_status != SettlementStatus.DISPUTED:
            result["message"] = "实例不在争议状态"
            return result
        
        # 解决争议
        if include_in_settlement:
            instance.settlement_status = SettlementStatus.INCLUDED
        else:
            instance.exclude_from_settlement(f"争议解决: {resolution_notes}")
        
        instance.notes = f"{instance.notes or ''}\n[争议解决] {resolution_notes}".strip()
        updated_instance = await self.order_craft_route_instance_repository.update(instance, session)
        
        result["success"] = True
        result["message"] = "争议已解决"
        result["instance"] = updated_instance
        
        return result
    
    async def get_bill_settlement_summary(
        self,
        factory_id: int,
        session: AsyncSession
    ) -> Dict[str, Any]:
        """获取工厂结算汇总"""
        
        # 获取各状态的账单统计
        pending_bills = await self.daily_worker_bill_repository.get_by_status(
            factory_id, BillStatus.PENDING, session
        )
        approved_bills = await self.daily_worker_bill_repository.get_by_status(
            factory_id, BillStatus.APPROVED, session
        )
        paid_bills = await self.daily_worker_bill_repository.get_by_status(
            factory_id, BillStatus.PAID, session
        )
        
        # 获取各结算状态的实例统计
        pending_instances = await self.order_craft_route_instance_repository.get_by_settlement_status(
            factory_id, SettlementStatus.PENDING, session
        )
        included_instances = await self.order_craft_route_instance_repository.get_by_settlement_status(
            factory_id, SettlementStatus.INCLUDED, session
        )
        settled_instances = await self.order_craft_route_instance_repository.get_by_settlement_status(
            factory_id, SettlementStatus.SETTLED, session
        )
        disputed_instances = await self.order_craft_route_instance_repository.get_by_settlement_status(
            factory_id, SettlementStatus.DISPUTED, session
        )
        
        return {
            "bills": {
                "pending_count": len(pending_bills),
                "approved_count": len(approved_bills),
                "paid_count": len(paid_bills),
                "pending_amount": sum(bill.total_amount for bill in pending_bills),
                "approved_amount": sum(bill.total_amount for bill in approved_bills),
                "paid_amount": sum(bill.total_amount for bill in paid_bills)
            },
            "instances": {
                "pending_count": len(pending_instances),
                "included_count": len(included_instances),
                "settled_count": len(settled_instances),
                "disputed_count": len(disputed_instances)
            }
        }
    
    async def _validate_bill_for_review(
        self,
        bill: DailyWorkerBill,
        session: AsyncSession
    ) -> Dict[str, Any]:
        """验证账单是否可以提交审核"""
        
        # 检查账单是否有相关的工作实例
        instances = await self._get_bill_instances(bill, session)
        
        if not instances:
            return {"valid": False, "message": "账单没有关联的工作实例"}
        
        # 检查实例是否都已完成
        incomplete_instances = [
            instance for instance in instances 
            if instance.status != "completed"
        ]
        
        if incomplete_instances:
            return {
                "valid": False, 
                "message": f"存在 {len(incomplete_instances)} 个未完成的工作实例"
            }
        
        # 检查金额是否合理
        if bill.total_amount <= 0:
            return {"valid": False, "message": "账单总金额必须大于0"}
        
        return {"valid": True, "message": "验证通过"}
    
    async def _get_bill_instances(
        self,
        bill: DailyWorkerBill,
        session: AsyncSession
    ) -> List[OrderCraftRouteInstance]:
        """获取账单相关的工作实例"""
        
        # 获取账单日期的工作实例
        start_datetime = datetime.combine(bill.bill_date, datetime.min.time())
        end_datetime = datetime.combine(bill.bill_date, datetime.max.time())
        
        return await self.order_craft_route_instance_repository.get_worker_completed_in_date_range(
            bill.factory_id, bill.worker_user_id, start_datetime, end_datetime, session
        )
    
    async def _mark_instances_as_settled(
        self,
        bill: DailyWorkerBill,
        session: AsyncSession
    ) -> None:
        """标记账单相关实例为已结算"""
        
        instances = await self._get_bill_instances(bill, session)
        
        for instance in instances:
            if instance.settlement_status == SettlementStatus.INCLUDED:
                instance.mark_as_settled()
        
        await self.order_craft_route_instance_repository.bulk_update(instances, session)
    
    async def _reset_instance_settlement_status(
        self,
        bill: DailyWorkerBill,
        session: AsyncSession
    ) -> None:
        """重置账单相关实例的结算状态"""
        
        instances = await self._get_bill_instances(bill, session)
        
        for instance in instances:
            if instance.settlement_status in [SettlementStatus.INCLUDED]:
                instance.reset_settlement_status()
        
        await self.order_craft_route_instance_repository.bulk_update(instances, session)