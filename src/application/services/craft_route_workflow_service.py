from typing import Optional, List, Dict, Any, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from src.domain.entities.order_craft_route_instance import OrderCraftRouteInstance, CompletionGranularity
from src.domain.entities.order_craft_route import OrderCraftRoute
from src.domain.entities.order_bundle import OrderBundle
from src.domain.entities.order_part import OrderPart
from src.domain.entities.order_line import OrderLine
from src.domain.entities.order import Order
from src.application.interfaces.order_bundle_repository_interface import OrderBundleRepositoryInterface
from src.application.interfaces.order_part_repository_interface import OrderPartRepositoryInterface
from src.application.interfaces.order_line_repository_interface import OrderLineRepositoryInterface
from src.application.interfaces.order_repository_interface import OrderRepositoryInterface
from src.application.interfaces.order_craft_route_repository_interface import OrderCraftRouteRepositoryInterface


class CraftRouteWorkflowService:
    """工艺路线工作流服务 - 处理完成实例后的状态更新和阶段推进"""
    
    def __init__(
        self,
        order_bundle_repository: OrderBundleRepositoryInterface,
        order_part_repository: OrderPartRepositoryInterface,
        order_line_repository: OrderLineRepositoryInterface,
        order_repository: OrderRepositoryInterface,
        order_craft_route_repository: OrderCraftRouteRepositoryInterface
    ):
        self.order_bundle_repository = order_bundle_repository
        self.order_part_repository = order_part_repository
        self.order_line_repository = order_line_repository
        self.order_repository = order_repository
        self.order_craft_route_repository = order_craft_route_repository
    
    async def process_completion_instance(
        self, 
        completion_instance: OrderCraftRouteInstance,
        session: AsyncSession
    ) -> Dict[str, Any]:
        """
        处理完成实例，更新相关实体状态和工艺路线阶段
        
        Args:
            completion_instance: 完成实例
            session: 数据库会话
            
        Returns:
            Dict包含更新结果和影响的实体
        """
        result = {
            "success": True,
            "message": "Processing completed successfully",
            "updated_entities": [],
            "advanced_stages": [],
            "errors": []
        }
        
        try:
            # 1. 检查工艺路线是否完成
            craft_route_complete = await self._check_craft_route_completion(
                completion_instance, session
            )
            
            # 2. 根据完成粒度更新相关实体
            if completion_instance.is_bundle_level():
                await self._update_bundle_status(completion_instance, session, result)
            elif completion_instance.is_bed_level():
                await self._update_bed_status(completion_instance, session, result)
            elif completion_instance.is_order_level():
                await self._update_order_status(completion_instance, session, result)
            
            # 3. 如果工艺路线完成，推进到下一阶段
            if craft_route_complete:
                await self._advance_to_next_craft_route(
                    completion_instance, session, result
                )
            
            # 4. 更新父级实体状态
            await self._update_parent_entity_status(completion_instance, session, result)
            
        except Exception as e:
            result["success"] = False
            result["message"] = f"Processing failed: {str(e)}"
            result["errors"].append(str(e))
        
        return result
    
    async def _check_craft_route_completion(
        self, 
        completion_instance: OrderCraftRouteInstance,
        session: AsyncSession
    ) -> bool:
        """检查工艺路线是否完成"""
        # 获取该工艺路线的所有完成实例
        craft_route = completion_instance.order_craft_route
        
        # 根据完成粒度计算是否完成
        if completion_instance.is_bundle_level():
            return await self._check_bundle_craft_route_completion(
                craft_route, completion_instance.order_bundle_no, session
            )
        elif completion_instance.is_bed_level():
            return await self._check_bed_craft_route_completion(
                craft_route, completion_instance.order_part_no, session
            )
        else:  # order level
            return await self._check_order_craft_route_completion(
                craft_route, completion_instance.order_no, session
            )
    
    async def _check_bundle_craft_route_completion(
        self, 
        craft_route: OrderCraftRoute,
        order_bundle_no: str,
        session: AsyncSession
    ) -> bool:
        """检查扎级别工艺路线是否完成"""
        # 获取扎的信息
        bundle = await self.order_bundle_repository.get_by_order_bundle_no_and_factory(
            order_bundle_no, craft_route.factory_id, craft_route.order_craft.order_no, session
        )
        if not bundle:
            return False
        
        # 计算该工艺路线在此扎上的完成数量
        completed_quantity = sum(
            instance.completed_quantity 
            for instance in craft_route.completion_instances
            if (instance.order_bundle_no == order_bundle_no and 
                instance.status == "completed")
        )
        
        # 如果完成数量达到扎的总数量，则认为完成
        return completed_quantity >= bundle.quantity
    
    async def _check_bed_craft_route_completion(
        self, 
        craft_route: OrderCraftRoute,
        order_part_no: str,
        session: AsyncSession
    ) -> bool:
        """检查床级别工艺路线是否完成"""
        # 获取床的所有扎
        bundles = await self.order_bundle_repository.get_by_order_part_no_and_factory(
            order_part_no, craft_route.factory_id, craft_route.order_craft.order_no, session
        )
        
        total_quantity = sum(bundle.quantity for bundle in bundles)
        
        # 计算该工艺路线在此床上的完成数量
        completed_quantity = sum(
            instance.completed_quantity 
            for instance in craft_route.completion_instances
            if (instance.order_part_no == order_part_no and 
                instance.status == "completed")
        )
        
        return completed_quantity >= total_quantity
    
    async def _check_order_craft_route_completion(
        self, 
        craft_route: OrderCraftRoute,
        order_no: str,
        session: AsyncSession
    ) -> bool:
        """检查整单级别工艺路线是否完成"""
        # 获取订单的总数量
        order = await self.order_repository.get_by_order_no_and_factory(
            order_no, craft_route.factory_id, session
        )
        if not order:
            return False
        
        # 计算该工艺路线在此订单上的完成数量
        completed_quantity = sum(
            instance.completed_quantity 
            for instance in craft_route.completion_instances
            if (instance.order_no == order_no and 
                instance.status == "completed")
        )
        
        return completed_quantity >= order.total_quantity
    
    async def _update_bundle_status(
        self, 
        completion_instance: OrderCraftRouteInstance,
        session: AsyncSession,
        result: Dict[str, Any]
    ) -> None:
        """更新扎状态"""
        bundle = await self.order_bundle_repository.get_by_order_bundle_no_and_factory(
            completion_instance.order_bundle_no,
            completion_instance.factory_id,
            completion_instance.order_no,
            session
        )
        
        if bundle:
            # 更新当前工艺路线代码
            bundle.current_craft_route_code = completion_instance.order_craft_route.code
            # 更新完成数量
            bundle.completed_quantity += completion_instance.completed_quantity
            bundle.update_progress()
            
            await self.order_bundle_repository.update(bundle)
            result["updated_entities"].append(f"Bundle: {bundle.order_bundle_no}")
    
    async def _update_bed_status(
        self, 
        completion_instance: OrderCraftRouteInstance,
        session: AsyncSession,
        result: Dict[str, Any]
    ) -> None:
        """更新床状态"""
        # 更新床下所有扎的状态
        bundles = await self.order_bundle_repository.get_by_order_part_no_and_factory(
            completion_instance.order_part_no,
            completion_instance.factory_id,
            completion_instance.order_no,
            session
        )
        
        for bundle in bundles:
            bundle.current_craft_route_code = completion_instance.order_craft_route.code
            await self.order_bundle_repository.update(bundle)
        
        # 更新订单部位状态
        order_part = await self.order_part_repository.get_by_order_part_no_and_factory(
            completion_instance.order_part_no,
            completion_instance.factory_id,
            completion_instance.order_no,
            session
        )
        
        if order_part:
            order_part.current_craft_route_code = completion_instance.order_craft_route.code
            await self.order_part_repository.update(order_part)
            result["updated_entities"].append(f"OrderPart: {order_part.order_part_no}")
    
    async def _update_order_status(
        self, 
        completion_instance: OrderCraftRouteInstance,
        session: AsyncSession,
        result: Dict[str, Any]
    ) -> None:
        """更新整单状态"""
        # 更新订单状态
        order = await self.order_repository.get_by_order_no_and_factory(
            completion_instance.order_no,
            completion_instance.factory_id,
            session
        )
        
        if order:
            order.current_craft_route_code = completion_instance.order_craft_route.code
            await self.order_repository.update(order)
            result["updated_entities"].append(f"Order: {order.order_no}")
    
    async def _advance_to_next_craft_route(
        self, 
        completion_instance: OrderCraftRouteInstance,
        session: AsyncSession,
        result: Dict[str, Any]
    ) -> None:
        """推进到下一个工艺路线"""
        current_route = completion_instance.order_craft_route
        
        # 查找下一个工艺路线
        next_route = await self.order_craft_route_repository.get_next_route_in_craft(
            current_route.order_craft_id, current_route.order, session
        )
        
        if next_route:
            # 启动下一个工艺路线
            next_route.start_route()
            await self.order_craft_route_repository.update(next_route)
            result["advanced_stages"].append(
                f"Advanced from {current_route.code} to {next_route.code}"
            )
        else:
            # 整个工艺完成
            order_craft = current_route.order_craft
            order_craft.complete_craft()
            # 这里需要注入order_craft_repository
            result["advanced_stages"].append(f"OrderCraft {order_craft.craft_code} completed")
    
    async def _update_parent_entity_status(
        self, 
        completion_instance: OrderCraftRouteInstance,
        session: AsyncSession,
        result: Dict[str, Any]
    ) -> None:
        """更新父级实体状态"""
        # 这里可以添加额外的父级状态更新逻辑
        # 例如：更新订单行状态、更新订单整体进度等
        pass
    
    async def get_completion_progress(
        self, 
        order_craft_route_id: int,
        granularity: CompletionGranularity,
        target_identifier: str,
        session: AsyncSession
    ) -> Dict[str, Any]:
        """获取完成进度"""
        # 查询指定范围内的完成实例
        progress_data = {
            "total_quantity": 0,
            "completed_quantity": 0,
            "completion_percentage": 0.0,
            "completion_instances": []
        }
        
        # 实现具体的进度查询逻辑
        return progress_data