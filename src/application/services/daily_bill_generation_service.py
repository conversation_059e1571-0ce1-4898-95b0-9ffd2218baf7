from typing import List, Dict, Any, Optional
from datetime import date, datetime, timezone
from decimal import Decimal
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, func
from src.domain.entities.daily_worker_bill import DailyWorkerBill, BillStatus
from src.domain.entities.order_craft_route_instance import OrderCraftRouteInstance, SettlementStatus, CompletionGranularity
from src.domain.entities.user import User
from src.application.interfaces.daily_worker_bill_repository_interface import DailyWorkerBillRepositoryInterface
from src.application.interfaces.order_craft_route_instance_repository_interface import OrderCraftRouteInstanceRepositoryInterface
from src.application.interfaces.user_repository_interface import UserRepositoryInterface


class DailyBillGenerationService:
    """日结账单生成服务"""
    
    def __init__(
        self,
        daily_worker_bill_repository: DailyWorkerBillRepositoryInterface,
        order_craft_route_instance_repository: OrderCraftRouteInstanceRepositoryInterface,
        user_repository: UserRepositoryInterface
    ):
        self.daily_worker_bill_repository = daily_worker_bill_repository
        self.order_craft_route_instance_repository = order_craft_route_instance_repository
        self.user_repository = user_repository
    
    async def generate_daily_bills_for_factory(
        self, 
        factory_id: int, 
        bill_date: date,
        session: AsyncSession
    ) -> List[DailyWorkerBill]:
        """为工厂生成指定日期的所有工人账单"""
        
        # 1. 获取当日完成的所有工作实例
        instances = await self._get_completed_instances_for_date(
            factory_id, bill_date, session
        )
        
        if not instances:
            return []
        
        # 2. 按工人分组
        instances_by_worker = self._group_instances_by_worker(instances)
        
        # 3. 为每个工人生成账单
        generated_bills = []
        for worker_user_id, worker_instances in instances_by_worker.items():
            # 检查是否已存在账单
            existing_bill = await self.daily_worker_bill_repository.get_by_worker_and_date(
                worker_user_id, factory_id, bill_date, session
            )
            
            if existing_bill:
                # 更新现有账单
                updated_bill = await self._update_existing_bill(
                    existing_bill, worker_instances, session
                )
                generated_bills.append(updated_bill)
            else:
                # 创建新账单
                new_bill = await self._create_new_bill(
                    factory_id, worker_user_id, bill_date, worker_instances, session
                )
                generated_bills.append(new_bill)
        
        return generated_bills
    
    async def generate_bill_for_worker(
        self,
        factory_id: int,
        worker_user_id: int,
        bill_date: date,
        session: AsyncSession
    ) -> Optional[DailyWorkerBill]:
        """为特定工人生成指定日期的账单"""
        
        # 获取工人当日完成的实例
        instances = await self._get_worker_completed_instances_for_date(
            factory_id, worker_user_id, bill_date, session
        )
        
        if not instances:
            return None
        
        # 检查是否已存在账单
        existing_bill = await self.daily_worker_bill_repository.get_by_worker_and_date(
            worker_user_id, factory_id, bill_date, session
        )
        
        if existing_bill:
            return await self._update_existing_bill(existing_bill, instances, session)
        else:
            return await self._create_new_bill(
                factory_id, worker_user_id, bill_date, instances, session
            )
    
    async def regenerate_bill(
        self,
        bill_id: int,
        session: AsyncSession
    ) -> Optional[DailyWorkerBill]:
        """重新生成账单"""
        
        bill = await self.daily_worker_bill_repository.get_by_id(bill_id, session)
        if not bill or not bill.is_editable():
            return None
        
        # 获取当日实例
        instances = await self._get_worker_completed_instances_for_date(
            bill.factory_id, bill.worker_user_id, bill.bill_date, session
        )
        
        return await self._update_existing_bill(bill, instances, session)
    
    async def _get_completed_instances_for_date(
        self,
        factory_id: int,
        bill_date: date,
        session: AsyncSession
    ) -> List[OrderCraftRouteInstance]:
        """获取指定日期完成的工作实例"""
        
        start_datetime = datetime.combine(bill_date, datetime.min.time()).replace(tzinfo=timezone.utc)
        end_datetime = datetime.combine(bill_date, datetime.max.time()).replace(tzinfo=timezone.utc)
        
        return await self.order_craft_route_instance_repository.get_completed_in_date_range(
            factory_id, start_datetime, end_datetime, session
        )
    
    async def _get_worker_completed_instances_for_date(
        self,
        factory_id: int,
        worker_user_id: int,
        bill_date: date,
        session: AsyncSession
    ) -> List[OrderCraftRouteInstance]:
        """获取特定工人指定日期完成的工作实例"""
        
        start_datetime = datetime.combine(bill_date, datetime.min.time()).replace(tzinfo=timezone.utc)
        end_datetime = datetime.combine(bill_date, datetime.max.time()).replace(tzinfo=timezone.utc)
        
        return await self.order_craft_route_instance_repository.get_worker_completed_in_date_range(
            factory_id, worker_user_id, start_datetime, end_datetime, session
        )
    
    def _group_instances_by_worker(
        self, 
        instances: List[OrderCraftRouteInstance]
    ) -> Dict[int, List[OrderCraftRouteInstance]]:
        """按工人分组实例"""
        
        groups = {}
        for instance in instances:
            worker_id = instance.worker_user_id
            if worker_id not in groups:
                groups[worker_id] = []
            groups[worker_id].append(instance)
        
        return groups
    
    async def _create_new_bill(
        self,
        factory_id: int,
        worker_user_id: int,
        bill_date: date,
        instances: List[OrderCraftRouteInstance],
        session: AsyncSession
    ) -> DailyWorkerBill:
        """创建新账单"""
        
        # 生成账单号
        bill_no = DailyWorkerBill.generate_bill_no(factory_id, worker_user_id, bill_date)
        
        # 计算统计数据
        bill_data = self._calculate_bill_data(instances)
        
        # 创建账单
        bill = DailyWorkerBill(
            factory_id=factory_id,
            worker_user_id=worker_user_id,
            bill_date=bill_date,
            bill_no=bill_no,
            total_completed_quantity=bill_data["total_quantity"],
            total_work_instances=bill_data["total_instances"],
            total_work_duration_minutes=bill_data["total_duration"],
            base_amount=bill_data["base_amount"],
            total_amount=bill_data["base_amount"],
            quality_score_average=bill_data["avg_quality_score"],
            defect_count=bill_data["defect_count"],
            rework_count=bill_data["rework_count"],
            breakdown_by_granularity=bill_data["granularity_breakdown"],
            breakdown_by_craft_route=bill_data["craft_route_breakdown"],
            breakdown_by_order=bill_data["order_breakdown"],
            is_auto_generated=True,
            generated_at=datetime.now(timezone.utc)
        )
        
        # 保存账单
        created_bill = await self.daily_worker_bill_repository.create(bill, session)
        
        # 标记实例为已纳入账单
        await self._mark_instances_as_included(instances, session)
        
        return created_bill
    
    async def _update_existing_bill(
        self,
        bill: DailyWorkerBill,
        instances: List[OrderCraftRouteInstance],
        session: AsyncSession
    ) -> DailyWorkerBill:
        """更新现有账单"""
        
        if not bill.is_editable():
            return bill
        
        # 重新计算数据
        bill_data = self._calculate_bill_data(instances)
        
        # 更新账单数据
        bill.total_completed_quantity = bill_data["total_quantity"]
        bill.total_work_instances = bill_data["total_instances"]
        bill.total_work_duration_minutes = bill_data["total_duration"]
        bill.base_amount = bill_data["base_amount"]
        bill.update_total_amount()
        bill.quality_score_average = bill_data["avg_quality_score"]
        bill.defect_count = bill_data["defect_count"]
        bill.rework_count = bill_data["rework_count"]
        bill.breakdown_by_granularity = bill_data["granularity_breakdown"]
        bill.breakdown_by_craft_route = bill_data["craft_route_breakdown"]
        bill.breakdown_by_order = bill_data["order_breakdown"]
        
        # 保存更新
        updated_bill = await self.daily_worker_bill_repository.update(bill, session)
        
        # 标记实例为已纳入账单
        await self._mark_instances_as_included(instances, session)
        
        return updated_bill
    
    def _calculate_bill_data(self, instances: List[OrderCraftRouteInstance]) -> Dict[str, Any]:
        """计算账单数据"""
        
        total_quantity = sum(instance.completed_quantity for instance in instances)
        total_instances = len(instances)
        
        # 计算总工作时长
        total_duration = 0
        for instance in instances:
            duration = instance.calculate_work_duration()
            if duration:
                total_duration += duration
        
        # 计算质量分数平均值
        quality_scores = [instance.quality_level for instance in instances if instance.quality_level]
        avg_quality_score = None
        if quality_scores:
            # 假设质量等级 A=100, B=80, C=60
            score_map = {"A": 100, "B": 80, "C": 60}
            scores = [score_map.get(level, 0) for level in quality_scores]
            avg_quality_score = Decimal(str(sum(scores) / len(scores)))
        
        # 计算基础金额 (这里需要根据实际业务规则调整)
        base_amount = self._calculate_base_amount(instances)
        
        # 统计缺陷和返工
        defect_count = 0  # 这里需要根据实际数据统计
        rework_count = 0  # 这里需要根据实际数据统计
        
        # 分解统计
        granularity_breakdown = self._calculate_granularity_breakdown(instances)
        craft_route_breakdown = self._calculate_craft_route_breakdown(instances)
        order_breakdown = self._calculate_order_breakdown(instances)
        
        return {
            "total_quantity": total_quantity,
            "total_instances": total_instances,
            "total_duration": total_duration,
            "base_amount": base_amount,
            "avg_quality_score": avg_quality_score,
            "defect_count": defect_count,
            "rework_count": rework_count,
            "granularity_breakdown": granularity_breakdown,
            "craft_route_breakdown": craft_route_breakdown,
            "order_breakdown": order_breakdown
        }
    
    def _calculate_base_amount(self, instances: List[OrderCraftRouteInstance]) -> Decimal:
        """计算基础金额"""
        # 这里需要根据实际的计价规则实现
        # 示例：按件计价
        total_amount = Decimal("0")
        
        for instance in instances:
            # 假设每个实例有固定单价，或者根据工艺路线计价
            unit_price = Decimal("1.0")  # 这里应该从工艺路线或配置中获取
            instance_amount = unit_price * instance.completed_quantity
            total_amount += instance_amount
        
        return total_amount
    
    def _calculate_granularity_breakdown(self, instances: List[OrderCraftRouteInstance]) -> Dict[str, Any]:
        """按粒度分解统计"""
        breakdown = {
            "bundle": {"count": 0, "quantity": 0},
            "bed": {"count": 0, "quantity": 0},
            "order": {"count": 0, "quantity": 0}
        }
        
        for instance in instances:
            granularity = instance.completion_granularity.value
            breakdown[granularity]["count"] += 1
            breakdown[granularity]["quantity"] += instance.completed_quantity
        
        return breakdown
    
    def _calculate_craft_route_breakdown(self, instances: List[OrderCraftRouteInstance]) -> Dict[str, Any]:
        """按工艺路线分解统计"""
        breakdown = {}
        
        for instance in instances:
            route_code = instance.order_craft_route.code
            if route_code not in breakdown:
                breakdown[route_code] = {"count": 0, "quantity": 0}
            
            breakdown[route_code]["count"] += 1
            breakdown[route_code]["quantity"] += instance.completed_quantity
        
        return breakdown
    
    def _calculate_order_breakdown(self, instances: List[OrderCraftRouteInstance]) -> Dict[str, Any]:
        """按订单分解统计"""
        breakdown = {}
        
        for instance in instances:
            order_no = instance.order_no
            if order_no not in breakdown:
                breakdown[order_no] = {"count": 0, "quantity": 0}
            
            breakdown[order_no]["count"] += 1
            breakdown[order_no]["quantity"] += instance.completed_quantity
        
        return breakdown
    
    async def _mark_instances_as_included(
        self, 
        instances: List[OrderCraftRouteInstance],
        session: AsyncSession
    ) -> None:
        """标记实例为已纳入账单"""
        
        for instance in instances:
            if instance.is_pending_settlement():
                instance.include_in_bill()
        
        # 批量更新
        await self.order_craft_route_instance_repository.bulk_update(instances, session)
    
    async def finalize_bill(
        self,
        bill_id: int,
        session: AsyncSession
    ) -> Optional[DailyWorkerBill]:
        """确认账单"""
        
        bill = await self.daily_worker_bill_repository.get_by_id(bill_id, session)
        if not bill or bill.status != BillStatus.DRAFT:
            return None
        
        bill.submit_for_review()
        return await self.daily_worker_bill_repository.update(bill, session)
    
    async def get_bill_instances(
        self,
        bill_id: int,
        session: AsyncSession
    ) -> List[OrderCraftRouteInstance]:
        """获取账单相关的工作实例"""
        
        bill = await self.daily_worker_bill_repository.get_by_id(bill_id, session)
        if not bill:
            return []
        
        return await self._get_worker_completed_instances_for_date(
            bill.factory_id, bill.worker_user_id, bill.bill_date, session
        )