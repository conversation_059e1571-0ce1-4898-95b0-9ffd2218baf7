from typing import List, Optional
from src.application.interfaces.craft_repository_interface import CraftRepositoryInterface
from src.application.interfaces.craft_route_repository_interface import CraftRouteRepositoryInterface
from src.application.interfaces.skill_repository_interface import SkillRepositoryInterface
from src.application.dto.craft_dto import (
    CraftCreateDTO, CraftUpdateDTO, CraftResponseDTO, CraftListDTO,
    CraftRouteCreateDTO, CraftRouteUpdateDTO, CraftRouteResponseDTO,
    CraftRouteDetailDTO, CraftWithRoutesDTO, CraftRouteListDTO,
    CraftSearchDTO, CraftRouteSearchDTO, CraftOperationResultDTO,
    BulkCraftRouteCreateDTO, BulkCraftRouteOperationResultDTO
)
from src.domain.entities.craft import Craft
from src.domain.entities.craft_route import CraftRoute


class CraftUseCases:
    """Use cases for craft management operations."""

    def __init__(
        self,
        craft_repository: CraftRepositoryInterface,
        craft_route_repository: CraftRouteRepositoryInterface,
        skill_repository: SkillRepositoryInterface
    ):
        self.craft_repository = craft_repository
        self.craft_route_repository = craft_route_repository
        self.skill_repository = skill_repository

    # Craft operations
    async def create_craft(self, craft_data: CraftCreateDTO) -> CraftResponseDTO:
        """Create a new craft."""
        # Check if craft code already exists
        existing_craft = await self.craft_repository.get_by_code(craft_data.code)
        if existing_craft:
            raise ValueError(f"Craft with code '{craft_data.code}' already exists")

        craft = Craft(
            code=craft_data.code,
            name=craft_data.name,
            priority=craft_data.priority,
            enabled=craft_data.enabled,
            description=craft_data.description
        )
        
        created_craft = await self.craft_repository.create(craft)
        return CraftResponseDTO.model_validate(created_craft)

    async def get_craft_by_id(self, craft_id: int) -> Optional[CraftResponseDTO]:
        """Get craft by ID."""
        craft = await self.craft_repository.get_by_id(craft_id)
        if craft:
            return CraftResponseDTO.model_validate(craft)
        return None

    async def get_craft_by_code(self, code: str) -> Optional[CraftWithRoutesDTO]:
        """Get craft by code with its routes."""
        craft = await self.craft_repository.get_by_code(code)
        if not craft:
            return None

        # Build routes DTOs
        routes = []
        for route in craft.get_ordered_routes():
            route_dto = CraftRouteDetailDTO(
                id=route.id,
                craft_code=route.craft_code,
                skill_code=route.skill_code,
                skill_name=route.skill.name if route.skill else route.skill_code,
                code=route.code,
                name=route.name,
                order=route.order,
                measurement_types=route.get_measurement_types(),
                registration_types=route.get_registration_types(),
                notes=route.notes,
                is_required=route.is_required,
                created_at=route.created_at,
                updated_at=route.updated_at
            )
            routes.append(route_dto)

        craft_dto = CraftWithRoutesDTO.model_validate(craft)
        craft_dto.routes = routes
        return craft_dto

    async def get_all_crafts(self, search: Optional[CraftSearchDTO] = None) -> CraftListDTO:
        """Get all crafts with optional search filters."""
        if search and search.search_term:
            crafts = await self.craft_repository.search_crafts(search.search_term)
        else:
            crafts = await self.craft_repository.get_all()

        # Apply additional filters
        if search:
            if search.enabled is not None:
                crafts = [c for c in crafts if c.enabled == search.enabled]
            if search.min_priority is not None:
                crafts = [c for c in crafts if c.priority >= search.min_priority]
            if search.max_priority is not None:
                crafts = [c for c in crafts if c.priority <= search.max_priority]

        craft_dtos = [CraftResponseDTO.model_validate(craft) for craft in crafts]
        return CraftListDTO(crafts=craft_dtos, total=len(craft_dtos))

    async def get_enabled_crafts(self) -> CraftListDTO:
        """Get all enabled crafts."""
        crafts = await self.craft_repository.get_enabled_crafts()
        craft_dtos = [CraftResponseDTO.model_validate(craft) for craft in crafts]
        return CraftListDTO(crafts=craft_dtos, total=len(craft_dtos))

    async def update_craft(self, craft_id: int, craft_data: CraftUpdateDTO) -> Optional[CraftResponseDTO]:
        """Update craft."""
        craft = await self.craft_repository.get_by_id(craft_id)
        if not craft:
            return None

        # Update fields if provided
        if craft_data.name is not None:
            craft.name = craft_data.name
        if craft_data.priority is not None:
            craft.priority = craft_data.priority
        if craft_data.enabled is not None:
            craft.enabled = craft_data.enabled
        if craft_data.description is not None:
            craft.description = craft_data.description

        updated_craft = await self.craft_repository.update(craft)
        return CraftResponseDTO.model_validate(updated_craft)

    async def delete_craft(self, craft_id: int) -> bool:
        """Delete craft and all its routes."""
        craft = await self.craft_repository.get_by_id(craft_id)
        if not craft:
            return False

        # Delete all routes for this craft first
        await self.craft_route_repository.delete_by_craft_code(craft.code)
        
        # Delete the craft
        return await self.craft_repository.delete(craft_id)

    # Craft Route operations
    async def create_craft_route(self, route_data: CraftRouteCreateDTO) -> CraftRouteResponseDTO:
        """Create a new craft route."""
        # Validate craft exists
        craft = await self.craft_repository.get_by_code(route_data.craft_code)
        if not craft:
            raise ValueError(f"Craft with code '{route_data.craft_code}' not found")

        # Validate skill exists
        skill = await self.skill_repository.get_by_code(route_data.skill_code)
        if not skill:
            raise ValueError(f"Skill with code '{route_data.skill_code}' not found")

        # Check if route already exists
        existing_route = await self.craft_route_repository.get_by_craft_and_skill(
            route_data.craft_code, route_data.skill_code
        )
        if existing_route:
            raise ValueError(f"Route already exists for craft '{route_data.craft_code}' and skill '{route_data.skill_code}'")

        craft_route = CraftRoute(
            craft_code=route_data.craft_code,
            skill_code=route_data.skill_code,
            code=route_data.code,
            name=route_data.name,
            order=route_data.order,
            measurement_types=route_data.measurement_types,
            registration_types=route_data.registration_types,
            notes=route_data.notes,
            is_required=route_data.is_required
        )
        
        created_route = await self.craft_route_repository.create(craft_route)
        return CraftRouteResponseDTO.model_validate(created_route)

    async def create_bulk_craft_routes(self, bulk_data: BulkCraftRouteCreateDTO) -> BulkCraftRouteOperationResultDTO:
        """Create multiple craft routes at once."""
        # Validate craft exists
        craft = await self.craft_repository.get_by_code(bulk_data.craft_code)
        if not craft:
            raise ValueError(f"Craft with code '{bulk_data.craft_code}' not found")

        results = []
        successful_count = 0
        failed_count = 0

        for route_data in bulk_data.routes:
            try:
                # Ensure craft_code matches
                route_data.craft_code = bulk_data.craft_code
                
                route_result = await self.create_craft_route(route_data)
                
                results.append(CraftOperationResultDTO(
                    success=True,
                    message="Route created successfully",
                    craft_route_id=route_result.id
                ))
                successful_count += 1

            except Exception as e:
                results.append(CraftOperationResultDTO(
                    success=False,
                    message=f"Failed to create route: {str(e)}",
                    details={"skill_code": route_data.skill_code}
                ))
                failed_count += 1

        return BulkCraftRouteOperationResultDTO(
            success=failed_count == 0,
            message=f"Created {successful_count} routes, {failed_count} failed",
            results=results,
            successful_count=successful_count,
            failed_count=failed_count
        )

    async def get_craft_routes(self, craft_code: str) -> CraftRouteListDTO:
        """Get all routes for a specific craft."""
        routes = await self.craft_route_repository.get_by_craft_code(craft_code)
        
        route_dtos = []
        for route in routes:
            route_dto = CraftRouteDetailDTO(
                id=route.id,
                craft_code=route.craft_code,
                skill_code=route.skill_code,
                skill_name=route.skill.name if route.skill else route.skill_code,
                code=route.code,
                name=route.name,
                order=route.order,
                measurement_types=route.get_measurement_types(),
                registration_types=route.get_registration_types(),
                notes=route.notes,
                is_required=route.is_required,
                created_at=route.created_at,
                updated_at=route.updated_at
            )
            route_dtos.append(route_dto)

        return CraftRouteListDTO(routes=route_dtos, total=len(route_dtos))

    async def get_skill_routes(self, skill_code: str) -> CraftRouteListDTO:
        """Get all routes that use a specific skill."""
        routes = await self.craft_route_repository.get_by_skill_code(skill_code)
        
        route_dtos = []
        for route in routes:
            route_dto = CraftRouteDetailDTO(
                id=route.id,
                craft_code=route.craft_code,
                skill_code=route.skill_code,
                skill_name=route.skill.name if route.skill else route.skill_code,
                code=route.code,
                name=route.name,
                order=route.order,
                measurement_types=route.get_measurement_types(),
                registration_types=route.get_registration_types(),
                notes=route.notes,
                is_required=route.is_required,
                created_at=route.created_at,
                updated_at=route.updated_at
            )
            route_dtos.append(route_dto)

        return CraftRouteListDTO(routes=route_dtos, total=len(route_dtos))

    async def update_craft_route(self, route_id: int, route_data: CraftRouteUpdateDTO) -> Optional[CraftRouteResponseDTO]:
        """Update craft route."""
        route = await self.craft_route_repository.get_by_id(route_id)
        if not route:
            return None

        # Update fields if provided
        if route_data.code is not None:
            route.code = route_data.code
        if route_data.name is not None:
            route.name = route_data.name
        if route_data.order is not None:
            route.order = route_data.order
        if route_data.measurement_types is not None:
            route.set_measurement_types(route_data.measurement_types)
        if route_data.registration_types is not None:
            route.set_registration_types(route_data.registration_types)
        if route_data.notes is not None:
            route.notes = route_data.notes
        if route_data.is_required is not None:
            route.is_required = route_data.is_required

        updated_route = await self.craft_route_repository.update(route)
        return CraftRouteResponseDTO.model_validate(updated_route)

    async def delete_craft_route(self, route_id: int) -> bool:
        """Delete craft route."""
        return await self.craft_route_repository.delete(route_id)

    async def reorder_craft_routes(self, craft_code: str, route_orders: List[tuple]) -> CraftOperationResultDTO:
        """Reorder routes for a craft."""
        try:
            success = await self.craft_route_repository.reorder_craft_routes(craft_code, route_orders)
            
            if success:
                return CraftOperationResultDTO(
                    success=True,
                    message="Routes reordered successfully",
                    details={"craft_code": craft_code, "reordered_count": len(route_orders)}
                )
            else:
                return CraftOperationResultDTO(
                    success=False,
                    message="Failed to reorder routes",
                    details={"craft_code": craft_code}
                )
        except Exception as e:
            return CraftOperationResultDTO(
                success=False,
                message=f"Failed to reorder routes: {str(e)}",
                details={"craft_code": craft_code}
            )