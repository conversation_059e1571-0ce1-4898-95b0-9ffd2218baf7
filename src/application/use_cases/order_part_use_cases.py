from typing import List, Optional
from datetime import datetime
from src.application.interfaces.order_part_repository_interface import OrderPartRepositoryInterface
from src.application.interfaces.order_bundle_repository_interface import OrderBundleRepositoryInterface
from src.application.interfaces.order_repository_interface import OrderRepositoryInterface
from src.application.interfaces.user_repository_interface import UserRepositoryInterface
from src.application.dto.order_part_dto import (
    OrderPartCreateDTO, OrderPartUpdateDTO, OrderPartResponseDTO, OrderPartWithBundlesDTO,
    OrderPartListDTO, OrderPartSearchDTO, OrderPartStatusUpdateDTO, BulkOrderPartCreateDTO,
    OrderPartOperationResultDTO, OrderPartStatisticsDTO, OrderPartSummaryDTO
)
from src.application.dto.order_bundle_dto import OrderBundleResponseDTO
from src.domain.entities.order_part import OrderPart, PartStatus, PartType
from src.domain.entities.order_bundle import OrderBundle


class OrderPartUseCases:
    """Use cases for order part management operations."""
    
    def __init__(
        self,
        order_part_repository: OrderPartRepositoryInterface,
        order_bundle_repository: OrderBundleRepositoryInterface,
        order_repository: OrderRepositoryInterface,
        user_repository: UserRepositoryInterface
    ):
        self.order_part_repository = order_part_repository
        self.order_bundle_repository = order_bundle_repository
        self.order_repository = order_repository
        self.user_repository = user_repository
    
    async def create_order_part(self, part_data: OrderPartCreateDTO, factory_id: int) -> OrderPartResponseDTO:
        """Create a new order part."""
        # Verify order exists in this factory
        order = await self.order_repository.get_by_order_no_and_factory(part_data.order_no, factory_id)
        if not order:
            raise ValueError(f"Order {part_data.order_no} not found in this factory")
        
        # Verify supervisor exists if provided
        if part_data.supervisor_user_id:
            supervisor = await self.user_repository.get_by_id(part_data.supervisor_user_id)
            if not supervisor:
                raise ValueError(f"Supervisor user {part_data.supervisor_user_id} not found")
        
        # Generate order part number
        part_type = PartType(part_data.part_type.value)
        order_part_no = OrderPart.generate_order_part_no(part_data.order_no, part_data.part_sequence, part_type)
        
        # Check if order part already exists
        existing_part = await self.order_part_repository.get_by_order_part_no_and_factory(
            order_part_no, factory_id, part_data.order_no
        )
        if existing_part:
            raise ValueError(f"Order part {order_part_no} already exists in this factory")
        
        # Create order part
        order_part = OrderPart(
            factory_id=factory_id,
            order_id=order.id,  # Set the order_id from the fetched order
            order_no=part_data.order_no,
            order_part_no=order_part_no,
            skc_no=part_data.skc_no,
            color=part_data.color,
            part_type=part_type,
            part_name=part_data.part_name,
            part_sequence=part_data.part_sequence,
            total_quantity=part_data.total_quantity,
            description=part_data.description,
            notes=part_data.notes,
            planned_start_date=part_data.planned_start_date,
            planned_end_date=part_data.planned_end_date,
            machine_no=part_data.machine_no,
            process_route=part_data.process_route,
            supervisor_user_id=part_data.supervisor_user_id
        )
        
        created_part = await self.order_part_repository.create(order_part)
        
        # Create order bundles if provided
        if part_data.order_bundles:
            await self._create_order_bundles_for_part(created_part, part_data.order_bundles, factory_id)
        
        return await self._build_order_part_dto(created_part)
    
    async def get_order_part_by_id(self, order_part_id: int) -> Optional[OrderPartResponseDTO]:
        """Get order part by ID."""
        order_part = await self.order_part_repository.get_by_id(order_part_id)
        if order_part:
            return await self._build_order_part_dto(order_part)
        return None
    
    async def get_order_part_with_bundles(self, order_part_id: int) -> Optional[OrderPartWithBundlesDTO]:
        """Get order part by ID with order bundles."""
        order_part = await self.order_part_repository.get_by_id(order_part_id)
        if order_part:
            return await self._build_order_part_with_bundles_dto(order_part)
        return None
    
    async def get_order_parts_by_order(self, order_no: str, factory_id: int) -> List[OrderPartResponseDTO]:
        """Get all order parts for an order in a specific factory."""
        order_parts = await self.order_part_repository.get_by_order_no_and_factory(order_no, factory_id)
        
        response_dtos = []
        for order_part in order_parts:
            dto = await self._build_order_part_dto(order_part)
            response_dtos.append(dto)
        
        return response_dtos
    
    async def get_all_order_parts(self, factory_id: int, search: Optional[OrderPartSearchDTO] = None, skip: int = 0, limit: int = 100) -> OrderPartListDTO:
        """Get all order parts with optional search filters."""
        if search:
            order_parts = await self._search_order_parts_with_filters(search, factory_id)
        else:
            order_parts = await self.order_part_repository.get_all(factory_id, skip, limit)
        
        # Convert to DTOs
        order_part_dtos = []
        for order_part in order_parts:
            order_part_dto = await self._build_order_part_dto(order_part)
            order_part_dtos.append(order_part_dto)
        
        return OrderPartListDTO(order_parts=order_part_dtos, total=len(order_part_dtos))
    
    async def update_order_part(self, order_part_id: int, part_data: OrderPartUpdateDTO) -> Optional[OrderPartResponseDTO]:
        """Update order part."""
        order_part = await self.order_part_repository.get_by_id(order_part_id)
        if not order_part:
            return None
        
        # Validate supervisor user if specified
        if part_data.supervisor_user_id:
            supervisor = await self.user_repository.get_by_id(part_data.supervisor_user_id)
            if not supervisor:
                raise ValueError(f"Supervisor user {part_data.supervisor_user_id} not found")
        
        # Update fields if provided
        if part_data.part_name is not None:
            order_part.part_name = part_data.part_name
        if part_data.color is not None:
            order_part.color = part_data.color
        if part_data.total_quantity is not None:
            order_part.total_quantity = part_data.total_quantity
        if part_data.status is not None:
            order_part.status = PartStatus(part_data.status.value)
        if part_data.completed_quantity is not None:
            order_part.completed_quantity = part_data.completed_quantity
            order_part.update_progress()
        if part_data.description is not None:
            order_part.description = part_data.description
        if part_data.notes is not None:
            order_part.notes = part_data.notes
        if part_data.planned_start_date is not None:
            order_part.planned_start_date = part_data.planned_start_date
        if part_data.planned_end_date is not None:
            order_part.planned_end_date = part_data.planned_end_date
        if part_data.actual_start_date is not None:
            order_part.actual_start_date = part_data.actual_start_date
        if part_data.actual_end_date is not None:
            order_part.actual_end_date = part_data.actual_end_date
        if part_data.machine_no is not None:
            order_part.machine_no = part_data.machine_no
        if part_data.process_route is not None:
            order_part.process_route = part_data.process_route
        if part_data.supervisor_user_id is not None:
            order_part.supervisor_user_id = part_data.supervisor_user_id
        
        updated_part = await self.order_part_repository.update(order_part)
        return await self._build_order_part_dto(updated_part)
    
    async def update_order_part_status(self, order_part_id: int, status_data: OrderPartStatusUpdateDTO) -> OrderPartOperationResultDTO:
        """Update order part status."""
        order_part = await self.order_part_repository.get_by_id(order_part_id)
        if not order_part:
            return OrderPartOperationResultDTO(
                success=False,
                message="Order part not found",
                order_part_id=order_part_id
            )
        
        try:
            old_status = order_part.status
            new_status = PartStatus(status_data.status.value)
            
            # Update status using order part methods
            if new_status == PartStatus.CUTTING:
                order_part.start_production()
            elif new_status == PartStatus.COMPLETED:
                order_part.complete_part()
            else:
                order_part.status = new_status
                order_part.updated_at = datetime.utcnow()
            
            # Add notes if provided
            if status_data.notes:
                order_part.notes = f"{order_part.notes or ''}\n[{datetime.utcnow()}] Status changed to {new_status.value}: {status_data.notes}".strip()
            
            await self.order_part_repository.update(order_part)
            
            return OrderPartOperationResultDTO(
                success=True,
                message=f"Order part status updated to {new_status.value}",
                order_part_id=order_part_id,
                order_part_no=order_part.order_part_no,
                details={"old_status": old_status.value, "new_status": new_status.value}
            )
        
        except ValueError as e:
            return OrderPartOperationResultDTO(
                success=False,
                message=f"Invalid status: {str(e)}",
                order_part_id=order_part_id
            )
        except Exception as e:
            return OrderPartOperationResultDTO(
                success=False,
                message=f"Failed to update status: {str(e)}",
                order_part_id=order_part_id
            )
    
    async def delete_order_part(self, order_part_id: int) -> bool:
        """Delete order part and all its bundles."""
        order_part = await self.order_part_repository.get_by_id(order_part_id)
        if not order_part:
            return False
        
        # Delete all order bundles first
        await self.order_bundle_repository.delete_by_order_part_no_and_factory(
            order_part.order_part_no, order_part.factory_id, order_part.order_no
        )
        
        # Delete the order part
        return await self.order_part_repository.delete(order_part_id)
    
    async def bulk_create_order_parts(self, bulk_data: BulkOrderPartCreateDTO, factory_id: int) -> List[OrderPartResponseDTO]:
        """Create multiple order parts for an order."""
        # Verify order exists in this factory
        order = await self.order_repository.get_by_order_no_and_factory(bulk_data.order_no, factory_id)
        if not order:
            raise ValueError(f"Order {bulk_data.order_no} not found in this factory")
        
        created_parts = []
        for part_data in bulk_data.order_parts:
            part_data.order_no = bulk_data.order_no  # Ensure consistency
            created_part = await self.create_order_part(part_data, factory_id)
            created_parts.append(created_part)
        
        return created_parts
    
    async def get_order_part_statistics(self, factory_id: int, order_no: Optional[str] = None) -> OrderPartStatisticsDTO:
        """Get order part statistics for a factory."""
        stats = await self.order_part_repository.get_order_part_statistics(factory_id, order_no)
        
        return OrderPartStatisticsDTO(
            total_order_parts=stats["total_order_parts"],
            total_quantity=stats["total_quantity"],
            completed_quantity=stats["completed_quantity"],
            completion_percentage=stats["completion_percentage"],
            status_breakdown=stats["status_breakdown"],
            planned_parts=stats["planned_parts"],
            cutting_parts=stats["cutting_parts"],
            sewing_parts=stats["sewing_parts"],
            quality_check_parts=stats["quality_check_parts"],
            completed_parts=stats["completed_parts"],
            on_hold_parts=stats["on_hold_parts"],
            cancelled_parts=stats["cancelled_parts"]
        )
    
    # Helper methods
    async def _create_order_bundles_for_part(self, order_part: OrderPart, bundle_configs, factory_id: int):
        """Create order bundles for an order part."""
        from src.application.use_cases.order_bundle_use_cases import OrderBundleUseCases
        
        # This would typically be injected, but for now we'll assume it's available
        # In a real implementation, you'd inject the OrderBundleUseCases
        pass
    
    async def _search_order_parts_with_filters(self, search: OrderPartSearchDTO, factory_id: int) -> List[OrderPart]:
        """Search order parts with multiple filters."""
        # This is a simplified version - in a real implementation,
        # you'd add the search functionality to the repository
        if search.order_no:
            return await self.order_part_repository.get_by_order_no_and_factory(search.order_no, factory_id)
        elif search.status:
            return await self.order_part_repository.get_by_status(PartStatus(search.status.value), factory_id)
        elif search.supervisor_user_id:
            return await self.order_part_repository.get_by_supervisor(search.supervisor_user_id, factory_id)
        else:
            return await self.order_part_repository.get_all(factory_id)
    
    async def _build_order_part_dto(self, order_part: OrderPart) -> OrderPartResponseDTO:
        """Build OrderPartResponseDTO from OrderPart entity."""
        dto = OrderPartResponseDTO.model_validate(order_part)
        
        # Add supervisor name if available
        if order_part.supervisor:
            dto.supervisor_name = order_part.supervisor.full_name or order_part.supervisor.username
        
        return dto
    
    async def _build_order_part_with_bundles_dto(self, order_part: OrderPart) -> OrderPartWithBundlesDTO:
        """Build OrderPartWithBundlesDTO from OrderPart entity."""
        dto = OrderPartWithBundlesDTO.model_validate(order_part)
        
        # Add supervisor name if available
        if order_part.supervisor:
            dto.supervisor_name = order_part.supervisor.full_name or order_part.supervisor.username
        
        # Get order bundles
        order_bundles = await self.order_bundle_repository.get_by_order_part_no_and_factory(
            order_part.order_part_no, order_part.factory_id, order_part.order_no
        )
        
        bundle_dtos = []
        for bundle in order_bundles:
            bundle_dto = await self._build_order_bundle_dto(bundle)
            bundle_dtos.append(bundle_dto)
        
        dto.order_bundles = bundle_dtos
        return dto
    
    async def _build_order_bundle_dto(self, order_bundle: OrderBundle) -> OrderBundleResponseDTO:
        """Build OrderBundleResponseDTO from OrderBundle entity."""
        dto = OrderBundleResponseDTO.model_validate(order_bundle)
        
        # Add worker names if available
        if order_bundle.cutter:
            dto.cutter_name = order_bundle.cutter.full_name or order_bundle.cutter.username
        if order_bundle.sewer:
            dto.sewer_name = order_bundle.sewer.full_name or order_bundle.sewer.username
        if order_bundle.qc_user:
            dto.qc_user_name = order_bundle.qc_user.full_name or order_bundle.qc_user.username
        
        # Add calculated fields
        dto.good_quantity = order_bundle.get_good_quantity()
        dto.processing_time = order_bundle.get_processing_time()
        dto.cutting_time = order_bundle.get_cutting_time()
        dto.sewing_time = order_bundle.get_sewing_time()
        dto.qc_time = order_bundle.get_qc_time()
        
        return dto