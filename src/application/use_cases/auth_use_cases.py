import uuid
from typing import <PERSON><PERSON>
from datetime import datetime
from src.domain.entities.user import User
from src.domain.entities.validation_code_record import Validation<PERSON>odeRecord
from src.domain.value_objects.validation_code import ValidationCodeType
from src.domain.services.validation_code_service import ValidationCodeService
from src.application.interfaces.user_repository_interface import UserRepositoryInterface
from src.application.interfaces.validation_code_repository_interface import ValidationCodeRepositoryInterface
from src.application.dto.auth_dto import (
    PhonePasswordLoginDTO, 
    PhoneSmsLoginDTO, 
    SendSmsCodeDTO,
    GenerateImageCodeDTO
)
from src.application.use_cases.user_use_cases import UserUseCases


class AuthUseCases:
    """Authentication use cases for phone-based login."""
    
    def __init__(
        self,
        user_repository: UserRepositoryInterface,
        validation_code_repository: ValidationCodeRepositoryInterface,
        validation_code_service: ValidationCodeService,
        user_use_cases: UserUseCases
    ):
        self.user_repository = user_repository
        self.validation_code_repository = validation_code_repository
        self.validation_code_service = validation_code_service
        self.user_use_cases = user_use_cases
    
    async def authenticate_with_phone_password(self, login_data: PhonePasswordLoginDTO) -> Optional[User]:
        """Authenticate user with phone + password + image validation code."""
        # Validate image code first
        if not await self._validate_image_code(login_data.session_id, login_data.image_code):
            raise ValueError("Invalid image validation code")
        
        # Normalize phone number
        phone = self.validation_code_service.normalize_phone(login_data.phone)
        
        # Get user by phone
        user = await self.user_repository.get_by_phone(phone)
        if not user:
            raise ValueError("Invalid phone number or password")
        
        # Verify password
        if not self.user_use_cases.verify_password(login_data.password, user.hashed_password):
            raise ValueError("Invalid phone number or password")
        
        if not user.is_active:
            raise ValueError("User account is disabled")
        
        # Mark image code as used
        await self._mark_image_code_as_used(login_data.session_id, login_data.image_code)
        
        return user
    
    async def authenticate_with_phone_sms(self, login_data: PhoneSmsLoginDTO) -> Optional[User]:
        """Authenticate user with phone + SMS code + image validation code."""
        # Validate image code first
        if not await self._validate_image_code(login_data.session_id, login_data.image_code):
            raise ValueError("Invalid image validation code")
        
        # Normalize phone number
        phone = self.validation_code_service.normalize_phone(login_data.phone)
        
        # Validate SMS code
        if not await self._validate_sms_code(phone, login_data.sms_code):
            raise ValueError("Invalid or expired SMS code")
        
        # Get user by phone
        user = await self.user_repository.get_by_phone(phone)
        if not user:
            raise ValueError("User not found")
        
        if not user.is_active:
            raise ValueError("User account is disabled")
        
        # Mark codes as used
        await self._mark_sms_code_as_used(phone, login_data.sms_code)
        await self._mark_image_code_as_used(login_data.session_id, login_data.image_code)
        
        return user
    
    async def generate_image_validation_code(self, request: GenerateImageCodeDTO) -> dict:
        """Generate image validation code."""
        session_id = request.session_id or str(uuid.uuid4())
        
        # Create validation code
        validation_code = self.validation_code_service.create_image_validation_code(session_id)
        
        # Save to database
        code_record = ValidationCodeRecord(
            code=validation_code.code,
            code_type=validation_code.code_type,
            identifier=validation_code.identifier,
            expires_at=validation_code.expires_at,
            created_at=validation_code.created_at
        )
        await self.validation_code_repository.create(code_record)
        
        # Generate image (placeholder - implement actual image generation)
        image_base64 = await self._generate_captcha_image(validation_code.code)
        
        return {
            "session_id": session_id,
            "image_base64": image_base64,
            "expires_in_seconds": 600  # 10 minutes
        }
    
    async def send_sms_validation_code(self, request: SendSmsCodeDTO) -> dict:
        """Send SMS validation code."""
        # Validate image code first
        if not await self._validate_image_code(request.session_id, request.image_code):
            raise ValueError("Invalid image validation code")
        
        # Normalize phone number
        phone = self.validation_code_service.normalize_phone(request.phone)
        
        # Validate phone format
        if not self.validation_code_service.validate_phone_format(phone):
            raise ValueError("Invalid phone number format")
        
        # Check rate limiting (max 5 SMS per hour)
        recent_count = await self.validation_code_repository.count_recent_codes(
            phone, ValidationCodeType.SMS, 60
        )
        if recent_count >= 5:
            raise ValueError("Too many SMS requests. Please try again later.")
        
        # Generate SMS code
        validation_code = self.validation_code_service.create_sms_validation_code(phone)
        
        # Save to database
        code_record = ValidationCodeRecord(
            code=validation_code.code,
            code_type=validation_code.code_type,
            identifier=validation_code.identifier,
            expires_at=validation_code.expires_at,
            created_at=validation_code.created_at
        )
        await self.validation_code_repository.create(code_record)
        
        # Send SMS (placeholder - implement actual SMS sending)
        await self._send_sms(phone, validation_code.code)
        
        # Mark image code as used
        await self._mark_image_code_as_used(request.session_id, request.image_code)
        
        return {
            "success": True,
            "message": "SMS code sent successfully",
            "expires_in_seconds": 300  # 5 minutes
        }
    
    async def _validate_image_code(self, session_id: str, code: str) -> bool:
        """Validate image validation code."""
        code_record = await self.validation_code_repository.get_latest_by_identifier_and_type(
            session_id, ValidationCodeType.IMAGE
        )
        return code_record and code_record.is_valid(code)
    
    async def _validate_sms_code(self, phone: str, code: str) -> bool:
        """Validate SMS validation code."""
        code_record = await self.validation_code_repository.get_latest_by_identifier_and_type(
            phone, ValidationCodeType.SMS
        )
        return code_record and code_record.is_valid(code)
    
    async def _mark_image_code_as_used(self, session_id: str, code: str) -> None:
        """Mark image validation code as used."""
        code_record = await self.validation_code_repository.get_latest_by_identifier_and_type(
            session_id, ValidationCodeType.IMAGE
        )
        if code_record and code_record.is_valid(code):
            await self.validation_code_repository.mark_as_used(code_record.id)
    
    async def _mark_sms_code_as_used(self, phone: str, code: str) -> None:
        """Mark SMS validation code as used."""
        code_record = await self.validation_code_repository.get_latest_by_identifier_and_type(
            phone, ValidationCodeType.SMS
        )
        if code_record and code_record.is_valid(code):
            await self.validation_code_repository.mark_as_used(code_record.id)
    
    async def _generate_captcha_image(self, code: str) -> str:
        """Generate captcha image using captcha service."""
        from src.infrastructure.external_services.captcha_service import create_captcha_service
        captcha_service = create_captcha_service()
        return await captcha_service.generate_image_captcha(code)
    
    async def _send_sms(self, phone: str, code: str) -> None:
        """Send SMS code using SMS service."""
        from src.infrastructure.external_services.sms_service import create_sms_service
        sms_service = create_sms_service()
        await sms_service.send_verification_code(phone, code)
    
    async def cleanup_expired_codes(self) -> int:
        """Clean up expired validation codes."""
        return await self.validation_code_repository.delete_expired_codes()