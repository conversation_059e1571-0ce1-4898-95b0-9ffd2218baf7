from typing import List, Optional
from datetime import datetime
from src.application.interfaces.order_bundle_repository_interface import OrderBundleRepositoryInterface
from src.application.interfaces.order_part_repository_interface import OrderPartRepositoryInterface
from src.application.interfaces.order_repository_interface import OrderRepositoryInterface
from src.application.interfaces.user_repository_interface import UserRepositoryInterface
from src.application.dto.order_bundle_dto import (
    OrderBundleCreateDTO, OrderBundleUpdateDTO, OrderBundleResponseDTO, OrderBundleListDTO,
    OrderBundleSearchDTO, OrderBundleStatusUpdateDTO, OrderBundleProductionUpdateDTO,
    BulkOrderBundleCreateDTO, OrderBundleOperationResultDTO, OrderBundleStatisticsDTO,
    OrderBundleSummaryDTO
)
from src.domain.entities.order_bundle import OrderBundle, BundleStatus


class OrderBundleUseCases:
    """Use cases for order bundle management operations."""
    
    def __init__(
        self,
        order_bundle_repository: OrderBundleRepositoryInterface,
        order_part_repository: OrderPartRepositoryInterface,
        order_repository: OrderRepositoryInterface,
        user_repository: UserRepositoryInterface
    ):
        self.order_bundle_repository = order_bundle_repository
        self.order_part_repository = order_part_repository
        self.order_repository = order_repository
        self.user_repository = user_repository
    
    async def create_order_bundle(self, bundle_data: OrderBundleCreateDTO, factory_id: int) -> OrderBundleResponseDTO:
        """Create a new order bundle."""
        # Verify order exists in this factory
        order = await self.order_repository.get_by_order_no_and_factory(bundle_data.order_no, factory_id)
        if not order:
            raise ValueError(f"Order {bundle_data.order_no} not found in this factory")
        
        # Verify order part exists in this factory
        order_part = await self.order_part_repository.get_by_order_part_no_and_factory(
            bundle_data.order_part_no, factory_id, bundle_data.order_no
        )
        if not order_part:
            raise ValueError(f"Order part {bundle_data.order_part_no} not found in this factory")
        
        # Verify users exist if provided
        if bundle_data.cutter_user_id:
            cutter = await self.user_repository.get_by_id(bundle_data.cutter_user_id)
            if not cutter:
                raise ValueError(f"Cutter user {bundle_data.cutter_user_id} not found")
        
        if bundle_data.sewer_user_id:
            sewer = await self.user_repository.get_by_id(bundle_data.sewer_user_id)
            if not sewer:
                raise ValueError(f"Sewer user {bundle_data.sewer_user_id} not found")
        
        if bundle_data.qc_user_id:
            qc_user = await self.user_repository.get_by_id(bundle_data.qc_user_id)
            if not qc_user:
                raise ValueError(f"QC user {bundle_data.qc_user_id} not found")
        
        # Generate order bundle number
        order_bundle_no = OrderBundle.generate_order_bundle_no(
            bundle_data.order_part_no, bundle_data.size, bundle_data.bundle_sequence
        )
        
        # Check if order bundle already exists
        existing_bundle = await self.order_bundle_repository.get_by_order_bundle_no_and_factory(
            order_bundle_no, factory_id, bundle_data.order_no
        )
        if existing_bundle:
            raise ValueError(f"Order bundle {order_bundle_no} already exists in this factory")
        
        # Create order bundle
        order_bundle = OrderBundle(
            factory_id=factory_id,
            order_no=bundle_data.order_no,
            order_bundle_no=order_bundle_no,
            order_part_no=bundle_data.order_part_no,
            order_part_id=order_part.id,
            skc_no=bundle_data.skc_no,
            color=bundle_data.color,
            size=bundle_data.size,
            bundle_sequence=bundle_data.bundle_sequence,
            quantity=bundle_data.quantity,
            notes=bundle_data.notes,
            planned_start_date=bundle_data.planned_start_date,
            planned_end_date=bundle_data.planned_end_date,
            cutting_machine=bundle_data.cutting_machine,
            sewing_machine=bundle_data.sewing_machine,
            cutter_user_id=bundle_data.cutter_user_id,
            sewer_user_id=bundle_data.sewer_user_id,
            qc_user_id=bundle_data.qc_user_id
        )
        
        created_bundle = await self.order_bundle_repository.create(order_bundle)
        return await self._build_order_bundle_dto(created_bundle)
    
    async def get_order_bundle_by_id(self, order_bundle_id: int) -> Optional[OrderBundleResponseDTO]:
        """Get order bundle by ID."""
        order_bundle = await self.order_bundle_repository.get_by_id(order_bundle_id)
        if order_bundle:
            return await self._build_order_bundle_dto(order_bundle)
        return None
    
    async def get_order_bundles_by_order_part(self, order_part_no: str, factory_id: int, order_no: str) -> List[OrderBundleResponseDTO]:
        """Get all order bundles for an order part in a specific factory."""
        order_bundles = await self.order_bundle_repository.get_by_order_part_no_and_factory(
            order_part_no, factory_id, order_no
        )
        
        response_dtos = []
        for order_bundle in order_bundles:
            dto = await self._build_order_bundle_dto(order_bundle)
            response_dtos.append(dto)
        
        return response_dtos
    
    async def get_order_bundles_by_order(self, order_no: str, factory_id: int) -> List[OrderBundleResponseDTO]:
        """Get all order bundles for an order in a specific factory."""
        order_bundles = await self.order_bundle_repository.get_by_order_no_and_factory(order_no, factory_id)
        
        response_dtos = []
        for order_bundle in order_bundles:
            dto = await self._build_order_bundle_dto(order_bundle)
            response_dtos.append(dto)
        
        return response_dtos
    
    async def get_all_order_bundles(self, factory_id: int, search: Optional[OrderBundleSearchDTO] = None, skip: int = 0, limit: int = 100) -> OrderBundleListDTO:
        """Get all order bundles with optional search filters."""
        if search:
            order_bundles = await self._search_order_bundles_with_filters(search, factory_id)
        else:
            order_bundles = await self.order_bundle_repository.get_all(factory_id, skip, limit)
        
        # Convert to DTOs
        order_bundle_dtos = []
        for order_bundle in order_bundles:
            order_bundle_dto = await self._build_order_bundle_dto(order_bundle)
            order_bundle_dtos.append(order_bundle_dto)
        
        return OrderBundleListDTO(order_bundles=order_bundle_dtos, total=len(order_bundle_dtos))
    
    async def update_order_bundle(self, order_bundle_id: int, bundle_data: OrderBundleUpdateDTO) -> Optional[OrderBundleResponseDTO]:
        """Update order bundle."""
        order_bundle = await self.order_bundle_repository.get_by_id(order_bundle_id)
        if not order_bundle:
            return None
        
        # Validate users if specified
        if bundle_data.cutter_user_id:
            cutter = await self.user_repository.get_by_id(bundle_data.cutter_user_id)
            if not cutter:
                raise ValueError(f"Cutter user {bundle_data.cutter_user_id} not found")
        
        if bundle_data.sewer_user_id:
            sewer = await self.user_repository.get_by_id(bundle_data.sewer_user_id)
            if not sewer:
                raise ValueError(f"Sewer user {bundle_data.sewer_user_id} not found")
        
        if bundle_data.qc_user_id:
            qc_user = await self.user_repository.get_by_id(bundle_data.qc_user_id)
            if not qc_user:
                raise ValueError(f"QC user {bundle_data.qc_user_id} not found")
        
        # Update fields if provided
        if bundle_data.quantity is not None:
            order_bundle.quantity = bundle_data.quantity
        if bundle_data.status is not None:
            order_bundle.status = BundleStatus(bundle_data.status.value)
        if bundle_data.completed_quantity is not None:
            order_bundle.completed_quantity = bundle_data.completed_quantity
            order_bundle.update_progress()
        if bundle_data.defective_quantity is not None:
            order_bundle.defective_quantity = bundle_data.defective_quantity
        if bundle_data.rework_quantity is not None:
            order_bundle.rework_quantity = bundle_data.rework_quantity
        if bundle_data.notes is not None:
            order_bundle.notes = bundle_data.notes
        if bundle_data.planned_start_date is not None:
            order_bundle.planned_start_date = bundle_data.planned_start_date
        if bundle_data.planned_end_date is not None:
            order_bundle.planned_end_date = bundle_data.planned_end_date
        if bundle_data.cutting_machine is not None:
            order_bundle.cutting_machine = bundle_data.cutting_machine
        if bundle_data.sewing_machine is not None:
            order_bundle.sewing_machine = bundle_data.sewing_machine
        if bundle_data.cutter_user_id is not None:
            order_bundle.cutter_user_id = bundle_data.cutter_user_id
        if bundle_data.sewer_user_id is not None:
            order_bundle.sewer_user_id = bundle_data.sewer_user_id
        if bundle_data.qc_user_id is not None:
            order_bundle.qc_user_id = bundle_data.qc_user_id
        if bundle_data.quality_level is not None:
            order_bundle.quality_level = bundle_data.quality_level
        if bundle_data.quality_notes is not None:
            order_bundle.quality_notes = bundle_data.quality_notes
        
        updated_bundle = await self.order_bundle_repository.update(order_bundle)
        return await self._build_order_bundle_dto(updated_bundle)
    
    async def update_order_bundle_status(self, order_bundle_id: int, status_data: OrderBundleStatusUpdateDTO) -> OrderBundleOperationResultDTO:
        """Update order bundle status."""
        order_bundle = await self.order_bundle_repository.get_by_id(order_bundle_id)
        if not order_bundle:
            return OrderBundleOperationResultDTO(
                success=False,
                message="Order bundle not found",
                order_bundle_id=order_bundle_id
            )
        
        try:
            old_status = order_bundle.status
            new_status = BundleStatus(status_data.status.value)
            
            # Update status using order bundle methods
            if new_status == BundleStatus.CUTTING:
                order_bundle.start_cutting(status_data.user_id, status_data.machine)
            elif new_status == BundleStatus.CUT_COMPLETED:
                order_bundle.complete_cutting()
            elif new_status == BundleStatus.SEWING:
                order_bundle.start_sewing(status_data.user_id, status_data.machine)
            elif new_status == BundleStatus.SEW_COMPLETED:
                order_bundle.complete_sewing()
            elif new_status == BundleStatus.QUALITY_CHECK:
                order_bundle.start_quality_check(status_data.user_id)
            elif new_status == BundleStatus.COMPLETED:
                order_bundle.complete_quality_check("A")  # Default quality level
            elif new_status == BundleStatus.REWORK:
                order_bundle.send_to_rework(0, status_data.notes)
            else:
                order_bundle.status = new_status
                order_bundle.updated_at = datetime.utcnow()
            
            # Add notes if provided
            if status_data.notes:
                order_bundle.notes = f"{order_bundle.notes or ''}\n[{datetime.utcnow()}] Status changed to {new_status.value}: {status_data.notes}".strip()
            
            await self.order_bundle_repository.update(order_bundle)
            
            return OrderBundleOperationResultDTO(
                success=True,
                message=f"Order bundle status updated to {new_status.value}",
                order_bundle_id=order_bundle_id,
                order_bundle_no=order_bundle.order_bundle_no,
                details={"old_status": old_status.value, "new_status": new_status.value}
            )
        
        except ValueError as e:
            return OrderBundleOperationResultDTO(
                success=False,
                message=f"Invalid status: {str(e)}",
                order_bundle_id=order_bundle_id
            )
        except Exception as e:
            return OrderBundleOperationResultDTO(
                success=False,
                message=f"Failed to update status: {str(e)}",
                order_bundle_id=order_bundle_id
            )
    
    async def update_order_bundle_production(self, order_bundle_id: int, production_data: OrderBundleProductionUpdateDTO) -> OrderBundleOperationResultDTO:
        """Update order bundle production data."""
        order_bundle = await self.order_bundle_repository.get_by_id(order_bundle_id)
        if not order_bundle:
            return OrderBundleOperationResultDTO(
                success=False,
                message="Order bundle not found",
                order_bundle_id=order_bundle_id
            )
        
        try:
            # Update production quantities
            if production_data.completed_quantity is not None:
                order_bundle.completed_quantity = production_data.completed_quantity
            if production_data.defective_quantity is not None:
                order_bundle.defective_quantity = production_data.defective_quantity
            if production_data.rework_quantity is not None:
                order_bundle.rework_quantity = production_data.rework_quantity
            if production_data.quality_level is not None:
                order_bundle.quality_level = production_data.quality_level
            if production_data.quality_notes is not None:
                order_bundle.quality_notes = production_data.quality_notes
            if production_data.notes is not None:
                order_bundle.notes = f"{order_bundle.notes or ''}\n[{datetime.utcnow()}] Production update: {production_data.notes}".strip()
            
            order_bundle.update_progress()
            await self.order_bundle_repository.update(order_bundle)
            
            return OrderBundleOperationResultDTO(
                success=True,
                message="Order bundle production updated successfully",
                order_bundle_id=order_bundle_id,
                order_bundle_no=order_bundle.order_bundle_no,
                details={
                    "completed_quantity": order_bundle.completed_quantity,
                    "defective_quantity": order_bundle.defective_quantity,
                    "progress_percentage": order_bundle.progress_percentage
                }
            )
        
        except Exception as e:
            return OrderBundleOperationResultDTO(
                success=False,
                message=f"Failed to update production: {str(e)}",
                order_bundle_id=order_bundle_id
            )
    
    async def delete_order_bundle(self, order_bundle_id: int) -> bool:
        """Delete order bundle."""
        return await self.order_bundle_repository.delete(order_bundle_id)
    
    async def bulk_create_order_bundles(self, bulk_data: BulkOrderBundleCreateDTO, factory_id: int) -> List[OrderBundleResponseDTO]:
        """Create multiple order bundles for an order part."""
        # Verify order exists in this factory
        order = await self.order_repository.get_by_order_no_and_factory(bulk_data.order_no, factory_id)
        if not order:
            raise ValueError(f"Order {bulk_data.order_no} not found in this factory")
        
        # Verify order part exists
        order_part = await self.order_part_repository.get_by_order_part_no_and_factory(
            bulk_data.order_part_no, factory_id, bulk_data.order_no
        )
        if not order_part:
            raise ValueError(f"Order part {bulk_data.order_part_no} not found in this factory")
        
        created_bundles = []
        for bundle_data in bulk_data.order_bundles:
            bundle_data.order_no = bulk_data.order_no  # Ensure consistency
            bundle_data.order_part_no = bulk_data.order_part_no  # Ensure consistency
            created_bundle = await self.create_order_bundle(bundle_data, factory_id)
            created_bundles.append(created_bundle)
        
        return created_bundles
    
    async def get_order_bundle_statistics(self, factory_id: int, order_no: Optional[str] = None, order_part_no: Optional[str] = None) -> OrderBundleStatisticsDTO:
        """Get order bundle statistics for a factory."""
        stats = await self.order_bundle_repository.get_order_bundle_statistics(factory_id, order_no, order_part_no)
        
        return OrderBundleStatisticsDTO(
            total_order_bundles=stats["total_order_bundles"],
            total_quantity=stats["total_quantity"],
            completed_quantity=stats["completed_quantity"],
            defective_quantity=stats["defective_quantity"],
            rework_quantity=stats["rework_quantity"],
            good_quantity=stats["good_quantity"],
            completion_percentage=stats["completion_percentage"],
            quality_rate=stats["quality_rate"],
            status_breakdown=stats["status_breakdown"],
            planned_bundles=stats["planned_bundles"],
            cutting_bundles=stats["cutting_bundles"],
            cut_completed_bundles=stats["cut_completed_bundles"],
            sewing_bundles=stats["sewing_bundles"],
            sew_completed_bundles=stats["sew_completed_bundles"],
            quality_check_bundles=stats["quality_check_bundles"],
            completed_bundles=stats["completed_bundles"],
            rework_bundles=stats["rework_bundles"],
            on_hold_bundles=stats["on_hold_bundles"],
            cancelled_bundles=stats["cancelled_bundles"]
        )
    
    # Helper methods
    async def _search_order_bundles_with_filters(self, search: OrderBundleSearchDTO, factory_id: int) -> List[OrderBundle]:
        """Search order bundles with multiple filters."""
        # This is a simplified version - in a real implementation,
        # you'd add the search functionality to the repository
        if search.order_no:
            return await self.order_bundle_repository.get_by_order_no_and_factory(search.order_no, factory_id)
        elif search.order_part_no:
            return await self.order_bundle_repository.get_by_order_part_no_and_factory(
                search.order_part_no, factory_id, search.order_no or ""
            )
        elif search.status:
            return await self.order_bundle_repository.get_by_status(BundleStatus(search.status.value), factory_id)
        elif search.size:
            return await self.order_bundle_repository.get_by_size(search.size, factory_id)
        elif search.cutter_user_id:
            return await self.order_bundle_repository.get_by_user(search.cutter_user_id, factory_id, "cutter")
        elif search.sewer_user_id:
            return await self.order_bundle_repository.get_by_user(search.sewer_user_id, factory_id, "sewer")
        elif search.qc_user_id:
            return await self.order_bundle_repository.get_by_user(search.qc_user_id, factory_id, "qc")
        else:
            return await self.order_bundle_repository.get_all(factory_id)
    
    async def _build_order_bundle_dto(self, order_bundle: OrderBundle) -> OrderBundleResponseDTO:
        """Build OrderBundleResponseDTO from OrderBundle entity."""
        dto = OrderBundleResponseDTO.model_validate(order_bundle)
        
        # Add worker names if available
        if order_bundle.cutter:
            dto.cutter_name = order_bundle.cutter.full_name or order_bundle.cutter.username
        if order_bundle.sewer:
            dto.sewer_name = order_bundle.sewer.full_name or order_bundle.sewer.username
        if order_bundle.qc_user:
            dto.qc_user_name = order_bundle.qc_user.full_name or order_bundle.qc_user.username
        
        # Add calculated fields
        dto.good_quantity = order_bundle.get_good_quantity()
        dto.processing_time = order_bundle.get_processing_time()
        dto.cutting_time = order_bundle.get_cutting_time()
        dto.sewing_time = order_bundle.get_sewing_time()
        dto.qc_time = order_bundle.get_qc_time()
        
        return dto