from typing import List, Optional
from datetime import datetime
from decimal import Decimal
from src.application.interfaces.order_repository_interface import OrderRepositoryInterface
from src.application.interfaces.order_line_repository_interface import OrderLineRepositoryInterface
from src.application.interfaces.user_repository_interface import UserRepositoryInterface
from src.application.interfaces.craft_repository_interface import CraftRepositoryInterface
from src.application.interfaces.craft_route_repository_interface import CraftRouteRepositoryInterface
from src.application.dto.order_dto import (
    OrderCreateDTO, OrderUpdateDTO, OrderResponseDTO, OrderDetailResponseDTO,
    OrderListDTO, OrderSearchDTO, OrderStatusUpdateDTO, OrderCraftProgressDTO,
    OrderProductionUpdateDTO, BulkOrderLineCreateDTO, OrderOperationResultDTO,
    OrderStatisticsDTO, OrderLineStatisticsDTO, OrderSummaryDTO, OrderDashboardDTO,
    OrderLineCreateDTO, OrderLineUpdateDTO, OrderLineResponseDTO, OrderAmountUpdateDTO
)
from src.domain.entities.order import Order, OrderStatus
from src.domain.entities.order_line import OrderLine


class OrderUseCases:
    """Use cases for order management operations."""

    def __init__(
        self,
        order_repository: OrderRepositoryInterface,
        order_line_repository: OrderLineRepositoryInterface,
        user_repository: UserRepositoryInterface,
        craft_repository: CraftRepositoryInterface,
        craft_route_repository: CraftRouteRepositoryInterface,
        order_craft_use_cases=None,
        order_part_use_cases=None,
        skill_repository=None
    ):
        self.order_repository = order_repository
        self.order_line_repository = order_line_repository
        self.user_repository = user_repository
        self.craft_repository = craft_repository
        self.craft_route_repository = craft_route_repository
        self.order_craft_use_cases = order_craft_use_cases
        self.order_part_use_cases = order_part_use_cases
        self.skill_repository = skill_repository

    # Order operations
    async def create_order(self, order_data: OrderCreateDTO, current_factory_id: int) -> OrderDetailResponseDTO:
        """Create a new order with order lines, order crafts, and order craft routes in a single DB transaction."""
        from src.infrastructure.database.database import Database
        from config import settings
        db = Database(settings.database.url)
        
        async with db.session_factory() as session:
            try:
                # Check if order number already exists in this factory
                existing_order = await self.order_repository.get_by_order_no_and_factory(order_data.order_no, current_factory_id, session=session)
                if existing_order:
                    raise ValueError(f"Order with order_no '{order_data.order_no}' already exists in this factory")

                # Validate owner user if specified
                if order_data.owner_user_id:
                    owner = await self.user_repository.get_by_id(order_data.owner_user_id)
                    if not owner:
                        raise ValueError(f"Owner user with ID {order_data.owner_user_id} not found")

                # Create order
                order = Order(
                    factory_id=current_factory_id,
                    skc_no=order_data.skc_no,
                    external_skc_no=order_data.external_skc_no,
                    order_no=order_data.order_no,
                    external_order_no=order_data.external_order_no,
                    external_order_no2=order_data.external_order_no2,
                    cost=order_data.cost,
                    price=order_data.price,
                    expect_finished_at=order_data.expect_finished_at,
                    owner_user_id=order_data.owner_user_id,
                    description=order_data.description,
                    notes=order_data.notes
                )

                created_order = await self.order_repository.create(order)

                # Create order lines
                order_lines = []
                for line_data in order_data.order_lines:
                    order_line_no = OrderLine.generate_order_line_no(order_data.order_no, line_data.size)
                    # Check if order line already exists in this factory
                    existing_line = await self.order_line_repository.get_by_order_line_no_and_factory(order_line_no, current_factory_id, session=session)
                    if existing_line:
                        raise ValueError(f"Order line '{order_line_no}' already exists in this factory")
                    order_line = OrderLine(
                        order=created_order,
                        factory_id=current_factory_id,
                        order_no=order_data.order_no,
                        order_line_no=order_line_no,
                        size=line_data.size,
                        amount=line_data.amount,
                        notes=line_data.notes
                    )
                    order_lines.append(order_line)

                created_lines = await self.order_line_repository.bulk_create(order_lines)

                # Update order total amount
                total_amount = sum(line.amount for line in created_lines)
                created_order.total_amount = total_amount
                session.add(created_order)
                await session.flush()

                # Create order crafts and routes if provided (all in same transaction)
                if order_data.order_crafts and self.order_craft_use_cases:
                    await self._create_order_crafts_in_transaction(
                        session,
                        order_data.order_no,
                        current_factory_id,
                        order_data.order_crafts
                    )

                await session.commit()
                # Build response with all related data
                return await self._build_order_detail_dto(created_order)
                
            except Exception as e:
                await session.rollback()
                raise e

    async def get_order_by_id(self, order_id: int) -> Optional[OrderDetailResponseDTO]:
        """Get order by ID with order lines."""
        order = await self.order_repository.get_by_id(order_id)
        if order:
            return await self._build_order_detail_dto(order)
        return None

    async def get_order_by_order_no(self, order_no: str, factory_id: int) -> Optional[OrderDetailResponseDTO]:
        """Get order by order number and factory ID with order lines."""
        order = await self.order_repository.get_by_order_no_and_factory(order_no, factory_id)
        if order:
            return await self._build_order_detail_dto(order)
        return None

    async def get_all_orders(self, search: Optional[OrderSearchDTO] = None, skip: int = 0, limit: int = 100) -> OrderListDTO:
        """Get all orders with optional search filters."""
        if search:
            orders = await self._search_orders_with_filters(search)
        else:
            orders = await self.order_repository.get_all(skip, limit)

        # Convert to DTOs
        order_dtos = []
        for order in orders:
            order_dto = OrderResponseDTO.model_validate(order)
            order_dto.completion_percentage = order.get_completion_percentage()
            order_dtos.append(order_dto)

        return OrderListDTO(orders=order_dtos, total=len(order_dtos))

    async def update_order(self, order_id: int, order_data: OrderUpdateDTO) -> Optional[OrderResponseDTO]:
        """Update order."""
        order = await self.order_repository.get_by_id(order_id)
        if not order:
            return None

        # Validate owner user if specified
        if order_data.owner_user_id:
            owner = await self.user_repository.get_by_id(order_data.owner_user_id)
            if not owner:
                raise ValueError(f"Owner user with ID {order_data.owner_user_id} not found")

        # Update fields if provided
        if order_data.skc_no is not None:
            order.skc_no = order_data.skc_no
        if order_data.external_skc_no is not None:
            order.external_skc_no = order_data.external_skc_no
        if order_data.external_order_no is not None:
            order.external_order_no = order_data.external_order_no
        if order_data.external_order_no2 is not None:
            order.external_order_no2 = order_data.external_order_no2
        if order_data.cost is not None:
            order.cost = order_data.cost  # type: ignore
        if order_data.price is not None:
            order.price = order_data.price  # type: ignore
        if order_data.expect_finished_at is not None:
            order.expect_finished_at = order_data.expect_finished_at
        if order_data.owner_user_id is not None:
            order.owner_user_id = order_data.owner_user_id
        if order_data.description is not None:
            order.description = order_data.description
        if order_data.notes is not None:
            order.notes = order_data.notes

        updated_order = await self.order_repository.update(order)
        
        order_dto = OrderResponseDTO.model_validate(updated_order)
        order_dto.completion_percentage = updated_order.get_completion_percentage()
        return order_dto

    async def update_order_status(self, order_id: int, status_data: OrderStatusUpdateDTO) -> OrderOperationResultDTO:
        """Update order status."""
        order = await self.order_repository.get_by_id(order_id)
        if not order:
            return OrderOperationResultDTO(
                success=False,
                message="Order not found",
                order_id=order_id,
                order_no=None,
                details={}
            )

        try:
            # Validate status
            new_status = OrderStatus(status_data.status)
            
            # Update status using order methods
            if new_status == OrderStatus.IN_PROGRESS:
                order.start_order()
            elif new_status == OrderStatus.COMPLETED:
                order.complete_order()
            elif new_status == OrderStatus.CANCELLED:
                order.cancel_order()
            elif new_status == OrderStatus.ON_HOLD:
                order.hold_order()
            elif new_status == OrderStatus.DELAYED:
                order.delay_order()
            else:
                order.status = new_status
                order.updated_at = datetime.utcnow()

            # Add notes if provided
            if status_data.notes:
                order.notes = f"{order.notes or ''}\n[{datetime.utcnow()}] Status changed to {new_status.value}: {status_data.notes}".strip()

            await self.order_repository.update(order)

            return OrderOperationResultDTO(
                success=True,
                message=f"Order status updated to {new_status.value}",
                order_id=order_id,
                order_no=order.order_no,
                details={"new_status": new_status.value}
            )

        except ValueError as e:
            return OrderOperationResultDTO(
                success=False,
                message=f"Invalid status: {str(e)}",
                order_id=order_id,
                order_no=None,
                details={}
            )
        except Exception as e:
            return OrderOperationResultDTO(
                success=False,
                message=f"Failed to update status: {str(e)}",
                order_id=order_id,
                order_no=None,
                details={}
            )

    async def start_order(self, order_no: str) -> OrderOperationResultDTO:
        """Start an order - change status from PENDING to IN_PROGRESS and start first order craft and route."""
        from src.infrastructure.database.database import Database
        from config import settings
        db = Database(settings.database.url)
        
        async with db.session_factory() as session:
            try:
                # Get order
                order = await self.order_repository.get_by_order_no(order_no)
                if not order:
                    return OrderOperationResultDTO(
                        success=False,
                        message=f"Order {order_no} not found",
                        order_id=None,
                        order_no=order_no,
                        details={}
                    )

                # Check if order can be started
                if order.status != OrderStatus.PENDING:
                    return OrderOperationResultDTO(
                        success=False,
                        message=f"Order {order_no} cannot be started. Current status: {order.status.value}",
                        order_id=order.id,
                        order_no=order_no,
                        details={"current_status": order.status.value}
                    )

                # Start the order
                order.start_order()
                await self.order_repository.update(order)

                # Get first order craft
                if self.order_craft_use_cases:
                    order_crafts = await self.order_craft_use_cases.get_order_crafts_by_order(order_no)
                    if order_crafts:
                        # Sort by order and get first craft
                        first_craft = min(order_crafts, key=lambda x: x.order)
                        
                        # Start first order craft if it's pending
                        if first_craft.status == "pending":
                            # Update order craft status to in_progress
                            first_craft_entity = await self.order_craft_use_cases.order_craft_repository.get_by_id(first_craft.id)
                            if first_craft_entity:
                                first_craft_entity.start_craft()
                                await self.order_craft_use_cases.order_craft_repository.update(first_craft_entity)
                                
                                # Get first route of the first craft
                                routes = await self.order_craft_use_cases.order_craft_route_repository.get_by_order_craft_id(first_craft.id)
                                if routes:
                                    # Sort by order and get first route
                                    first_route = min(routes, key=lambda x: x.order)
                                    if first_route.status == "pending":
                                        first_route.start_route()
                                        await self.order_craft_use_cases.order_craft_route_repository.update(first_route)

                await session.commit()

                return OrderOperationResultDTO(
                    success=True,
                    message=f"Order {order_no} started successfully",
                    order_id=order.id,
                    order_no=order_no,
                    details={
                        "old_status": "pending",
                        "new_status": "in_progress",
                        "started_at": order.started_at.isoformat() if order.started_at else None
                    }
                )

            except Exception as e:
                await session.rollback()
                return OrderOperationResultDTO(
                    success=False,
                    message=f"Failed to start order: {str(e)}",
                    order_id=None,
                    order_no=order_no,
                    details={}
                )

    async def update_craft_progress(self, order_id: int, progress_data: OrderCraftProgressDTO) -> OrderOperationResultDTO:
        """Update order craft progress."""
        order = await self.order_repository.get_by_id(order_id)
        if not order:
            return OrderOperationResultDTO(
                success=False,
                message="Order not found",
                order_id=order_id,
                order_no=None,
                details={}
            )

        try:
            # Validate craft exists
            craft = await self.craft_repository.get_by_code(progress_data.craft_code)
            if not craft:
                raise ValueError(f"Craft with code '{progress_data.craft_code}' not found")

            # Validate craft route if specified
            if progress_data.craft_route_id:
                craft_route = await self.craft_route_repository.get_by_id(progress_data.craft_route_id)
                if not craft_route:
                    raise ValueError(f"Craft route with ID {progress_data.craft_route_id} not found")
                if craft_route.craft_code != progress_data.craft_code:
                    raise ValueError("Craft route does not belong to the specified craft")

            # Update craft progress
            order.update_craft_progress(progress_data.craft_code, progress_data.craft_route_id)

            # Add progress notes if provided
            if progress_data.notes:
                order.notes = f"{order.notes or ''}\n[{datetime.utcnow()}] Craft progress: {progress_data.notes}".strip()

            await self.order_repository.update(order)

            return OrderOperationResultDTO(
                success=True,
                message=f"Craft progress updated to {progress_data.craft_code}",
                order_id=order_id,
                order_no=order.order_no,
                details={
                    "craft_code": progress_data.craft_code,
                    "craft_route_id": progress_data.craft_route_id
                }
            )

        except ValueError as e:
            return OrderOperationResultDTO(
                success=False,
                message=str(e),
                order_id=order_id,
                order_no=None,
                details={}
            )
        except Exception as e:
            return OrderOperationResultDTO(
                success=False,
                message=f"Failed to update craft progress: {str(e)}",
                order_id=order_id,
                order_no=None,
                details={}
            )

    async def update_order_amount(self, order_id: int, amount_data: OrderAmountUpdateDTO) -> OrderOperationResultDTO:
        """Update order total amount."""
        order = await self.order_repository.get_by_id(order_id)
        if not order:
            return OrderOperationResultDTO(
                success=False,
                message="Order not found",
                order_id=order_id,
                order_no=None,
                details={}
            )

        try:
            old_amount = order.total_amount
            order.total_amount = amount_data.total_amount

            # Add notes if provided
            if amount_data.notes:
                order.notes = f"{order.notes or ''}\n[{datetime.utcnow()}] Amount updated from {old_amount} to {amount_data.total_amount}: {amount_data.notes}".strip()

            order.updated_at = datetime.utcnow()
            await self.order_repository.update(order)

            return OrderOperationResultDTO(
                success=True,
                message=f"Order amount updated from {old_amount} to {amount_data.total_amount}",
                order_id=order_id,
                order_no=order.order_no,
                details={
                    "old_amount": old_amount,
                    "new_amount": amount_data.total_amount
                }
            )

        except Exception as e:
            return OrderOperationResultDTO(
                success=False,
                message=f"Failed to update order amount: {str(e)}",
                order_id=order_id,
                order_no=order.order_no,
                details={}
            )

    async def delete_order(self, order_id: int) -> bool:
        """Delete order and all its order lines."""
        order = await self.order_repository.get_by_id(order_id)
        if not order:
            return False

        # Delete all order lines first
        await self.order_line_repository.delete_by_order_no(order.order_no)
        
        # Delete the order
        return await self.order_repository.delete(order_id)

    # Order Line operations
    async def add_order_lines(self, bulk_data: BulkOrderLineCreateDTO, factory_id: int) -> List[OrderLineResponseDTO]:
        """Add multiple order lines to an existing order."""
        # Validate order exists in this factory
        order = await self.order_repository.get_by_order_no_and_factory(bulk_data.order_no, factory_id)
        if not order:
            raise ValueError(f"Order with order_no '{bulk_data.order_no}' not found in this factory")

        order_lines = []
        for line_data in bulk_data.order_lines:
            order_line_no = OrderLine.generate_order_line_no(bulk_data.order_no, line_data.size)
            
            # Check if order line already exists in this factory
            existing_line = await self.order_line_repository.get_by_order_line_no_and_factory(order_line_no, factory_id)
            if existing_line:
                raise ValueError(f"Order line '{order_line_no}' already exists in this factory")

            order_line = OrderLine(
                factory_id=factory_id,
                order_no=bulk_data.order_no,
                order_line_no=order_line_no,
                size=line_data.size,
                amount=line_data.amount,
                notes=line_data.notes
            )
            order_lines.append(order_line)

        created_lines = await self.order_line_repository.bulk_create(order_lines)

        # Update order total amount
        all_lines = await self.order_line_repository.get_by_order_no_and_factory(bulk_data.order_no, factory_id)
        total_amount = sum(line.amount for line in all_lines)
        order.total_amount = total_amount
        await self.order_repository.update(order)

        # Convert to DTOs
        return [self._build_order_line_dto(line) for line in created_lines]

    async def update_production(self, production_data: OrderProductionUpdateDTO) -> OrderOperationResultDTO:
        """Update production quantities for order lines."""
        try:
            # Validate all order lines exist
            for update in production_data.order_line_updates:
                order_line_id = update.order_line_id
                order_line = await self.order_line_repository.get_by_id(order_line_id)
                if not order_line:
                    raise ValueError(f"Order line with ID {order_line_id} not found")

            # Perform bulk updates
            production_updates = [
                (update.order_line_id, update.produced_amount)
                for update in production_data.order_line_updates
                if update.produced_amount is not None
            ]
            
            completion_updates = [
                (update.order_line_id, update.completed_amount)
                for update in production_data.order_line_updates
                if update.completed_amount is not None
            ]

            success = True
            if production_updates:
                success &= await self.order_line_repository.bulk_update_production(production_updates)
            if completion_updates:
                success &= await self.order_line_repository.bulk_update_completion(completion_updates)

            if success:
                return OrderOperationResultDTO(
                    order_id=None,
                    order_no=None,
                    success=True,
                    message=f"Updated production for {len(production_data.order_line_updates)} order lines",
                    details={
                        "updated_lines": len(production_data.order_line_updates),
                        "production_updates": len(production_updates),
                        "completion_updates": len(completion_updates)
                    }
                )
            else:
                return OrderOperationResultDTO(
                    order_id=None,
                    order_no=None,
                    success=False,
                    message="Failed to update production quantities",
                    details={}
                )

        except ValueError as e:
            return OrderOperationResultDTO(
                order_id=None,
                order_no=None,
                success=False,
                message=str(e),
                details={}
            )
        except Exception as e:
            return OrderOperationResultDTO(
                success=False,
                message=f"Failed to update production: {str(e)}",
                order_id=None,
                order_no=None,
                details={}
            )

    # Statistics and Dashboard
    async def get_order_statistics(self, user_id: Optional[int] = None) -> OrderStatisticsDTO:
        """Get order statistics."""
        stats = await self.order_repository.get_order_statistics(user_id)
        line_stats = await self.order_line_repository.get_order_line_statistics()
        
        return OrderStatisticsDTO(
            total_orders=stats["total_orders"],
            active_orders=stats["active_orders"],
            completed_orders=stats["status_breakdown"].get("completed", 0),
            overdue_orders=stats["overdue_orders"],
            status_breakdown=stats["status_breakdown"],
            total_amount=line_stats["total_amount"],
            total_completed_amount=line_stats["total_completed"],
            completion_rate=line_stats["completion_percentage"]
        )

    async def get_dashboard_data(self, user_id: Optional[int] = None) -> OrderDashboardDTO:
        """Get comprehensive dashboard data."""
        # Get statistics
        order_stats = await self.get_order_statistics(user_id)
        line_stats = await self.order_line_repository.get_order_line_statistics()
        
        # Get recent orders
        recent_orders_data = await self.order_repository.get_all(0, 10)
        recent_orders = [self._build_order_summary_dto(order) for order in recent_orders_data]
        
        # Get overdue orders
        overdue_orders_data = await self.order_repository.get_overdue_orders()
        overdue_orders = [self._build_order_summary_dto(order) for order in overdue_orders_data[:10]]
        
        # Get orders by craft
        orders_by_craft = {}
        active_orders = await self.order_repository.get_active_orders()
        for order in active_orders:
            craft = order.current_craft or "未分配"
            if craft not in orders_by_craft:
                orders_by_craft[craft] = 0
            orders_by_craft[craft] += 1
        
        return OrderDashboardDTO(
            order_statistics=order_stats,
            recent_orders=recent_orders,
            overdue_orders=overdue_orders,
            orders_by_craft=orders_by_craft,
            production_summary=OrderLineStatisticsDTO.model_validate(line_stats)
        )

    # Helper methods
    async def _search_orders_with_filters(self, search: OrderSearchDTO) -> List[Order]:
        """Search orders with multiple filters."""
        if search.search_term:
            orders = await self.order_repository.search_orders(search.search_term)
        else:
            orders = await self.order_repository.get_all()

        # Apply additional filters
        filtered_orders = []
        for order in orders:
            # Status filter
            if search.status and order.status.value != search.status:
                continue
            
            # Owner filter
            if search.owner_user_id and order.owner_user_id != search.owner_user_id:
                continue
            
            # Current craft filter
            if search.current_craft and order.current_craft != search.current_craft:
                continue
            
            # Date range filter
            if search.start_date or search.end_date:
                date_field = getattr(order, search.date_field or "created_at")
                if search.start_date and date_field < search.start_date:
                    continue
                if search.end_date and date_field > search.end_date:
                    continue
            
            filtered_orders.append(order)

        return filtered_orders

    async def _create_order_crafts_in_transaction(self, session, order_no: str, current_factory_id: int, order_craft_configs):
        """Create order crafts and routes within an existing transaction."""
        from src.domain.entities.order_craft import OrderCraft
        from src.domain.entities.order_craft_route import OrderCraftRoute
        
        order_crafts = []
        order_craft_routes_to_create = []
        
        for config in order_craft_configs:
            # Validate craft exists
            craft = await self.craft_repository.get_by_code(config.craft_code)
            if not craft:
                raise ValueError(f"Craft {config.craft_code} not found")
            
            # Create order craft
            order_craft = OrderCraft(
                order_no=order_no,
                factory_id=current_factory_id,
                craft_code=config.craft_code,
                craft_name=config.craft_name,
                order=config.order,
                is_required=config.is_required,
                estimated_duration_hours=config.estimated_duration_hours,
                notes=config.notes
            )
            
            order_crafts.append(order_craft)
        
        # Create order crafts first
        session.add_all(order_crafts)
        await session.flush()
        
        # Refresh to get IDs
        for order_craft in order_crafts:
            await session.refresh(order_craft)
        
        # Create order craft routes for each order craft
        for i, order_craft in enumerate(order_crafts):
            config = order_craft_configs[i]
            route_configs = getattr(config, 'order_craft_routes', [])
            
            for route_config in route_configs:
                # Validate skill exists (assuming skill repository is available)
                if self.skill_repository is not None:
                    skill = await self.skill_repository.get_by_code(route_config.skill_code)
                    if not skill:
                        raise ValueError(f"Skill {route_config.skill_code} not found")
                
                # Validate assigned user if specified
                if route_config.assigned_user_id:
                    user = await self.user_repository.get_by_id(route_config.assigned_user_id)
                    if not user:
                        raise ValueError(f"User {route_config.assigned_user_id} not found")
                
                order_craft_route = OrderCraftRoute(
                    order_craft_id=order_craft.id,
                    factory_id=current_factory_id,
                    skill_code=route_config.skill_code,
                    name=route_config.name,
                    code=route_config.code,
                    order=route_config.order,
                    measurement_types=getattr(route_config, 'measurement_types', None),
                    registration_types=getattr(route_config, 'registration_types', None),
                    is_required=route_config.is_required,
                    assigned_user_id=route_config.assigned_user_id,
                    estimated_duration_minutes=getattr(route_config, 'estimated_duration_minutes', None),
                    price=getattr(route_config, 'price', None),
                    total_cost=getattr(route_config, 'total_cost', None),
                    notes=getattr(route_config, 'notes', None)
                )
                
                order_craft_routes_to_create.append(order_craft_route)
        
        # Create order craft routes if any
        if order_craft_routes_to_create:
            session.add_all(order_craft_routes_to_create)
            await session.flush()

    async def _build_order_detail_dto(self, order: Order) -> OrderDetailResponseDTO:
        """Build OrderDetailResponseDTO from Order entity with order lines, crafts, craft routes, and order parts."""
        order_lines = await self.order_line_repository.get_by_order_no_and_factory(order.order_no, order.factory_id)
        
        # Build DTO manually to avoid triggering SQLAlchemy relationship lazy loads
        order_dto = OrderDetailResponseDTO(
            id=order.id,
            skc_no=order.skc_no,
            external_skc_no=order.external_skc_no,
            order_no=order.order_no,
            external_order_no=order.external_order_no,
            external_order_no2=order.external_order_no2,
            cost=Decimal(str(order.cost)) if order.cost is not None else None,
            price=Decimal(str(order.price)) if order.price is not None else None,
            expect_finished_at=order.expect_finished_at,
            owner_user_id=order.owner_user_id,
            description=order.description,
            notes=order.notes,
            total_amount=order.total_amount,
            status=order.status.value,  # Convert enum to string
            current_craft=order.current_craft,
            current_craft_route=order.current_craft_route,
            created_at=order.created_at,
            finished_at=order.finished_at,
            updated_at=order.updated_at,
            completion_percentage=order.get_completion_percentage(),
            order_lines=[self._build_order_line_dto(line) for line in order_lines],
            order_crafts=[],  # Will be set below if available
            order_parts=[]   # Will be set below if available
        )
        
        # Add order crafts with routes if order_craft_use_cases is available
        if self.order_craft_use_cases:
            try:
                order_crafts = await self.order_craft_use_cases.get_order_crafts_by_order(order.order_no)
                order_dto.order_crafts = order_crafts
            except Exception:
                order_dto.order_crafts = []
        else:
            order_dto.order_crafts = []
        
        # Add order parts if order_part_use_cases is available
        if self.order_part_use_cases:
            try:
                order_parts = await self.order_part_use_cases.get_order_parts_by_order(order.order_no, order.factory_id)
                order_dto.order_parts = order_parts
            except Exception:
                order_dto.order_parts = []
        else:
            order_dto.order_parts = []
        
        return order_dto

    def _build_order_line_dto(self, order_line: OrderLine) -> OrderLineResponseDTO:
        """Build OrderLineResponseDTO from OrderLine entity."""
        dto = OrderLineResponseDTO.model_validate(order_line)
        dto.completion_percentage = order_line.get_completion_percentage()
        dto.production_percentage = order_line.get_production_percentage()
        dto.remaining_amount = order_line.get_remaining_amount()
        return dto

    def _build_order_summary_dto(self, order: Order) -> OrderSummaryDTO:
        """Build OrderSummaryDTO from Order entity."""
        dto = OrderSummaryDTO.model_validate(order)
        dto.completion_percentage = order.get_completion_percentage()
        dto.completed_amount = sum(line.completed_amount for line in order.order_lines)
        dto.owner_name = order.owner.full_name if order.owner else None
        return dto