from typing import List, Optional
from passlib.context import Crypt<PERSON>ontext
from src.domain.entities.user import User
from src.application.interfaces.user_repository_interface import UserRepositoryInterface
from src.application.dto.user_dto import UserCreateDTO, UserUpdateDTO


class UserUseCases:
    """User business logic use cases."""
    
    def __init__(self, user_repository: UserRepositoryInterface):
        self.user_repository = user_repository
        self.pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
    
    def _hash_password(self, password: str) -> str:
        """Hash a password."""
        return self.pwd_context.hash(password)
    
    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """Verify a password against its hash."""
        return self.pwd_context.verify(plain_password, hashed_password)
    
    async def create_user(self, user_data: UserCreateDTO) -> User:
        """Create a new user."""
        # Check if username or email already exists
        existing_user = await self.user_repository.get_by_username(user_data.username)
        if existing_user:
            raise ValueError("Username already registered")
        
        existing_email = await self.user_repository.get_by_email(user_data.email)
        if existing_email:
            raise ValueError("Email already registered")
        
        # Create new user
        hashed_password = self._hash_password(user_data.password)
        user = User(
            username=user_data.username,
            email=user_data.email,
            hashed_password=hashed_password,
            full_name=user_data.full_name
        )
        
        return await self.user_repository.create(user)
    
    async def authenticate_user(self, username: str, password: str) -> Optional[User]:
        """Authenticate a user by username and password."""
        user = await self.user_repository.get_by_username(username)
        if not user:
            return None
        if not self.verify_password(password, user.hashed_password):
            return None
        return user
    
    async def get_user_by_id(self, user_id: int) -> Optional[User]:
        """Get user by ID."""
        return await self.user_repository.get_by_id(user_id)
    
    async def get_user_by_username(self, username: str) -> Optional[User]:
        """Get user by username."""
        return await self.user_repository.get_by_username(username)
    
    async def update_user(self, user_id: int, user_data: UserUpdateDTO) -> Optional[User]:
        """Update an existing user."""
        user = await self.user_repository.get_by_id(user_id)
        if not user:
            return None
        
        # Update fields
        if user_data.full_name is not None:
            user.full_name = user_data.full_name
        if user_data.avatar_url is not None:
            user.avatar_url = user_data.avatar_url
        if user_data.is_active is not None:
            user.is_active = user_data.is_active
        
        return await self.user_repository.update(user)
    
    async def delete_user(self, user_id: int) -> bool:
        """Delete a user."""
        return await self.user_repository.delete(user_id)
    
    async def get_all_users(self, skip: int = 0, limit: int = 100) -> List[User]:
        """Get all users with pagination."""
        return await self.user_repository.get_all(skip=skip, limit=limit)