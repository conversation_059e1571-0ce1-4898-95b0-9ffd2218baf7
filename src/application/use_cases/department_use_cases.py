from typing import List, Optional
from datetime import datetime

from src.domain.entities.department import Department
from src.application.interfaces.department_repository_interface import DepartmentRepositoryInterface
from src.application.dto.department_dto import (
    DepartmentCreateDTO, DepartmentUpdateDTO, DepartmentResponseDTO,
    DepartmentListDTO, DepartmentSearchDTO, DepartmentOperationResultDTO
)
from src.infrastructure.logging.logger import get_logger

logger = get_logger(__name__)


class DepartmentUseCases:
    """Use cases for department management."""
    
    def __init__(self, department_repository: DepartmentRepositoryInterface):
        self.department_repository = department_repository
    
    async def create_department(
        self, 
        department_data: DepartmentCreateDTO, 
        factory_id: int
    ) -> DepartmentResponseDTO:
        """Create a new department."""
        logger.info(
            "Creating new department",
            factory_id=factory_id,
            department_name=department_data.name,
            department_code=department_data.code,
            operator_id=department_data.operator_id
        )
        
        # Check if department code already exists in this factory
        existing_department = await self.department_repository.get_by_code_and_factory(
            department_data.code, factory_id
        )
        if existing_department:
            logger.warning(
                "Department code already exists in factory",
                factory_id=factory_id,
                department_code=department_data.code,
                operator_id=department_data.operator_id
            )
            raise ValueError(f"Department with code '{department_data.code}' already exists in this factory")
        
        # Create department entity
        department = Department(
            name=department_data.name,
            code=department_data.code,
            factory_id=factory_id,
            manager_name=department_data.manager_name,
            phone=department_data.phone,
            email=department_data.email,
            location=department_data.location,
            description=department_data.description,
            is_active=department_data.is_active,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        # Save to database
        created_department = await self.department_repository.create(department)
        
        logger.info(
            "Successfully created department",
            department_id=created_department.id,
            factory_id=factory_id,
            department_name=created_department.name,
            operator_id=department_data.operator_id
        )
        
        return DepartmentResponseDTO.model_validate(created_department)
    
    async def get_department_by_id(self, department_id: int) -> Optional[DepartmentResponseDTO]:
        """Get department by ID."""
        logger.info("Getting department by ID", department_id=department_id)
        
        department = await self.department_repository.get_by_id(department_id)
        if not department:
            logger.warning("Department not found", department_id=department_id)
            return None
        
        return DepartmentResponseDTO.model_validate(department)
    
    async def get_departments_by_factory(
        self, 
        factory_id: int, 
        skip: int = 0, 
        limit: int = 100
    ) -> DepartmentListDTO:
        """Get departments by factory ID with pagination."""
        logger.info(
            "Getting departments by factory",
            factory_id=factory_id,
            skip=skip,
            limit=limit
        )
        
        departments = await self.department_repository.get_by_factory_id(
            factory_id, skip, limit
        )
        
        # Convert to DTOs
        department_dtos = [
            DepartmentResponseDTO.model_validate(dept) for dept in departments
        ]
        
        logger.info(
            "Successfully retrieved departments",
            factory_id=factory_id,
            count=len(department_dtos)
        )
        
        return DepartmentListDTO(
            departments=department_dtos,
            total=len(department_dtos)  # Note: This is the count for this page, not total count
        )
    
    async def update_department(
        self, 
        department_id: int, 
        department_data: DepartmentUpdateDTO
    ) -> Optional[DepartmentResponseDTO]:
        """Update a department."""
        logger.info(
            "Updating department",
            department_id=department_id,
            operator_id=department_data.operator_id
        )
        
        # Get existing department
        existing_department = await self.department_repository.get_by_id(department_id)
        if not existing_department:
            logger.warning(
                "Department not found for update",
                department_id=department_id,
                operator_id=department_data.operator_id
            )
            return None
        
        # Check if code is being changed and if it conflicts
        if (department_data.code and 
            department_data.code != existing_department.code):
            code_conflict = await self.department_repository.get_by_code_and_factory(
                department_data.code, existing_department.factory_id
            )
            if code_conflict and code_conflict.id != department_id:
                logger.warning(
                    "Department code conflict during update",
                    department_id=department_id,
                    new_code=department_data.code,
                    operator_id=department_data.operator_id
                )
                raise ValueError(f"Department with code '{department_data.code}' already exists in this factory")
        
        # Update fields
        if department_data.name is not None:
            existing_department.name = department_data.name
        if department_data.code is not None:
            existing_department.code = department_data.code
        if department_data.manager_name is not None:
            existing_department.manager_name = department_data.manager_name
        if department_data.phone is not None:
            existing_department.phone = department_data.phone
        if department_data.email is not None:
            existing_department.email = department_data.email
        if department_data.location is not None:
            existing_department.location = department_data.location
        if department_data.description is not None:
            existing_department.description = department_data.description
        if department_data.is_active is not None:
            existing_department.is_active = department_data.is_active
        
        existing_department.updated_at = datetime.utcnow()
        
        # Save changes
        updated_department = await self.department_repository.update(existing_department)
        
        logger.info(
            "Successfully updated department",
            department_id=department_id,
            operator_id=department_data.operator_id
        )
        
        return DepartmentResponseDTO.model_validate(updated_department)
    
    async def delete_department(
        self, 
        department_id: int, 
        operator_id: int
    ) -> DepartmentOperationResultDTO:
        """Delete a department."""
        logger.info(
            "Deleting department",
            department_id=department_id,
            operator_id=operator_id
        )
        
        # Check if department exists
        existing_department = await self.department_repository.get_by_id(department_id)
        if not existing_department:
            logger.warning(
                "Department not found for deletion",
                department_id=department_id,
                operator_id=operator_id
            )
            return DepartmentOperationResultDTO(
                success=False,
                message=f"Department with ID {department_id} not found",
                operator_id=operator_id
            )
        
        # Perform deletion
        success = await self.department_repository.delete(department_id)
        
        if success:
            logger.info(
                "Successfully deleted department",
                department_id=department_id,
                operator_id=operator_id
            )
            return DepartmentOperationResultDTO(
                success=True,
                message=f"Department '{existing_department.name}' deleted successfully",
                department_id=department_id,
                operator_id=operator_id
            )
        else:
            logger.error(
                "Failed to delete department",
                department_id=department_id,
                operator_id=operator_id
            )
            return DepartmentOperationResultDTO(
                success=False,
                message=f"Failed to delete department with ID {department_id}",
                operator_id=operator_id
            )
    
    async def search_departments(
        self, 
        factory_id: int, 
        search_criteria: DepartmentSearchDTO,
        skip: int = 0,
        limit: int = 100
    ) -> DepartmentListDTO:
        """Search departments with criteria."""
        logger.info(
            "Searching departments",
            factory_id=factory_id,
            search_term=search_criteria.search_term,
            is_active=search_criteria.is_active,
            skip=skip,
            limit=limit
        )
        
        # Get all departments for the factory first
        all_departments = await self.department_repository.get_by_factory_id(
            factory_id, 0, 1000  # Get a large number to filter
        )
        
        # Apply filters
        filtered_departments = []
        for dept in all_departments:
            # Filter by active status
            if search_criteria.is_active is not None and dept.is_active != search_criteria.is_active:
                continue
            
            # Filter by search term (name or code)
            if search_criteria.search_term:
                search_term = search_criteria.search_term.lower()
                if (search_term not in dept.name.lower() and 
                    search_term not in dept.code.lower()):
                    continue
            
            # Filter by manager name
            if search_criteria.manager_name:
                if not dept.manager_name or search_criteria.manager_name.lower() not in dept.manager_name.lower():
                    continue
            
            filtered_departments.append(dept)
        
        # Apply pagination
        paginated_departments = filtered_departments[skip:skip + limit]
        
        # Convert to DTOs
        department_dtos = [
            DepartmentResponseDTO.model_validate(dept) for dept in paginated_departments
        ]
        
        logger.info(
            "Successfully searched departments",
            factory_id=factory_id,
            total_found=len(filtered_departments),
            returned_count=len(department_dtos)
        )
        
        return DepartmentListDTO(
            departments=department_dtos,
            total=len(filtered_departments)
        )
