from typing import List, Optional, Dict, Any
from datetime import date
from dependency_injector.wiring import Provide, inject
from sqlalchemy.ext.asyncio import AsyncSession
from src.domain.entities.daily_worker_bill import DailyWorkerBill, BillStatus, PaymentMethod
from src.application.interfaces.daily_worker_bill_repository_interface import DailyWorkerBillRepositoryInterface
from src.application.interfaces.order_craft_route_instance_repository_interface import OrderCraftRouteInstanceRepositoryInterface
from src.application.services.bill_settlement_workflow_service import BillSettlementWorkflowService
from src.application.dto.bill_dto import (
    CreateBillRequestDTO, UpdateBillRequestDTO, BillResponseDTO, BillListResponseDTO,
    BillOperationResponseDTO, BillSettlementSummaryDTO, BillSearchRequestDTO,
    UserInfoDTO, FactoryInfoDTO
)
from src.infrastructure.database.database import Database


class BillUseCases:
    """Bill management use cases."""
    
    @inject
    def __init__(
        self,
        daily_worker_bill_repository: DailyWorkerBillRepositoryInterface,
        order_craft_route_instance_repository: OrderCraftRouteInstanceRepositoryInterface,
        bill_settlement_workflow_service: BillSettlementWorkflowService,
        database: Database = Provide["db"]
    ):
        self.daily_worker_bill_repository = daily_worker_bill_repository
        self.order_craft_route_instance_repository = order_craft_route_instance_repository
        self.bill_settlement_workflow_service = bill_settlement_workflow_service
        self.database = database
    
    async def create_bill(
        self,
        current_user_id: int,
        factory_id: int,
        request: CreateBillRequestDTO
    ) -> BillOperationResponseDTO:
        """Create a new bill."""
        
        async with self.database.session_factory() as session:
            # Check if bill already exists for this worker and date
            existing_bill = await self.daily_worker_bill_repository.get_by_worker_and_date(
                request.worker_user_id, factory_id, request.bill_date, session
            )
            
            if existing_bill:
                return BillOperationResponseDTO(
                    success=False,
                    message=f"Bill already exists for worker {request.worker_user_id} on {request.bill_date}"
                )
            
            # Generate bill number
            bill_no = await self._generate_bill_number(factory_id, request.bill_date, session)
            
            # Create bill entity
            bill = DailyWorkerBill(
                factory_id=factory_id,
                worker_user_id=request.worker_user_id,
                bill_date=request.bill_date,
                bill_no=bill_no,
                total_completed_quantity=request.total_completed_quantity,
                total_work_instances=request.total_work_instances,
                total_work_duration_minutes=request.total_work_duration_minutes,
                base_amount=request.base_amount,
                bonus_amount=request.bonus_amount,
                deduction_amount=request.deduction_amount,
                total_amount=request.total_amount,
                quality_score_average=request.quality_score_average,
                defect_count=request.defect_count,
                rework_count=request.rework_count,
                breakdown_by_granularity=request.breakdown_by_granularity,
                breakdown_by_craft_route=request.breakdown_by_craft_route,
                breakdown_by_order=request.breakdown_by_order,
                notes=request.notes,
                is_auto_generated=False
            )
            
            created_bill = await self.daily_worker_bill_repository.create(bill, session)
            await session.commit()
            
            bill_dto = await self._convert_to_dto(created_bill)
            
            return BillOperationResponseDTO(
                success=True,
                message="Bill created successfully",
                bill=bill_dto
            )
    
    async def get_bill(self, bill_id: int, factory_id: int) -> Optional[BillResponseDTO]:
        """Get a bill by ID."""
        
        async with self.database.session_factory() as session:
            bill = await self.daily_worker_bill_repository.get_by_id(bill_id, session)
            
            if not bill or bill.factory_id != factory_id:
                return None
            
            return await self._convert_to_dto(bill)
    
    async def get_bills(
        self,
        factory_id: int,
        search_request: BillSearchRequestDTO
    ) -> BillListResponseDTO:
        """Get bills with filtering and pagination."""
        
        async with self.database.session_factory() as session:
            bills = []
            
            if search_request.worker_user_id and search_request.start_date and search_request.end_date:
                # Date range search for specific worker
                bills = await self.daily_worker_bill_repository.get_by_worker_and_date_range(
                    search_request.worker_user_id,
                    factory_id,
                    search_request.start_date,
                    search_request.end_date,
                    session
                )
            elif search_request.status:
                # Status-based search
                bills = await self.daily_worker_bill_repository.get_by_status(
                    factory_id, search_request.status, session
                )
            elif search_request.bill_date:
                # Specific date search
                bills = await self.daily_worker_bill_repository.get_by_factory_and_date(
                    factory_id, search_request.bill_date, session
                )
            else:
                # General pagination
                bills = await self.daily_worker_bill_repository.get_all(
                    factory_id, session, search_request.skip, search_request.limit
                )
            
            # Apply additional filtering if needed
            if search_request.worker_user_id and not (search_request.start_date and search_request.end_date):
                bills = [bill for bill in bills if bill.worker_user_id == search_request.worker_user_id]
            
            # Apply pagination for filtered results
            total = len(bills)
            if not search_request.start_date or not search_request.end_date:
                bills = bills[search_request.skip:search_request.skip + search_request.limit]
            
            bill_dtos = []
            for bill in bills:
                bill_dto = await self._convert_to_dto(bill)
                bill_dtos.append(bill_dto)
            
            return BillListResponseDTO(
                bills=bill_dtos,
                total=total,
                skip=search_request.skip,
                limit=search_request.limit
            )
    
    async def update_bill(
        self,
        bill_id: int,
        factory_id: int,
        request: UpdateBillRequestDTO
    ) -> BillOperationResponseDTO:
        """Update a bill."""
        
        async with self.database.session_factory() as session:
            bill = await self.daily_worker_bill_repository.get_by_id(bill_id, session)
            
            if not bill or bill.factory_id != factory_id:
                return BillOperationResponseDTO(
                    success=False,
                    message="Bill not found"
                )
            
            if bill.status != BillStatus.DRAFT:
                return BillOperationResponseDTO(
                    success=False,
                    message=f"Cannot update bill with status {bill.status.value}"
                )
            
            # Update fields
            if request.total_completed_quantity is not None:
                bill.total_completed_quantity = request.total_completed_quantity
            if request.total_work_instances is not None:
                bill.total_work_instances = request.total_work_instances
            if request.total_work_duration_minutes is not None:
                bill.total_work_duration_minutes = request.total_work_duration_minutes
            if request.base_amount is not None:
                bill.base_amount = request.base_amount
            if request.bonus_amount is not None:
                bill.bonus_amount = request.bonus_amount
            if request.deduction_amount is not None:
                bill.deduction_amount = request.deduction_amount
            if request.total_amount is not None:
                bill.total_amount = request.total_amount
            if request.quality_score_average is not None:
                bill.quality_score_average = request.quality_score_average
            if request.defect_count is not None:
                bill.defect_count = request.defect_count
            if request.rework_count is not None:
                bill.rework_count = request.rework_count
            if request.breakdown_by_granularity is not None:
                bill.breakdown_by_granularity = request.breakdown_by_granularity
            if request.breakdown_by_craft_route is not None:
                bill.breakdown_by_craft_route = request.breakdown_by_craft_route
            if request.breakdown_by_order is not None:
                bill.breakdown_by_order = request.breakdown_by_order
            if request.notes is not None:
                bill.notes = request.notes
            
            updated_bill = await self.daily_worker_bill_repository.update(bill, session)
            await session.commit()
            
            bill_dto = await self._convert_to_dto(updated_bill)
            
            return BillOperationResponseDTO(
                success=True,
                message="Bill updated successfully",
                bill=bill_dto
            )
    
    async def submit_bill_for_review(
        self,
        bill_id: int,
        factory_id: int
    ) -> BillOperationResponseDTO:
        """Submit a bill for review."""
        
        async with self.database.session_factory() as session:
            bill = await self.daily_worker_bill_repository.get_by_id(bill_id, session)
            
            if not bill or bill.factory_id != factory_id:
                return BillOperationResponseDTO(
                    success=False,
                    message="Bill not found"
                )
            
            result = await self.bill_settlement_workflow_service.submit_bill_for_review(
                bill_id, session
            )
            
            if result["success"]:
                await session.commit()
                bill_dto = await self._convert_to_dto(result["bill"])
                return BillOperationResponseDTO(
                    success=True,
                    message=result["message"],
                    bill=bill_dto
                )
            else:
                return BillOperationResponseDTO(
                    success=False,
                    message=result["message"]
                )
    
    async def approve_bill(
        self,
        bill_id: int,
        factory_id: int,
        reviewer_user_id: int,
        review_notes: Optional[str] = None
    ) -> BillOperationResponseDTO:
        """Approve a bill."""
        
        async with self.database.session_factory() as session:
            bill = await self.daily_worker_bill_repository.get_by_id(bill_id, session)
            
            if not bill or bill.factory_id != factory_id:
                return BillOperationResponseDTO(
                    success=False,
                    message="Bill not found"
                )
            
            result = await self.bill_settlement_workflow_service.approve_bill(
                bill_id, reviewer_user_id, review_notes, session
            )
            
            if result["success"]:
                await session.commit()
                bill_dto = await self._convert_to_dto(result["bill"])
                return BillOperationResponseDTO(
                    success=True,
                    message=result["message"],
                    bill=bill_dto
                )
            else:
                return BillOperationResponseDTO(
                    success=False,
                    message=result["message"]
                )
    
    async def reject_bill(
        self,
        bill_id: int,
        factory_id: int,
        reviewer_user_id: int,
        rejection_reason: str
    ) -> BillOperationResponseDTO:
        """Reject a bill."""
        
        async with self.database.session_factory() as session:
            bill = await self.daily_worker_bill_repository.get_by_id(bill_id, session)
            
            if not bill or bill.factory_id != factory_id:
                return BillOperationResponseDTO(
                    success=False,
                    message="Bill not found"
                )
            
            result = await self.bill_settlement_workflow_service.reject_bill(
                bill_id, reviewer_user_id, rejection_reason, session
            )
            
            if result["success"]:
                await session.commit()
                bill_dto = await self._convert_to_dto(result["bill"])
                return BillOperationResponseDTO(
                    success=True,
                    message=result["message"],
                    bill=bill_dto
                )
            else:
                return BillOperationResponseDTO(
                    success=False,
                    message=result["message"]
                )
    
    async def mark_bill_as_paid(
        self,
        bill_id: int,
        factory_id: int,
        payer_user_id: int,
        payment_method: PaymentMethod,
        payment_reference: Optional[str] = None,
        payment_notes: Optional[str] = None
    ) -> BillOperationResponseDTO:
        """Mark a bill as paid."""
        
        async with self.database.session_factory() as session:
            bill = await self.daily_worker_bill_repository.get_by_id(bill_id, session)
            
            if not bill or bill.factory_id != factory_id:
                return BillOperationResponseDTO(
                    success=False,
                    message="Bill not found"
                )
            
            result = await self.bill_settlement_workflow_service.mark_bill_as_paid(
                bill_id, payer_user_id, payment_method, payment_reference, payment_notes, session
            )
            
            if result["success"]:
                await session.commit()
                bill_dto = await self._convert_to_dto(result["bill"])
                return BillOperationResponseDTO(
                    success=True,
                    message=result["message"],
                    bill=bill_dto
                )
            else:
                return BillOperationResponseDTO(
                    success=False,
                    message=result["message"]
                )
    
    async def cancel_bill(
        self,
        bill_id: int,
        factory_id: int,
        cancellation_reason: str
    ) -> BillOperationResponseDTO:
        """Cancel a bill."""
        
        async with self.database.session_factory() as session:
            bill = await self.daily_worker_bill_repository.get_by_id(bill_id, session)
            
            if not bill or bill.factory_id != factory_id:
                return BillOperationResponseDTO(
                    success=False,
                    message="Bill not found"
                )
            
            result = await self.bill_settlement_workflow_service.cancel_bill(
                bill_id, cancellation_reason, session
            )
            
            if result["success"]:
                await session.commit()
                bill_dto = await self._convert_to_dto(result["bill"])
                return BillOperationResponseDTO(
                    success=True,
                    message=result["message"],
                    bill=bill_dto
                )
            else:
                return BillOperationResponseDTO(
                    success=False,
                    message=result["message"]
                )
    
    async def get_settlement_summary(self, factory_id: int) -> BillSettlementSummaryDTO:
        """Get settlement summary for a factory."""
        
        async with self.database.session_factory() as session:
            summary = await self.bill_settlement_workflow_service.get_bill_settlement_summary(
                factory_id, session
            )
            
            return BillSettlementSummaryDTO(
                bills=BillSettlementSummaryDTO.BillSummaryDTO(**summary["bills"]),
                instances=BillSettlementSummaryDTO.InstanceSummaryDTO(**summary["instances"])
            )
    
    async def dispute_instance(
        self,
        instance_id: int,
        factory_id: int,
        dispute_reason: str
    ) -> Dict[str, Any]:
        """Dispute a bill instance."""
        
        async with self.database.session_factory() as session:
            result = await self.bill_settlement_workflow_service.dispute_bill_instance(
                instance_id, dispute_reason, session
            )
            
            if result["success"]:
                await session.commit()
            
            return result
    
    async def resolve_instance_dispute(
        self,
        instance_id: int,
        factory_id: int,
        resolution_notes: str,
        include_in_settlement: bool
    ) -> Dict[str, Any]:
        """Resolve an instance dispute."""
        
        async with self.database.session_factory() as session:
            result = await self.bill_settlement_workflow_service.resolve_instance_dispute(
                instance_id, resolution_notes, include_in_settlement, session
            )
            
            if result["success"]:
                await session.commit()
            
            return result
    
    async def _generate_bill_number(
        self,
        factory_id: int,
        bill_date: date,
        session: AsyncSession
    ) -> str:
        """Generate a unique bill number."""
        
        # Get existing bills for the same date
        existing_bills = await self.daily_worker_bill_repository.get_by_factory_and_date(
            factory_id, bill_date, session
        )
        
        # Generate bill number: BILL-{factory_id}-{YYYYMMDD}-{sequence}
        sequence = len(existing_bills) + 1
        date_str = bill_date.strftime("%Y%m%d")
        
        return f"BILL-{factory_id}-{date_str}-{sequence:04d}"
    
    async def _convert_to_dto(self, bill: DailyWorkerBill) -> BillResponseDTO:
        """Convert bill entity to DTO."""
        
        bill_dto = BillResponseDTO.from_orm(bill)
        
        # Convert related entities
        if bill.worker_user:
            bill_dto.worker_user = UserInfoDTO.from_orm(bill.worker_user)
        if bill.factory:
            bill_dto.factory = FactoryInfoDTO.from_orm(bill.factory)
        if bill.reviewed_by_user:
            bill_dto.reviewed_by_user = UserInfoDTO.from_orm(bill.reviewed_by_user)
        if bill.paid_by_user:
            bill_dto.paid_by_user = UserInfoDTO.from_orm(bill.paid_by_user)
        
        return bill_dto