from typing import List, Optional
from datetime import datetime
from src.domain.entities.user import User
from src.domain.entities.factory import Factory
from src.domain.entities.user_factory import UserFactory, UserFactoryStatus, UserFactoryRole
from src.application.interfaces.user_repository_interface import UserRepositoryInterface
from src.application.interfaces.factory_repository_interface import FactoryRepositoryInterface
from src.application.interfaces.user_factory_repository_interface import UserFactoryRepositoryInterface
from src.application.dto.factory_dto import (
    FactoryJoinRequestDTO,
    FactoryJoinApprovalDTO,
    UserFactoryResponseDTO,
    FactoryDetailDTO,
    MyFactoriesDTO
)


class FactoryUseCases:
    """Use cases for factory and user-factory relationship management."""
    
    def __init__(
        self,
        user_repository: UserRepositoryInterface,
        factory_repository: FactoryRepositoryInterface,
        user_factory_repository: UserFactoryRepositoryInterface
    ):
        self.user_repository = user_repository
        self.factory_repository = factory_repository
        self.user_factory_repository = user_factory_repository
    
    async def request_to_join_factory(self, user_id: int, request_data: FactoryJoinRequestDTO) -> UserFactory:
        """User requests to join a factory."""
        # Check if user exists
        user = await self.user_repository.get_by_id(user_id)
        if not user:
            raise ValueError("User not found")
        
        # Check if factory exists and is active
        factory = await self.factory_repository.get_by_id(request_data.factory_id)
        if not factory:
            raise ValueError("Factory not found")
        if not factory.is_active:
            raise ValueError("Factory is not active")
        
        # Check if user already has a relationship with this factory
        existing = await self.user_factory_repository.get_by_user_and_factory(
            user_id, request_data.factory_id
        )
        if existing:
            if existing.status == UserFactoryStatus.PENDING:
                raise ValueError("You already have a pending request for this factory")
            elif existing.status == UserFactoryStatus.APPROVED:
                raise ValueError("You are already working in this factory")
            elif existing.status == UserFactoryStatus.REJECTED:
                # Allow re-application after rejection
                pass
        
        # Create new join request
        user_factory = UserFactory(
            user_id=user_id,
            factory_id=request_data.factory_id,
            department_id=request_data.department_id,
            status=UserFactoryStatus.PENDING,
            role=UserFactoryRole.WORKER
        )
        
        return await self.user_factory_repository.create(user_factory)
    
    async def approve_or_reject_request(
        self, 
        manager_id: int, 
        approval_data: FactoryJoinApprovalDTO
    ) -> UserFactory:
        """Manager approves or rejects a factory join request."""
        # Get the request
        user_factory = await self.user_factory_repository.get_by_id(approval_data.user_factory_id)
        if not user_factory:
            raise ValueError("Request not found")
        
        if user_factory.status != UserFactoryStatus.PENDING:
            raise ValueError("Request is not pending")
        
        # Check if manager has permission to approve for this factory
        manager_permission = await self.user_factory_repository.get_by_user_and_factory(
            manager_id, user_factory.factory_id
        )
        if not manager_permission or not manager_permission.can_approve_requests():
            raise ValueError("You don't have permission to approve requests for this factory")
        
        # Process approval/rejection
        if approval_data.action.lower() == "approve":
            user_factory.approve(manager_id, approval_data.start_date)
            if approval_data.employee_id:
                user_factory.employee_id = approval_data.employee_id
            if approval_data.position:
                user_factory.position = approval_data.position
            if approval_data.department_id:
                user_factory.department_id = approval_data.department_id
            if approval_data.role:
                user_factory.role = UserFactoryRole(approval_data.role.value)
        elif approval_data.action.lower() == "reject":
            if not approval_data.reason:
                raise ValueError("Rejection reason is required")
            user_factory.reject(manager_id, approval_data.reason)
        else:
            raise ValueError("Action must be 'approve' or 'reject'")
        
        return await self.user_factory_repository.update(user_factory)
    
    async def get_factory_pending_requests(self, manager_id: int, factory_id: int) -> List[UserFactory]:
        """Get pending join requests for a factory."""
        # Check manager permission
        manager_permission = await self.user_factory_repository.get_by_user_and_factory(
            manager_id, factory_id
        )
        if not manager_permission or not manager_permission.can_approve_requests():
            raise ValueError("You don't have permission to view requests for this factory")
        
        return await self.user_factory_repository.get_pending_requests_by_factory(factory_id)
    
    async def get_factory_members(self, user_id: int, factory_id: int) -> List[UserFactory]:
        """Get all members of a factory."""
        # Check if user has access to this factory
        user_permission = await self.user_factory_repository.get_by_user_and_factory(
            user_id, factory_id
        )
        if not user_permission or not user_permission.is_active():
            raise ValueError("You don't have access to view this factory's members")
        
        return await self.user_factory_repository.get_active_members_by_factory(factory_id)
    
    async def get_user_factories(self, user_id: int) -> MyFactoriesDTO:
        """Get all factories for a user."""
        user_factories = await self.user_factory_repository.get_by_user_id(user_id)
        
        active_factories = []
        pending_requests = []
        managed_factories = []
        
        for uf in user_factories:
            uf_dto = UserFactoryResponseDTO.model_validate(uf)
            
            if uf.status == UserFactoryStatus.APPROVED:
                active_factories.append(uf_dto)
                if uf.can_approve_requests():
                    managed_factories.append(uf_dto)
            elif uf.status == UserFactoryStatus.PENDING:
                pending_requests.append(uf_dto)
        
        return MyFactoriesDTO(
            active_factories=active_factories,
            pending_requests=pending_requests,
            managed_factories=managed_factories
        )
    
    async def update_user_role_in_factory(
        self, 
        manager_id: int, 
        user_id: int, 
        factory_id: int, 
        new_role: UserFactoryRole
    ) -> UserFactory:
        """Manager updates user's role in factory."""
        # Check manager permission
        manager_permission = await self.user_factory_repository.get_by_user_and_factory(
            manager_id, factory_id
        )
        if not manager_permission or not manager_permission.can_approve_requests():
            raise ValueError("You don't have permission to update roles in this factory")
        
        # Get user's factory relationship
        user_factory = await self.user_factory_repository.get_by_user_and_factory(
            user_id, factory_id
        )
        if not user_factory or not user_factory.is_active():
            raise ValueError("User is not an active member of this factory")
        
        # Update role
        user_factory.role = new_role
        user_factory.updated_at = datetime.utcnow()
        
        return await self.user_factory_repository.update(user_factory)
    
    async def suspend_user_from_factory(
        self, 
        manager_id: int, 
        user_id: int, 
        factory_id: int, 
        reason: Optional[str] = None
    ) -> UserFactory:
        """Manager suspends user from factory."""
        # Check manager permission
        manager_permission = await self.user_factory_repository.get_by_user_and_factory(
            manager_id, factory_id
        )
        if not manager_permission or not manager_permission.can_approve_requests():
            raise ValueError("You don't have permission to suspend users in this factory")
        
        # Get user's factory relationship
        user_factory = await self.user_factory_repository.get_by_user_and_factory(
            user_id, factory_id
        )
        if not user_factory or not user_factory.is_active():
            raise ValueError("User is not an active member of this factory")
        
        # Suspend user
        user_factory.suspend(reason)
        
        return await self.user_factory_repository.update(user_factory)
    
    async def resign_from_factory(self, user_id: int, factory_id: int) -> UserFactory:
        """User resigns from factory."""
        # Get user's factory relationship
        user_factory = await self.user_factory_repository.get_by_user_and_factory(
            user_id, factory_id
        )
        if not user_factory or not user_factory.is_active():
            raise ValueError("You are not an active member of this factory")
        
        # Resign
        user_factory.resign()
        
        return await self.user_factory_repository.update(user_factory)