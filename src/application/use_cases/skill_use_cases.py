from typing import List, Optional
from datetime import datetime
from src.application.interfaces.skill_repository_interface import SkillRepositoryInterface
from src.application.interfaces.user_factory_skill_repository_interface import UserFactorySkillRepositoryInterface
from src.application.interfaces.user_factory_repository_interface import UserFactoryRepositoryInterface
from src.application.dto.skill_dto import (
    SkillCreateDTO, SkillUpdateDTO, SkillResponseDTO, SkillListDTO,
    UserFactorySkillDTO, UserSkillsDTO, AssignSkillsDTO, UpdateSkillProficiencyDTO,
    CertifySkillDTO, RemoveSkillDTO, SkillSearchDTO, SkillOperationResultDTO
)
from src.domain.entities.skill import Skill
from src.domain.entities.user_factory_skill import UserFactorySkill


class SkillUseCases:
    """Use cases for skill management operations."""

    def __init__(
        self,
        skill_repository: SkillRepositoryInterface,
        user_factory_skill_repository: UserFactorySkillRepositoryInterface,
        user_factory_repository: UserFactoryRepositoryInterface
    ):
        self.skill_repository = skill_repository
        self.user_factory_skill_repository = user_factory_skill_repository
        self.user_factory_repository = user_factory_repository

    async def create_skill(self, skill_data: SkillCreateDTO) -> SkillResponseDTO:
        """Create a new skill."""
        skill = Skill(
            code=skill_data.code,
            name=skill_data.name,
            description=skill_data.description,
            category=skill_data.category,
            is_active=skill_data.is_active
        )
        
        created_skill = await self.skill_repository.create(skill)
        return SkillResponseDTO.model_validate(created_skill)

    async def get_skill_by_id(self, skill_id: int) -> Optional[SkillResponseDTO]:
        """Get skill by ID."""
        skill = await self.skill_repository.get_by_id(skill_id)
        if skill:
            return SkillResponseDTO.model_validate(skill)
        return None

    async def get_all_skills(self, search: Optional[SkillSearchDTO] = None) -> SkillListDTO:
        """Get all skills with optional search filters."""
        if search and search.search_term:
            skills = await self.skill_repository.search_skills(search.search_term)
        else:
            skills = await self.skill_repository.get_all()

        # Apply additional filters
        if search:
            if search.category:
                skills = [s for s in skills if s.category == search.category]
            if search.is_active is not None:
                skills = [s for s in skills if s.is_active == search.is_active]

        skill_dtos = [SkillResponseDTO.model_validate(skill) for skill in skills]
        return SkillListDTO(skills=skill_dtos, total=len(skill_dtos))

    async def get_active_skills(self) -> SkillListDTO:
        """Get all active skills."""
        skills = await self.skill_repository.get_active_skills()
        skill_dtos = [SkillResponseDTO.model_validate(skill) for skill in skills]
        return SkillListDTO(skills=skill_dtos, total=len(skill_dtos))

    async def update_skill(self, skill_id: int, skill_data: SkillUpdateDTO) -> Optional[SkillResponseDTO]:
        """Update skill."""
        skill = await self.skill_repository.get_by_id(skill_id)
        if not skill:
            return None

        # Update fields if provided
        if skill_data.name is not None:
            skill.name = skill_data.name
        if skill_data.description is not None:
            skill.description = skill_data.description
        if skill_data.category is not None:
            skill.category = skill_data.category
        if skill_data.is_active is not None:
            skill.is_active = skill_data.is_active

        updated_skill = await self.skill_repository.update(skill)
        return SkillResponseDTO.model_validate(updated_skill)

    async def delete_skill(self, skill_id: int) -> bool:
        """Delete skill."""
        return await self.skill_repository.delete(skill_id)

    async def get_user_skills(self, user_id: int, factory_id: int, manager_user_id: int) -> Optional[UserSkillsDTO]:
        """Get user's skills in a factory."""
        # Verify manager has access to this factory
        manager_relation = await self.user_factory_repository.get_by_user_and_factory(
            manager_user_id, factory_id
        )
        if not manager_relation or not manager_relation.can_approve_requests():
            raise ValueError("Permission denied: insufficient privileges to view user skills")

        # Get user-factory relationship
        user_factory = await self.user_factory_repository.get_by_user_and_factory(user_id, factory_id)
        if not user_factory:
            return None

        # Get user's skills
        user_skills = await self.user_factory_skill_repository.get_by_user_factory(user_factory.id)
        
        skill_dtos = []
        for user_skill in user_skills:
            skill_dto = UserFactorySkillDTO(
                id=user_skill.id,
                skill=SkillResponseDTO.model_validate(user_skill.skill),
                proficiency_level=user_skill.proficiency_level,
                certified=user_skill.certified,
                certification_date=user_skill.certification_date.isoformat() if user_skill.certification_date else None,
                certification_expires=user_skill.certification_expires.isoformat() if user_skill.certification_expires else None,
                notes=user_skill.notes,
                assigned_by=user_skill.assigned_by,
                assigned_at=user_skill.assigned_at.isoformat(),
                updated_at=user_skill.updated_at.isoformat()
            )
            skill_dtos.append(skill_dto)

        certified_count = sum(1 for skill in user_skills if skill.is_certified())

        return UserSkillsDTO(
            user_factory_id=user_factory.id,
            skills=skill_dtos,
            total_skills=len(skill_dtos),
            certified_skills=certified_count
        )

    async def assign_skills_to_user(self, factory_id: int, assign_data: AssignSkillsDTO, manager_user_id: int) -> List[SkillOperationResultDTO]:
        """Assign multiple skills to user in factory."""
        # Verify manager has access to this factory
        manager_relation = await self.user_factory_repository.get_by_user_and_factory(
            manager_user_id, factory_id
        )
        if not manager_relation or not manager_relation.can_approve_requests():
            raise ValueError("Permission denied: insufficient privileges to assign skills")

        # Get user-factory relationship
        user_factory = await self.user_factory_repository.get_by_user_and_factory(
            assign_data.user_id, factory_id
        )
        if not user_factory:
            raise ValueError("User not found in factory")

        results = []
        for skill_data in assign_data.skills:
            try:
                # Check if skill exists
                skill = await self.skill_repository.get_by_id(skill_data.skill_id)
                if not skill:
                    results.append(SkillOperationResultDTO(
                        success=False,
                        message="Skill not found",
                        skill_id=skill_data.skill_id
                    ))
                    continue

                # Check if user already has this skill
                existing_skill = await self.user_factory_skill_repository.get_by_user_factory_and_skill(
                    user_factory.id, skill_data.skill_id
                )
                if existing_skill:
                    results.append(SkillOperationResultDTO(
                        success=False,
                        message="User already has this skill",
                        skill_id=skill_data.skill_id,
                        user_factory_skill_id=existing_skill.id
                    ))
                    continue

                # Create user factory skill
                user_factory_skill = UserFactorySkill(
                    user_factory_id=user_factory.id,
                    skill_id=skill_data.skill_id,
                    proficiency_level=skill_data.proficiency_level,
                    notes=skill_data.notes,
                    assigned_by=manager_user_id
                )
                
                created_skill = await self.user_factory_skill_repository.create(user_factory_skill)
                
                results.append(SkillOperationResultDTO(
                    success=True,
                    message="Skill assigned successfully",
                    skill_id=skill_data.skill_id,
                    user_factory_skill_id=created_skill.id
                ))

            except Exception as e:
                results.append(SkillOperationResultDTO(
                    success=False,
                    message=f"Failed to assign skill: {str(e)}",
                    skill_id=skill_data.skill_id
                ))

        return results

    async def update_skill_proficiency(self, update_data: UpdateSkillProficiencyDTO, manager_user_id: int) -> SkillOperationResultDTO:
        """Update user's skill proficiency."""
        try:
            user_factory_skill = await self.user_factory_skill_repository.get_by_id(
                update_data.user_factory_skill_id
            )
            if not user_factory_skill:
                return SkillOperationResultDTO(
                    success=False,
                    message="User factory skill not found",
                    user_factory_skill_id=update_data.user_factory_skill_id
                )

            # Update proficiency
            user_factory_skill.update_proficiency(update_data.proficiency_level, manager_user_id)
            if update_data.notes is not None:
                user_factory_skill.notes = update_data.notes

            await self.user_factory_skill_repository.update(user_factory_skill)

            return SkillOperationResultDTO(
                success=True,
                message="Skill proficiency updated successfully",
                user_factory_skill_id=update_data.user_factory_skill_id,
                details={"proficiency_level": update_data.proficiency_level}
            )

        except Exception as e:
            return SkillOperationResultDTO(
                success=False,
                message=f"Failed to update skill proficiency: {str(e)}",
                user_factory_skill_id=update_data.user_factory_skill_id
            )

    async def certify_user_skill(self, certify_data: CertifySkillDTO, manager_user_id: int) -> SkillOperationResultDTO:
        """Certify user in skill."""
        try:
            user_factory_skill = await self.user_factory_skill_repository.get_by_id(
                certify_data.user_factory_skill_id
            )
            if not user_factory_skill:
                return SkillOperationResultDTO(
                    success=False,
                    message="User factory skill not found",
                    user_factory_skill_id=certify_data.user_factory_skill_id
                )

            # Certify skill
            expires_at = datetime.fromisoformat(certify_data.certification_expires) if certify_data.certification_expires else None
            user_factory_skill.certify(manager_user_id, expires_at)
            if certify_data.notes is not None:
                user_factory_skill.notes = certify_data.notes

            await self.user_factory_skill_repository.update(user_factory_skill)

            return SkillOperationResultDTO(
                success=True,
                message="User certified in skill successfully",
                user_factory_skill_id=certify_data.user_factory_skill_id,
                details={"certification_expires": certify_data.certification_expires}
            )

        except Exception as e:
            return SkillOperationResultDTO(
                success=False,
                message=f"Failed to certify user skill: {str(e)}",
                user_factory_skill_id=certify_data.user_factory_skill_id
            )

    async def remove_user_skill(self, remove_data: RemoveSkillDTO, manager_user_id: int) -> SkillOperationResultDTO:
        """Remove skill from user."""
        try:
            user_factory_skill = await self.user_factory_skill_repository.get_by_id(
                remove_data.user_factory_skill_id
            )
            if not user_factory_skill:
                return SkillOperationResultDTO(
                    success=False,
                    message="User factory skill not found",
                    user_factory_skill_id=remove_data.user_factory_skill_id
                )

            # Remove skill
            deleted = await self.user_factory_skill_repository.delete(remove_data.user_factory_skill_id)
            
            if deleted:
                return SkillOperationResultDTO(
                    success=True,
                    message="Skill removed from user successfully",
                    user_factory_skill_id=remove_data.user_factory_skill_id,
                    details={"reason": remove_data.reason}
                )
            else:
                return SkillOperationResultDTO(
                    success=False,
                    message="Failed to remove skill",
                    user_factory_skill_id=remove_data.user_factory_skill_id
                )

        except Exception as e:
            return SkillOperationResultDTO(
                success=False,
                message=f"Failed to remove user skill: {str(e)}",
                user_factory_skill_id=remove_data.user_factory_skill_id
            )