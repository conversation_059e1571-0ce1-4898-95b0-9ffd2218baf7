from typing import List, Optional
from datetime import datetime
from passlib.context import Crypt<PERSON>ontext
from src.application.interfaces.user_repository_interface import UserRepositoryInterface
from src.application.interfaces.role_repository_interface import RoleRepositoryInterface
from src.application.interfaces.factory_repository_interface import FactoryRepositoryInterface
from src.application.interfaces.user_factory_repository_interface import UserFactoryRepositoryInterface
from src.application.interfaces.skill_repository_interface import SkillRepositoryInterface
from src.application.interfaces.user_factory_skill_repository_interface import UserFactorySkillRepositoryInterface
from src.application.dto.user_management_dto import (
    UserSummaryDTO, FactoryUserDTO, FactoryUserListDTO, AddUserToFactoryDTO,
    AddUsersToFactoryDTO, BindUserRoleDTO, SuspendUserDTO, RemoveUserFromFactoryDTO,
    UserSearchDTO, AvailableUsersDTO, UserOperationResultDTO, CreateUserWithFactoryDTO,
    AddUserWithFactoryResponseDTO
)
from src.application.dto.role_dto import RoleSummaryDTO
from src.application.dto.skill_dto import UserFactorySkillDTO, SkillResponseDTO
from src.domain.entities.user import User
from src.domain.entities.user_factory import UserFactory, UserFactoryStatus, UserFactoryRole
from src.domain.entities.user_factory_skill import UserFactorySkill


class UserManagementUseCases:
    """Use cases for user management operations in factory context."""

    def __init__(
        self,
        user_repository: UserRepositoryInterface,
        role_repository: RoleRepositoryInterface,
        factory_repository: FactoryRepositoryInterface,
        user_factory_repository: UserFactoryRepositoryInterface,
        skill_repository: SkillRepositoryInterface,
        user_factory_skill_repository: UserFactorySkillRepositoryInterface
    ):
        self.user_repository = user_repository
        self.role_repository = role_repository
        self.factory_repository = factory_repository
        self.user_factory_repository = user_factory_repository
        self.skill_repository = skill_repository
        self.user_factory_skill_repository = user_factory_skill_repository
        self.pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

    async def get_factory_users(self, factory_id: int, manager_user_id: int) -> FactoryUserListDTO:
        """Get all users in a factory."""
        # Verify manager has access to this factory
        manager_relation = await self.user_factory_repository.get_by_user_and_factory(
            manager_user_id, factory_id
        )
        if not manager_relation or not manager_relation.can_approve_requests():
            raise ValueError("Permission denied: insufficient privileges to view factory users")

        # Get factory info
        factory = await self.factory_repository.get_by_id(factory_id)
        if not factory:
            raise ValueError("Factory not found")

        # Get all user-factory relationships for this factory
        user_factory_relations = await self.user_factory_repository.get_by_factory_id(factory_id)
        
        factory_users = []
        for relation in user_factory_relations:
            user = await self.user_repository.get_by_id(relation.user_id)
            if user:
                user_summary = UserSummaryDTO(
                    id=user.id,
                    username=user.username,
                    full_name=user.full_name,
                    email=user.email,
                    phone=user.phone,
                    is_active=user.is_active,
                    role=RoleSummaryDTO(
                        id=user.role.id,
                        name=user.role.name,
                        description=user.role.description,
                        is_active=user.role.is_active,
                        permission_count=len(user.role.permissions)
                    ) if user.role else None
                )
                
                # Get user's skills in this factory
                user_skills = []
                certified_count = 0
                for skill_relation in relation.skills:
                    skill_dto = UserFactorySkillDTO(
                        id=skill_relation.id,
                        skill=SkillResponseDTO.model_validate(skill_relation.skill),
                        proficiency_level=skill_relation.proficiency_level,
                        certified=skill_relation.certified,
                        certification_date=skill_relation.certification_date.isoformat() if skill_relation.certification_date else None,
                        certification_expires=skill_relation.certification_expires.isoformat() if skill_relation.certification_expires else None,
                        notes=skill_relation.notes,
                        assigned_by=skill_relation.assigned_by,
                        assigned_at=skill_relation.assigned_at.isoformat(),
                        updated_at=skill_relation.updated_at.isoformat()
                    )
                    user_skills.append(skill_dto)
                    if skill_relation.is_certified():
                        certified_count += 1
                
                factory_user = FactoryUserDTO(
                    user=user_summary,
                    factory_role=relation.role.value,
                    factory_status=relation.status.value,
                    joined_at=relation.start_date.isoformat() if relation.start_date else None,
                    skills=user_skills,
                    skills_count=len(user_skills),
                    certified_skills_count=certified_count
                )
                factory_users.append(factory_user)

        return FactoryUserListDTO(
            users=factory_users,
            total=len(factory_users),
            factory_id=factory_id,
            factory_name=factory.name
        )

    async def get_available_users(self, factory_id: int, manager_user_id: int, search: Optional[UserSearchDTO] = None) -> AvailableUsersDTO:
        """Get users not in the factory (available to add)."""
        # Verify manager has access to this factory
        manager_relation = await self.user_factory_repository.get_by_user_and_factory(
            manager_user_id, factory_id
        )
        if not manager_relation or not manager_relation.can_approve_requests():
            raise ValueError("Permission denied: insufficient privileges to view available users")

        # Get all users
        all_users = await self.user_repository.get_all()
        
        # Get users already in this factory
        factory_user_ids = set()
        factory_relations = await self.user_factory_repository.get_by_factory_id(factory_id)
        for relation in factory_relations:
            factory_user_ids.add(relation.user_id)

        # Filter available users
        available_users = []
        for user in all_users:
            if user.id not in factory_user_ids:
                # Apply search filters if provided
                if search:
                    if search.search_term:
                        search_term = search.search_term.lower()
                        if not (search_term in user.username.lower() or 
                               search_term in user.email.lower() or
                               (user.full_name and search_term in user.full_name.lower())):
                            continue
                    
                    if search.role_id is not None and user.role_id != search.role_id:
                        continue
                    
                    if search.is_active is not None and user.is_active != search.is_active:
                        continue

                user_summary = UserSummaryDTO(
                    id=user.id,
                    username=user.username,
                    full_name=user.full_name,
                    email=user.email,
                    phone=user.phone,
                    is_active=user.is_active,
                    role=RoleSummaryDTO(
                        id=user.role.id,
                        name=user.role.name,
                        description=user.role.description,
                        is_active=user.role.is_active,
                        permission_count=len(user.role.permissions)
                    ) if user.role else None
                )
                available_users.append(user_summary)

        return AvailableUsersDTO(
            users=available_users,
            total=len(available_users)
        )

    async def add_users_to_factory(self, factory_id: int, manager_user_id: int, add_data: AddUsersToFactoryDTO) -> List[UserOperationResultDTO]:
        """Add multiple users to factory."""
        # Verify manager has access to this factory
        manager_relation = await self.user_factory_repository.get_by_user_and_factory(
            manager_user_id, factory_id
        )
        if not manager_relation or not manager_relation.can_approve_requests():
            raise ValueError("Permission denied: insufficient privileges to add users to factory")

        results = []
        for user_data in add_data.users:
            try:
                # Check if user exists
                user = await self.user_repository.get_by_id(user_data.user_id)
                if not user:
                    results.append(UserOperationResultDTO(
                        success=False,
                        message="User not found",
                        user_id=user_data.user_id
                    ))
                    continue

                # Check if user is already in factory
                existing_relation = await self.user_factory_repository.get_by_user_and_factory(
                    user_data.user_id, factory_id
                )
                if existing_relation:
                    results.append(UserOperationResultDTO(
                        success=False,
                        message="User already in factory",
                        user_id=user_data.user_id
                    ))
                    continue

                # Create user-factory relationship
                user_factory = UserFactory(
                    user_id=user_data.user_id,
                    factory_id=factory_id,
                    role=UserFactoryRole(user_data.factory_role),
                    status=UserFactoryStatus.APPROVED,  # Direct add by manager
                    start_date=datetime.fromisoformat(user_data.start_date) if user_data.start_date else datetime.utcnow(),
                    approved_by=manager_user_id,
                    approved_at=datetime.utcnow()
                )
                
                await self.user_factory_repository.create(user_factory)
                
                results.append(UserOperationResultDTO(
                    success=True,
                    message="User added to factory successfully",
                    user_id=user_data.user_id
                ))

            except Exception as e:
                results.append(UserOperationResultDTO(
                    success=False,
                    message=f"Failed to add user: {str(e)}",
                    user_id=user_data.user_id
                ))

        return results

    async def bind_user_role(self, bind_data: BindUserRoleDTO, manager_user_id: int) -> UserOperationResultDTO:
        """Bind system role to user."""
        try:
            # Check if user exists
            user = await self.user_repository.get_by_id(bind_data.user_id)
            if not user:
                return UserOperationResultDTO(
                    success=False,
                    message="User not found",
                    user_id=bind_data.user_id
                )

            # Check if role exists
            role = await self.role_repository.get_by_id(bind_data.role_id)
            if not role:
                return UserOperationResultDTO(
                    success=False,
                    message="Role not found",
                    user_id=bind_data.user_id
                )

            # Assign role to user
            user.assign_role(role)
            await self.user_repository.update(user)

            return UserOperationResultDTO(
                success=True,
                message=f"Role '{role.name}' assigned to user successfully",
                user_id=bind_data.user_id,
                details={"role_name": role.name, "role_id": role.id}
            )

        except Exception as e:
            return UserOperationResultDTO(
                success=False,
                message=f"Failed to bind role: {str(e)}",
                user_id=bind_data.user_id
            )

    async def suspend_user_in_factory(self, factory_id: int, suspend_data: SuspendUserDTO, manager_user_id: int) -> UserOperationResultDTO:
        """Suspend user in factory."""
        try:
            # Verify manager has access to this factory
            manager_relation = await self.user_factory_repository.get_by_user_and_factory(
                manager_user_id, factory_id
            )
            if not manager_relation or not manager_relation.can_approve_requests():
                return UserOperationResultDTO(
                    success=False,
                    message="Permission denied: insufficient privileges to suspend users",
                    user_id=suspend_data.user_id
                )

            # Get user-factory relationship
            user_factory = await self.user_factory_repository.get_by_user_and_factory(
                suspend_data.user_id, factory_id
            )
            if not user_factory:
                return UserOperationResultDTO(
                    success=False,
                    message="User not found in factory",
                    user_id=suspend_data.user_id
                )

            # Suspend user
            user_factory.suspend(
                manager_user_id, 
                suspend_data.reason or "Suspended by manager"
            )
            await self.user_factory_repository.update(user_factory)

            return UserOperationResultDTO(
                success=True,
                message="User suspended from factory successfully",
                user_id=suspend_data.user_id,
                details={"reason": suspend_data.reason}
            )

        except Exception as e:
            return UserOperationResultDTO(
                success=False,
                message=f"Failed to suspend user: {str(e)}",
                user_id=suspend_data.user_id
            )

    async def remove_user_from_factory(self, factory_id: int, remove_data: RemoveUserFromFactoryDTO, manager_user_id: int) -> UserOperationResultDTO:
        """Remove user from factory."""
        try:
            # Verify manager has access to this factory
            manager_relation = await self.user_factory_repository.get_by_user_and_factory(
                manager_user_id, factory_id
            )
            if not manager_relation or not manager_relation.can_approve_requests():
                return UserOperationResultDTO(
                    success=False,
                    message="Permission denied: insufficient privileges to remove users",
                    user_id=remove_data.user_id
                )

            # Get user-factory relationship
            user_factory = await self.user_factory_repository.get_by_user_and_factory(
                remove_data.user_id, factory_id
            )
            if not user_factory:
                return UserOperationResultDTO(
                    success=False,
                    message="User not found in factory",
                    user_id=remove_data.user_id
                )

            # Remove user from factory (soft delete or status change)
            await self.user_factory_repository.delete(user_factory.id)

            return UserOperationResultDTO(
                success=True,
                message="User removed from factory successfully",
                user_id=remove_data.user_id,
                details={"reason": remove_data.reason}
            )

        except Exception as e:
            return UserOperationResultDTO(
                success=False,
                message=f"Failed to remove user: {str(e)}",
                user_id=remove_data.user_id
            )

    async def create_or_add_user_to_factory(
        self, 
        factory_id: int, 
        manager_user_id: int, 
        user_data: CreateUserWithFactoryDTO
    ) -> AddUserWithFactoryResponseDTO:
        """Create new user or add existing user to factory with skills."""
        try:
            # Verify manager has access to this factory
            manager_relation = await self.user_factory_repository.get_by_user_and_factory(
                manager_user_id, factory_id
            )
            if not manager_relation or not manager_relation.can_approve_requests():
                return AddUserWithFactoryResponseDTO(
                    success=False,
                    message="Permission denied: insufficient privileges to add users to factory",
                    user_created=False,
                    factory_relationship_created=False
                )

            user_created = False
            user = None
            
            # Step 1: Check if user exists by username or email
            existing_user_by_username = await self.user_repository.get_by_username(user_data.username)
            existing_user_by_email = await self.user_repository.get_by_email(user_data.email)
            
            if existing_user_by_username or existing_user_by_email:
                # User exists, use existing user
                user = existing_user_by_username or existing_user_by_email
                user_created = False
            else:
                # Create new user
                hashed_password = self.pwd_context.hash(user_data.password)
                user = User(
                    username=user_data.username,
                    email=user_data.email,
                    hashed_password=hashed_password,
                    full_name=user_data.full_name,
                    phone=user_data.phone,
                    is_active=user_data.is_active
                )
                
                user = await self.user_repository.create(user)
                user_created = True

            # Step 2: Assign system role if provided
            if user_data.role_id and (user_created or not user.role_id):
                role = await self.role_repository.get_by_id(user_data.role_id)
                if role:
                    user.assign_role(role)
                    await self.user_repository.update(user)

            # Step 3: Check if user is already in factory
            existing_factory_relation = await self.user_factory_repository.get_by_user_and_factory(
                user.id, factory_id
            )
            
            factory_relationship_created = False
            if not existing_factory_relation:
                # Create factory relationship
                user_factory = UserFactory(
                    user_id=user.id,
                    factory_id=factory_id,
                    department_id=user_data.department_id,
                    role=UserFactoryRole(user_data.factory_role),
                    status=UserFactoryStatus.APPROVED,  # Direct add by manager
                    employee_id=user_data.employee_id,
                    position=user_data.position,
                    start_date=datetime.fromisoformat(user_data.start_date) if user_data.start_date else datetime.utcnow(),
                    approved_by=manager_user_id,
                    approved_at=datetime.utcnow()
                )
                
                user_factory = await self.user_factory_repository.create(user_factory)
                factory_relationship_created = True
            else:
                user_factory = existing_factory_relation

            # Step 4: Assign skills to user in factory
            skills_assigned = 0
            skill_errors = []
            
            for skill_data in user_data.skills:
                try:
                    # Check if skill exists
                    skill = await self.skill_repository.get_by_id(skill_data.skill_id)
                    if not skill:
                        skill_errors.append(f"Skill ID {skill_data.skill_id} not found")
                        continue

                    # Check if user already has this skill in this factory
                    existing_skill = await self.user_factory_skill_repository.get_by_user_factory_and_skill(
                        user_factory.id, skill_data.skill_id
                    )
                    
                    if existing_skill:
                        skill_errors.append(f"User already has skill '{skill.name}' in this factory")
                        continue

                    # Create user factory skill
                    user_factory_skill = UserFactorySkill(
                        user_factory_id=user_factory.id,
                        skill_id=skill_data.skill_id,
                        proficiency_level=skill_data.proficiency_level,
                        certified=skill_data.certified,
                        certification_date=datetime.fromisoformat(skill_data.certification_date) if skill_data.certification_date else None,
                        certification_expires=datetime.fromisoformat(skill_data.certification_expires) if skill_data.certification_expires else None,
                        notes=skill_data.notes,
                        assigned_by=manager_user_id
                    )
                    
                    await self.user_factory_skill_repository.create(user_factory_skill)
                    skills_assigned += 1
                    
                except Exception as e:
                    skill_errors.append(f"Failed to assign skill {skill_data.skill_id}: {str(e)}")

            # Prepare response
            success_message = []
            if user_created:
                success_message.append("User created successfully")
            else:
                success_message.append("Existing user found")
                
            if factory_relationship_created:
                success_message.append("added to factory")
            else:
                success_message.append("already in factory")
                
            if skills_assigned > 0:
                success_message.append(f"{skills_assigned} skills assigned")

            details = {
                "user_created": user_created,
                "factory_relationship_created": factory_relationship_created,
                "skills_assigned": skills_assigned,
                "total_skills_requested": len(user_data.skills),
                "skill_errors": skill_errors if skill_errors else None
            }

            return AddUserWithFactoryResponseDTO(
                success=True,
                message=", ".join(success_message),
                user_id=user.id,
                user_created=user_created,
                factory_relationship_created=factory_relationship_created,
                skills_assigned=skills_assigned,
                details=details
            )

        except Exception as e:
            return AddUserWithFactoryResponseDTO(
                success=False,
                message=f"Operation failed: {str(e)}",
                user_created=False,
                factory_relationship_created=False,
                skills_assigned=0,
                details={"error": str(e)}
            )