from typing import Optional, List
from src.domain.entities.user import User
from src.domain.value_objects.user_session import UserSession
from src.domain.entities.user_factory import UserFactoryStatus
from src.application.interfaces.user_factory_repository_interface import UserFactoryRepositoryInterface
from src.infrastructure.external_services.session_service import SessionServiceInterface


class SessionUseCases:
    """Use cases for session management."""
    
    def __init__(
        self,
        session_service: SessionServiceInterface,
        user_factory_repository: UserFactoryRepositoryInterface
    ):
        self.session_service = session_service
        self.user_factory_repository = user_factory_repository
    
    async def create_user_session(self, user: User) -> tuple[str, UserSession]:
        """Create a new user session with default factory context."""
        # Get user's active factories
        user_factories = await self.user_factory_repository.get_by_user_id(user.id)
        active_factories = [
            uf for uf in user_factories 
            if uf.status == UserFactoryStatus.APPROVED
        ]
        
        # Set first active factory as default context
        current_factory_id = None
        current_factory_name = None
        current_department_id = None
        current_role = None
        is_factory_manager = False
        
        if active_factories:
            first_factory = active_factories[0]
            current_factory_id = first_factory.factory_id
            current_factory_name = first_factory.factory.name if first_factory.factory else None
            current_department_id = first_factory.department_id
            current_role = first_factory.role.value if first_factory.role else None
            is_factory_manager = first_factory.can_approve_requests()
        
        # Create session object
        user_session = UserSession(
            user_id=user.id,
            username=user.username,
            current_factory_id=current_factory_id,
            current_factory_name=current_factory_name,
            current_department_id=current_department_id,
            current_role=current_role,
            is_factory_manager=is_factory_manager
        )
        
        # Store session in Redis
        session_id = await self.session_service.create_session(user_session)
        
        return session_id, user_session
    
    async def get_user_session(self, session_id: str) -> Optional[UserSession]:
        """Get user session by session ID."""
        return await self.session_service.get_session(session_id)
    
    async def switch_factory_context(
        self, 
        session_id: str, 
        factory_id: int
    ) -> UserSession:
        """Switch user's factory context in session."""
        # Get current session
        current_session = await self.session_service.get_session(session_id)
        if not current_session:
            raise ValueError("Session not found")
        
        # Verify user has access to this factory
        user_factory = await self.user_factory_repository.get_by_user_and_factory(
            current_session.user_id, factory_id
        )
        if not user_factory or user_factory.status != UserFactoryStatus.APPROVED:
            raise ValueError("You don't have access to this factory")
        
        # Create new session context
        new_session = current_session.switch_factory_context(
            factory_id=factory_id,
            factory_name=user_factory.factory.name if user_factory.factory else None,
            department_id=user_factory.department_id,
            role=user_factory.role.value if user_factory.role else None,
            is_manager=user_factory.can_approve_requests()
        )
        
        # Update session in Redis
        await self.session_service.update_session(session_id, new_session)
        
        return new_session
    
    async def get_user_available_factories(self, session_id: str) -> List[dict]:
        """Get list of factories user can switch to."""
        # Get current session
        current_session = await self.session_service.get_session(session_id)
        if not current_session:
            raise ValueError("Session not found")
        
        # Get user's active factories
        user_factories = await self.user_factory_repository.get_by_user_id(current_session.user_id)
        active_factories = [
            uf for uf in user_factories 
            if uf.status == UserFactoryStatus.APPROVED
        ]
        
        # Format factory list
        factories = []
        for uf in active_factories:
            factories.append({
                "factory_id": uf.factory_id,
                "factory_name": uf.factory.name if uf.factory else None,
                "factory_code": uf.factory.code if uf.factory else None,
                "department_id": uf.department_id,
                "department_name": uf.department.name if uf.department else None,
                "role": uf.role.value if uf.role else None,
                "employee_id": uf.employee_id,
                "position": uf.position,
                "is_manager": uf.can_approve_requests(),
                "is_current": uf.factory_id == current_session.current_factory_id
            })
        
        return factories
    
    async def extend_session(self, session_id: str) -> bool:
        """Extend session expiration."""
        return await self.session_service.extend_session(session_id)
    
    async def destroy_session(self, session_id: str) -> bool:
        """Destroy user session."""
        return await self.session_service.delete_session(session_id)
    
    async def refresh_factory_context(self, session_id: str) -> UserSession:
        """Refresh factory context with latest data from database."""
        # Get current session
        current_session = await self.session_service.get_session(session_id)
        if not current_session or not current_session.current_factory_id:
            return current_session
        
        # Get latest factory data
        user_factory = await self.user_factory_repository.get_by_user_and_factory(
            current_session.user_id, current_session.current_factory_id
        )
        
        if not user_factory or user_factory.status != UserFactoryStatus.APPROVED:
            # Factory access removed, clear context
            new_session = current_session.clear_factory_context()
        else:
            # Update context with latest data
            new_session = current_session.switch_factory_context(
                factory_id=user_factory.factory_id,
                factory_name=user_factory.factory.name if user_factory.factory else None,
                department_id=user_factory.department_id,
                role=user_factory.role.value if user_factory.role else None,
                is_manager=user_factory.can_approve_requests()
            )
        
        # Update session in Redis
        await self.session_service.update_session(session_id, new_session)
        
        return new_session