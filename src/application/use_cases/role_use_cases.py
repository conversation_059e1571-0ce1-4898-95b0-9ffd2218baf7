from typing import List, Optional
from src.application.interfaces.role_repository_interface import RoleRepositoryInterface
from src.application.interfaces.permission_repository_interface import PermissionRepositoryInterface
from src.application.dto.role_dto import (
    RoleCreateDTO, RoleUpdateDTO, RoleResponseDTO, RoleListDTO, 
    RolePermissionAssignDTO, RolePermissionRemoveDTO, RoleSummaryDTO
)
from src.application.dto.permission_dto import PermissionResponseDTO
from src.domain.entities.role import Role


class RoleUseCases:
    """Use cases for role operations."""

    def __init__(
        self, 
        role_repository: RoleRepositoryInterface,
        permission_repository: PermissionRepositoryInterface
    ):
        self.role_repository = role_repository
        self.permission_repository = permission_repository

    async def create_role(self, role_data: RoleCreateDTO) -> RoleResponseDTO:
        """Create a new role."""
        # Create role entity
        role = Role(
            name=role_data.name,
            description=role_data.description,
            is_active=role_data.is_active
        )
        
        # Save role
        created_role = await self.role_repository.create(role)
        
        # Assign permissions if provided
        if role_data.permission_ids:
            for permission_id in role_data.permission_ids:
                await self.role_repository.add_permission_to_role(created_role.id, permission_id)
        
        # Reload role with permissions
        role_with_permissions = await self.role_repository.get_by_id(created_role.id)
        return RoleResponseDTO.model_validate(role_with_permissions)

    async def get_role_by_id(self, role_id: int) -> Optional[RoleResponseDTO]:
        """Get role by ID."""
        role = await self.role_repository.get_by_id(role_id)
        if role:
            return RoleResponseDTO.model_validate(role)
        return None

    async def get_all_roles(self) -> RoleListDTO:
        """Get all roles."""
        roles = await self.role_repository.get_all()
        role_dtos = [RoleResponseDTO.model_validate(role) for role in roles]
        return RoleListDTO(roles=role_dtos, total=len(role_dtos))

    async def get_active_roles(self) -> RoleListDTO:
        """Get all active roles."""
        roles = await self.role_repository.get_active_roles()
        role_dtos = [RoleResponseDTO.model_validate(role) for role in roles]
        return RoleListDTO(roles=role_dtos, total=len(role_dtos))

    async def update_role(self, role_id: int, role_data: RoleUpdateDTO) -> Optional[RoleResponseDTO]:
        """Update role."""
        role = await self.role_repository.get_by_id(role_id)
        if not role:
            return None

        # Update fields if provided
        if role_data.name is not None:
            role.name = role_data.name
        if role_data.description is not None:
            role.description = role_data.description
        if role_data.is_active is not None:
            role.is_active = role_data.is_active

        updated_role = await self.role_repository.update(role)
        return RoleResponseDTO.model_validate(updated_role)

    async def delete_role(self, role_id: int) -> bool:
        """Delete role."""
        return await self.role_repository.delete(role_id)

    async def assign_permissions_to_role(self, role_id: int, assignment_data: RolePermissionAssignDTO) -> Optional[RoleResponseDTO]:
        """Assign permissions to role."""
        role = await self.role_repository.get_by_id(role_id)
        if not role:
            return None

        # Verify all permissions exist
        for permission_id in assignment_data.permission_ids:
            permission = await self.permission_repository.get_by_id(permission_id)
            if not permission:
                raise ValueError(f"Permission with ID {permission_id} not found")

        # Assign permissions
        for permission_id in assignment_data.permission_ids:
            await self.role_repository.add_permission_to_role(role_id, permission_id)

        # Return updated role
        updated_role = await self.role_repository.get_by_id(role_id)
        return RoleResponseDTO.model_validate(updated_role)

    async def remove_permissions_from_role(self, role_id: int, removal_data: RolePermissionRemoveDTO) -> Optional[RoleResponseDTO]:
        """Remove permissions from role."""
        role = await self.role_repository.get_by_id(role_id)
        if not role:
            return None

        # Remove permissions
        for permission_id in removal_data.permission_ids:
            await self.role_repository.remove_permission_from_role(role_id, permission_id)

        # Return updated role
        updated_role = await self.role_repository.get_by_id(role_id)
        return RoleResponseDTO.model_validate(updated_role)

    async def get_role_permissions(self, role_id: int) -> Optional[List[PermissionResponseDTO]]:
        """Get permissions assigned to a role."""
        role = await self.role_repository.get_by_id(role_id)
        if not role:
            return None

        permissions = await self.role_repository.get_role_permissions(role_id)
        return [PermissionResponseDTO.model_validate(permission) for permission in permissions]

    async def get_roles_summary(self) -> List[RoleSummaryDTO]:
        """Get summary of all roles without full permission details."""
        roles = await self.role_repository.get_all()
        summaries = []
        
        for role in roles:
            summary = RoleSummaryDTO(
                id=role.id,
                name=role.name,
                description=role.description,
                is_active=role.is_active,
                permission_count=len(role.permissions)
            )
            summaries.append(summary)
        
        return summaries