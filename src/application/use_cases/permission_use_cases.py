from typing import List
from src.application.interfaces.permission_repository_interface import PermissionRepositoryInterface
from src.application.dto.permission_dto import PermissionTreeDTO, PermissionListDTO, PermissionResponseDTO


class PermissionUseCases:
    """Use cases for permission operations."""

    def __init__(self, permission_repository: PermissionRepositoryInterface):
        self.permission_repository = permission_repository

    async def get_permission_tree(self) -> List[PermissionTreeDTO]:
        """Get hierarchical permission tree."""
        # Get all permissions and build tree in memory to avoid session issues
        all_permissions = await self.permission_repository.get_all()
        
        # Build a map for quick lookup
        permission_map = {p.id: p for p in all_permissions}
        
        # Build children map to avoid lazy loading
        children_map = {}
        for permission in all_permissions:
            children_map[permission.id] = [
                p for p in all_permissions if p.parent_id == permission.id
            ]
        
        # Get root permissions (those with no parent)
        root_permissions = [p for p in all_permissions if p.parent_id is None]
        
        return [self._build_permission_tree_from_map(permission, children_map) for permission in root_permissions]

    async def get_all_permissions(self) -> PermissionListDTO:
        """Get all permissions as a flat list."""
        permissions = await self.permission_repository.get_all()
        permission_dtos = [PermissionResponseDTO.model_validate(permission) for permission in permissions]
        return PermissionListDTO(permissions=permission_dtos, total=len(permission_dtos))

    def _build_permission_tree_from_map(self, permission, children_map) -> PermissionTreeDTO:
        """Build permission tree structure using pre-built children map."""
        children_permissions = children_map.get(permission.id, [])
        children = [self._build_permission_tree_from_map(child, children_map) for child in children_permissions]
        return PermissionTreeDTO(
            id=permission.id,
            code=permission.code,
            name=permission.name,
            parent_id=permission.parent_id,
            children=children
        )