from pydantic import BaseModel, Field
from typing import Optional


class PhonePasswordLoginDTO(BaseModel):
    """DTO for phone + password + image validation code login."""
    phone: str = Field(..., pattern=r'^1[3-9]\d{9}$', description="Chinese mobile number")
    password: str = Field(..., min_length=1)
    image_code: str = Field(..., min_length=4, max_length=6)
    session_id: str = Field(..., description="Session ID for image validation code")


class PhoneSmsLoginDTO(BaseModel):
    """DTO for phone + SMS + image validation code login."""
    phone: str = Field(..., pattern=r'^1[3-9]\d{9}$', description="Chinese mobile number")
    sms_code: str = Field(..., min_length=4, max_length=8)
    image_code: str = Field(..., min_length=4, max_length=6)
    session_id: str = Field(..., description="Session ID for image validation code")


class SendSmsCodeDTO(BaseModel):
    """DTO for requesting SMS validation code."""
    phone: str = Field(..., pattern=r'^1[3-9]\d{9}$', description="Chinese mobile number")
    image_code: str = Field(..., min_length=4, max_length=6)
    session_id: str = Field(..., description="Session ID for image validation code")


class GenerateImageCodeDTO(BaseModel):
    """DTO for generating image validation code."""
    session_id: Optional[str] = Field(None, description="Optional session ID, will generate if not provided")


class ImageCodeResponseDTO(BaseModel):
    """Response DTO for image validation code."""
    session_id: str
    image_base64: str
    expires_in_seconds: int


class SendSmsCodeResponseDTO(BaseModel):
    """Response DTO for SMS code sending."""
    success: bool
    message: str
    expires_in_seconds: int


class TokenResponseDTO(BaseModel):
    """Response DTO for authentication token."""
    access_token: str
    token_type: str = "bearer"
    session_id: str
    user_info: dict
    factory_context: dict


class ValidationCodeCheckDTO(BaseModel):
    """DTO for validation code verification."""
    code: str = Field(..., min_length=4, max_length=8)
    identifier: str = Field(..., description="Phone number or session ID")
    code_type: str = Field(..., description="sms or image")