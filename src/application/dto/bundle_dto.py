from typing import List, Optional
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum


class BundleStatusEnum(str, Enum):
    """Bundle status enumeration for DTOs."""
    PLANNED = "planned"
    CUTTING = "cutting"
    CUT_COMPLETED = "cut_completed"
    SEWING = "sewing"
    SEW_COMPLETED = "sew_completed"
    QUALITY_CHECK = "quality_check"
    COMPLETED = "completed"
    REWORK = "rework"
    ON_HOLD = "on_hold"
    CANCELLED = "cancelled"


class BundleBaseDTO(BaseModel):
    """Base bundle DTO."""
    size: str = Field(..., min_length=1, max_length=20, description="尺码")
    quantity: int = Field(..., ge=0, description="扎件数")
    notes: Optional[str] = Field(None, max_length=1000, description="备注")
    planned_start_date: Optional[datetime] = Field(None, description="计划开始时间")
    planned_end_date: Optional[datetime] = Field(None, description="计划完成时间")


class BundleCreateDTO(BundleBaseDTO):
    """DTO for creating a new bundle."""
    part_no: str = Field(..., min_length=1, max_length=100, description="部位号")
    order_no: str = Field(..., min_length=1, max_length=100, description="订单号")
    skc_no: str = Field(..., min_length=1, max_length=100, description="款号")
    color: str = Field(..., min_length=1, max_length=50, description="颜色")


class BundleUpdateDTO(BaseModel):
    """DTO for updating a bundle."""
    quantity: Optional[int] = Field(None, ge=0, description="扎件数")
    completed_quantity: Optional[int] = Field(None, ge=0, description="已完成件数")
    defective_quantity: Optional[int] = Field(None, ge=0, description="次品件数")
    rework_quantity: Optional[int] = Field(None, ge=0, description="返工件数")
    status: Optional[BundleStatusEnum] = Field(None, description="扎状态")
    notes: Optional[str] = Field(None, max_length=1000, description="备注")
    planned_start_date: Optional[datetime] = Field(None, description="计划开始时间")
    planned_end_date: Optional[datetime] = Field(None, description="计划完成时间")
    actual_start_date: Optional[datetime] = Field(None, description="实际开始时间")
    actual_end_date: Optional[datetime] = Field(None, description="实际完成时间")
    cutting_machine: Optional[str] = Field(None, max_length=50, description="裁剪机床")
    sewing_machine: Optional[str] = Field(None, max_length=50, description="缝制机床")
    quality_level: Optional[str] = Field(None, max_length=20, description="质量等级(A/B/C)")
    quality_notes: Optional[str] = Field(None, max_length=1000, description="质量备注")
    cutter_user_id: Optional[int] = Field(None, description="裁剪工ID")
    sewer_user_id: Optional[int] = Field(None, description="缝制工ID")
    qc_user_id: Optional[int] = Field(None, description="质检员ID")


class BundleResponseDTO(BundleBaseDTO):
    """DTO for bundle response."""
    id: int = Field(..., description="扎ID")
    bundle_no: str = Field(..., description="扎号")
    part_no: str = Field(..., description="部位号")
    order_no: str = Field(..., description="订单号")
    skc_no: str = Field(..., description="款号")
    color: str = Field(..., description="颜色")
    bundle_sequence: int = Field(..., description="扎序号")
    completed_quantity: int = Field(..., description="已完成件数")
    defective_quantity: int = Field(..., description="次品件数")
    rework_quantity: int = Field(..., description="返工件数")
    status: BundleStatusEnum = Field(..., description="扎状态")
    progress_percentage: int = Field(..., description="完成百分比")
    
    # Timing information
    actual_start_date: Optional[datetime] = Field(None, description="实际开始时间")
    actual_end_date: Optional[datetime] = Field(None, description="实际完成时间")
    cut_start_date: Optional[datetime] = Field(None, description="裁剪开始时间")
    cut_end_date: Optional[datetime] = Field(None, description="裁剪完成时间")
    sew_start_date: Optional[datetime] = Field(None, description="缝制开始时间")
    sew_end_date: Optional[datetime] = Field(None, description="缝制完成时间")
    qc_start_date: Optional[datetime] = Field(None, description="质检开始时间")
    qc_end_date: Optional[datetime] = Field(None, description="质检完成时间")
    
    # Machine and worker information
    cutting_machine: Optional[str] = Field(None, description="裁剪机床")
    sewing_machine: Optional[str] = Field(None, description="缝制机床")
    cutter_name: Optional[str] = Field(None, description="裁剪工姓名")
    sewer_name: Optional[str] = Field(None, description="缝制工姓名")
    qc_user_name: Optional[str] = Field(None, description="质检员姓名")
    
    # Quality information
    quality_level: Optional[str] = Field(None, description="质量等级(A/B/C)")
    quality_notes: Optional[str] = Field(None, description="质量备注")
    
    # System fields
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    class Config:
        from_attributes = True


class BundleListDTO(BaseModel):
    """DTO for bundle list response."""
    bundles: List[BundleResponseDTO] = Field(..., description="扎列表")
    total: int = Field(..., description="总数量")


class BundleSearchDTO(BaseModel):
    """DTO for bundle search criteria."""
    search_term: Optional[str] = Field(None, description="搜索关键词 (扎号、部位号等)")
    part_no: Optional[str] = Field(None, description="部位号")
    order_no: Optional[str] = Field(None, description="订单号")
    size: Optional[str] = Field(None, description="尺码")
    status: Optional[BundleStatusEnum] = Field(None, description="扎状态")
    color: Optional[str] = Field(None, description="颜色")
    cutter_user_id: Optional[int] = Field(None, description="裁剪工ID")
    sewer_user_id: Optional[int] = Field(None, description="缝制工ID")
    qc_user_id: Optional[int] = Field(None, description="质检员ID")


class BundleOperationResultDTO(BaseModel):
    """DTO for bundle operation results."""
    success: bool = Field(..., description="操作是否成功")
    message: str = Field(..., description="结果消息")
    bundle_id: Optional[int] = Field(None, description="扎ID")
    bundle_no: Optional[str] = Field(None, description="扎号")
    details: Optional[dict] = Field(None, description="详细信息")


class BundleStatusUpdateDTO(BaseModel):
    """DTO for updating bundle status."""
    status: BundleStatusEnum = Field(..., description="新状态")
    notes: Optional[str] = Field(None, description="状态变更备注")


class BundleProductionUpdateDTO(BaseModel):
    """DTO for updating bundle production."""
    completed_quantity: Optional[int] = Field(None, ge=0, description="已完成数量")
    defective_quantity: Optional[int] = Field(None, ge=0, description="次品数量")
    rework_quantity: Optional[int] = Field(None, ge=0, description="返工数量")
    quality_level: Optional[str] = Field(None, max_length=20, description="质量等级(A/B/C)")
    quality_notes: Optional[str] = Field(None, description="质量备注")
    notes: Optional[str] = Field(None, description="生产备注")


class BundleWorkflowUpdateDTO(BaseModel):
    """DTO for updating bundle workflow progress."""
    action: str = Field(..., description="操作类型 (start_cutting, complete_cutting, start_sewing, complete_sewing, start_qc, complete_qc)")
    worker_user_id: Optional[int] = Field(None, description="操作工人ID")
    machine: Optional[str] = Field(None, description="机床编号")
    quality_level: Optional[str] = Field(None, description="质量等级 (仅质检完成时)")
    defective_quantity: Optional[int] = Field(None, ge=0, description="次品数量 (仅质检完成时)")
    quality_notes: Optional[str] = Field(None, description="质量备注 (仅质检完成时)")
    notes: Optional[str] = Field(None, description="操作备注")


class BulkBundleCreateDTO(BaseModel):
    """DTO for creating multiple bundles."""
    part_no: str = Field(..., description="部位号")
    bundles: List[BundleCreateDTO] = Field(..., min_length=1, description="扎列表")


class BundleStatisticsDTO(BaseModel):
    """DTO for bundle statistics."""
    total_bundles: int = Field(..., description="总扎数")
    completed_bundles: int = Field(..., description="已完成扎数")
    in_progress_bundles: int = Field(..., description="进行中扎数")
    pending_bundles: int = Field(..., description="待处理扎数")
    total_quantity: int = Field(..., description="总件数")
    completed_quantity: int = Field(..., description="已完成件数")
    defective_quantity: int = Field(..., description="次品件数")
    rework_quantity: int = Field(..., description="返工件数")
    completion_percentage: float = Field(..., description="完成百分比")
    quality_rate: float = Field(..., description="良品率")
    status_breakdown: dict = Field(..., description="状态分解")
