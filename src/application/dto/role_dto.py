from typing import Optional, List
from pydantic import BaseModel, Field
from .permission_dto import PermissionResponseDTO


class RoleBaseDTO(BaseModel):
    """Base role DTO."""
    name: str = Field(..., min_length=1, max_length=100, description="Unique role name")
    description: Optional[str] = Field(None, max_length=1000, description="Role description")
    is_active: bool = Field(True, description="Whether the role is active")


class RoleCreateDTO(RoleBaseDTO):
    """DTO for creating a new role."""
    permission_ids: List[int] = Field(default_factory=list, description="List of permission IDs to assign")


class RoleUpdateDTO(BaseModel):
    """DTO for updating a role."""
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="Role name")
    description: Optional[str] = Field(None, max_length=1000, description="Role description")
    is_active: Optional[bool] = Field(None, description="Whether the role is active")


class RoleResponseDTO(RoleBaseDTO):
    """DTO for role response."""
    id: int = Field(..., description="Role ID")
    permissions: List[PermissionResponseDTO] = Field(default_factory=list, description="Assigned permissions")
    
    class Config:
        from_attributes = True


class RoleListDTO(BaseModel):
    """DTO for role list response."""
    roles: List[RoleResponseDTO] = Field(..., description="List of roles")
    total: int = Field(..., description="Total number of roles")


class RolePermissionAssignDTO(BaseModel):
    """DTO for assigning permissions to role."""
    permission_ids: List[int] = Field(..., description="List of permission IDs to assign")


class RolePermissionRemoveDTO(BaseModel):
    """DTO for removing permissions from role."""
    permission_ids: List[int] = Field(..., description="List of permission IDs to remove")


class UserRoleAssignDTO(BaseModel):
    """DTO for assigning role to user."""
    role_id: int = Field(..., description="Role ID to assign")


class RoleSummaryDTO(BaseModel):
    """DTO for role summary without permissions."""
    id: int = Field(..., description="Role ID")
    name: str = Field(..., description="Role name")
    description: Optional[str] = Field(None, description="Role description")
    is_active: bool = Field(..., description="Whether the role is active")
    permission_count: int = Field(..., description="Number of permissions assigned")
    
    class Config:
        from_attributes = True