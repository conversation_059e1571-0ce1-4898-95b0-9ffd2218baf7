from typing import Optional, List
from pydantic import BaseModel
from datetime import datetime


class SessionFactoryContextDTO(BaseModel):
    """DTO for factory context in session."""
    factory_id: Optional[int] = None
    factory_name: Optional[str] = None
    factory_code: Optional[str] = None
    department_id: Optional[int] = None
    department_name: Optional[str] = None
    role: Optional[str] = None
    employee_id: Optional[str] = None
    position: Optional[str] = None
    is_manager: bool = False


class UserSessionDTO(BaseModel):
    """DTO for user session information."""
    user_id: int
    username: str
    session_id: str
    factory_context: SessionFactoryContextDTO
    session_created_at: datetime


class SwitchFactoryDTO(BaseModel):
    """DTO for switching factory context."""
    factory_id: int


class AvailableFactoryDTO(BaseModel):
    """DTO for available factory in switch list."""
    factory_id: int
    factory_name: str
    factory_code: str
    department_id: Optional[int] = None
    department_name: Optional[str] = None
    role: str
    employee_id: Optional[str] = None
    position: Optional[str] = None
    is_manager: bool
    is_current: bool


class AvailableFactoriesDTO(BaseModel):
    """DTO for list of available factories."""
    factories: List[AvailableFactoryDTO]
    current_factory_id: Optional[int] = None


class SessionStatusDTO(BaseModel):
    """DTO for session status response."""
    is_valid: bool
    user_session: Optional[UserSessionDTO] = None
    message: Optional[str] = None