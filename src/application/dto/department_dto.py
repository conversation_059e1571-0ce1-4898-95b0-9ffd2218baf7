from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, Field


class DepartmentBaseDTO(BaseModel):
    """Base department DTO."""
    name: str = Field(..., min_length=1, max_length=100, description="Department name")
    code: str = Field(..., min_length=1, max_length=20, description="Department code")
    manager_name: Optional[str] = Field(None, max_length=100, description="Manager name")
    phone: Optional[str] = Field(None, max_length=20, description="Phone number")
    email: Optional[str] = Field(None, max_length=100, description="Email address")
    location: Optional[str] = Field(None, max_length=200, description="Department location")
    description: Optional[str] = Field(None, max_length=1000, description="Department description")
    is_active: bool = Field(True, description="Whether the department is active")


class DepartmentCreateDTO(DepartmentBaseDTO):
    """DTO for creating a new department."""
    operator_id: int = Field(..., description="ID of the user creating the department")


class DepartmentUpdateDTO(BaseModel):
    """DTO for updating a department."""
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="Department name")
    code: Optional[str] = Field(None, min_length=1, max_length=20, description="Department code")
    manager_name: Optional[str] = Field(None, max_length=100, description="Manager name")
    phone: Optional[str] = Field(None, max_length=20, description="Phone number")
    email: Optional[str] = Field(None, max_length=100, description="Email address")
    location: Optional[str] = Field(None, max_length=200, description="Department location")
    description: Optional[str] = Field(None, max_length=1000, description="Department description")
    is_active: Optional[bool] = Field(None, description="Whether the department is active")
    operator_id: int = Field(..., description="ID of the user updating the department")


class DepartmentResponseDTO(DepartmentBaseDTO):
    """DTO for department response."""
    id: int = Field(..., description="Department ID")
    factory_id: int = Field(..., description="Factory ID")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    
    class Config:
        from_attributes = True


class DepartmentListDTO(BaseModel):
    """DTO for department list response."""
    departments: List[DepartmentResponseDTO] = Field(..., description="List of departments")
    total: int = Field(..., description="Total number of departments")


class DepartmentSearchDTO(BaseModel):
    """DTO for department search criteria."""
    search_term: Optional[str] = Field(None, description="Search term for name or code")
    is_active: Optional[bool] = Field(None, description="Filter by active status")
    manager_name: Optional[str] = Field(None, description="Filter by manager name")


class DepartmentOperationResultDTO(BaseModel):
    """DTO for department operation results."""
    success: bool = Field(..., description="Whether the operation was successful")
    message: str = Field(..., description="Operation result message")
    department_id: Optional[int] = Field(None, description="Department ID if applicable")
    operator_id: int = Field(..., description="ID of the user who performed the operation")
