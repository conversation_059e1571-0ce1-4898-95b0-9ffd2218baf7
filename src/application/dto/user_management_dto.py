from typing import List, Optional
from pydantic import BaseModel, Field
from .role_dto import RoleSummaryDTO
from .skill_dto import UserFactorySkillDTO


class UserSummaryDTO(BaseModel):
    """DTO for user summary information."""
    id: int = Field(..., description="User ID")
    username: str = Field(..., description="Username")
    full_name: Optional[str] = Field(None, description="User's full name")
    email: str = Field(..., description="Email address")
    phone: Optional[str] = Field(None, description="Phone number")
    is_active: bool = Field(..., description="Whether user is active")
    role: Optional[RoleSummaryDTO] = Field(None, description="User's system role")
    
    class Config:
        from_attributes = True


class FactoryUserDTO(BaseModel):
    """DTO for factory user with factory-specific information."""
    user: UserSummaryDTO = Field(..., description="User information")
    factory_role: str = Field(..., description="Role in factory (WORKER, SUPERVISOR, MANAGER, ADMIN)")
    factory_status: str = Field(..., description="Status in factory (APPROVED, SUSPENDED, etc.)")
    joined_at: Optional[str] = Field(None, description="Date joined factory")
    skills: List[UserFactorySkillDTO] = Field(default_factory=list, description="User's skills in this factory")
    skills_count: int = Field(0, description="Total number of skills")
    certified_skills_count: int = Field(0, description="Number of certified skills")
    
    class Config:
        from_attributes = True


class FactoryUserListDTO(BaseModel):
    """DTO for factory user list response."""
    users: List[FactoryUserDTO] = Field(..., description="List of factory users")
    total: int = Field(..., description="Total number of users")
    factory_id: int = Field(..., description="Factory ID")
    factory_name: str = Field(..., description="Factory name")


class AddUserToFactoryDTO(BaseModel):
    """DTO for adding user to factory."""
    user_id: int = Field(..., description="User ID to add")
    factory_role: str = Field("WORKER", description="Initial role in factory")
    start_date: Optional[str] = Field(None, description="Start date (ISO format)")


class AddUsersToFactoryDTO(BaseModel):
    """DTO for adding multiple users to factory."""
    users: List[AddUserToFactoryDTO] = Field(..., description="List of users to add")


class BindUserRoleDTO(BaseModel):
    """DTO for binding system role to user."""
    user_id: int = Field(..., description="User ID")
    role_id: int = Field(..., description="Role ID to assign")


class SuspendUserDTO(BaseModel):
    """DTO for suspending user."""
    user_id: int = Field(..., description="User ID to suspend")
    reason: Optional[str] = Field(None, description="Reason for suspension")
    suspension_note: Optional[str] = Field(None, description="Additional notes")


class RemoveUserFromFactoryDTO(BaseModel):
    """DTO for removing user from factory."""
    user_id: int = Field(..., description="User ID to remove")
    reason: Optional[str] = Field(None, description="Reason for removal")


class UserSearchDTO(BaseModel):
    """DTO for user search criteria."""
    search_term: Optional[str] = Field(None, description="Search by username, email, or full name")
    role_id: Optional[int] = Field(None, description="Filter by system role")
    is_active: Optional[bool] = Field(None, description="Filter by active status")
    not_in_factory: Optional[bool] = Field(None, description="Only show users not in current factory")


class AvailableUsersDTO(BaseModel):
    """DTO for available users (not in factory)."""
    users: List[UserSummaryDTO] = Field(..., description="List of available users")
    total: int = Field(..., description="Total number of available users")


class UserOperationResultDTO(BaseModel):
    """DTO for user operation results."""
    success: bool = Field(..., description="Whether operation succeeded")
    message: str = Field(..., description="Result message")
    user_id: int = Field(..., description="User ID affected")
    details: Optional[dict] = Field(None, description="Additional operation details")


class UserSkillCreateDTO(BaseModel):
    """DTO for creating user skill."""
    skill_id: int = Field(..., description="Skill ID")
    proficiency_level: str = Field("BEGINNER", description="Proficiency level (BEGINNER, INTERMEDIATE, ADVANCED, EXPERT)")
    certified: bool = Field(False, description="Whether user is initially certified")
    certification_date: Optional[str] = Field(None, description="Certification date (ISO format)")
    certification_expires: Optional[str] = Field(None, description="Certification expiry date (ISO format)")
    notes: Optional[str] = Field(None, description="Additional notes")


class CreateUserWithFactoryDTO(BaseModel):
    """DTO for creating a new user with factory relationship and skills."""
    # User basic information
    username: str = Field(..., min_length=1, max_length=50, description="Username")
    email: str = Field(..., description="Email address")
    password: str = Field(..., min_length=6, description="Password")
    full_name: Optional[str] = Field(None, max_length=100, description="Full name")
    phone: Optional[str] = Field(None, pattern=r'^1[3-9]\d{9}$', description="Phone number (Chinese format)")
    is_active: bool = Field(True, description="Whether user is active")
    
    # System role (optional)
    role_id: Optional[int] = Field(None, description="System role ID to assign")
    
    # Factory relationship
    factory_role: str = Field("WORKER", description="Role in factory (WORKER, SUPERVISOR, MANAGER, ADMIN)")
    department_id: Optional[int] = Field(None, description="Department ID in factory")
    start_date: Optional[str] = Field(None, description="Start date (ISO format)")
    position: Optional[str] = Field(None, max_length=100, description="Position/job title")
    employee_id: Optional[str] = Field(None, max_length=50, description="Employee ID")
    
    # Skills
    skills: List[UserSkillCreateDTO] = Field(default_factory=list, description="Initial skills to assign")


class AddUserWithFactoryResponseDTO(BaseModel):
    """Response DTO for add user with factory operation."""
    success: bool = Field(..., description="Whether operation succeeded")
    message: str = Field(..., description="Result message")
    user_id: Optional[int] = Field(None, description="User ID (if created or found)")
    user_created: bool = Field(..., description="Whether a new user was created")
    factory_relationship_created: bool = Field(..., description="Whether factory relationship was created")
    skills_assigned: int = Field(0, description="Number of skills successfully assigned")
    details: Optional[dict] = Field(None, description="Additional operation details")