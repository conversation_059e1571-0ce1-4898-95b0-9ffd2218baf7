from typing import List, Optional
from pydantic import BaseModel, Field
from datetime import datetime


class CraftBaseDTO(BaseModel):
    """Base craft DTO."""
    code: str = Field(..., min_length=1, max_length=50, description="Unique craft code")
    name: str = Field(..., min_length=1, max_length=100, description="Craft name")
    priority: int = Field(0, ge=0, description="Craft priority (higher number = higher priority)")
    enabled: bool = Field(True, description="Whether the craft is enabled")
    description: Optional[str] = Field(None, max_length=1000, description="Craft description")


class CraftCreateDTO(CraftBaseDTO):
    """DTO for creating a new craft."""
    pass


class CraftUpdateDTO(BaseModel):
    """DTO for updating a craft."""
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="Craft name")
    priority: Optional[int] = Field(None, ge=0, description="Craft priority")
    enabled: Optional[bool] = Field(None, description="Whether the craft is enabled")
    description: Optional[str] = Field(None, max_length=1000, description="Craft description")


class CraftResponseDTO(CraftBaseDTO):
    """DTO for craft response."""
    id: int = Field(..., description="Craft ID")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    
    class Config:
        from_attributes = True


class CraftListDTO(BaseModel):
    """DTO for craft list response."""
    crafts: List[CraftResponseDTO] = Field(..., description="List of crafts")
    total: int = Field(..., description="Total number of crafts")


class CraftRouteBaseDTO(BaseModel):
    """Base craft route DTO."""
    craft_code: str = Field(..., min_length=1, max_length=50, description="Craft code")
    skill_code: str = Field(..., min_length=1, max_length=50, description="Skill code")
    code: str = Field(..., min_length=1, max_length=50, description="Unique code within craft")
    name: str = Field(..., min_length=1, max_length=100, description="Display name for this route")
    order: int = Field(0, ge=0, description="Order in the craft workflow")
    measurement_types: Optional[List[str]] = Field(None, description="Available measurement types")
    registration_types: Optional[List[str]] = Field(None, description="Available registration types")
    notes: Optional[str] = Field(None, max_length=1000, description="Additional notes")
    is_required: bool = Field(True, description="Whether this step is required")


class CraftRouteCreateDTO(CraftRouteBaseDTO):
    """DTO for creating a new craft route."""
    pass


class CraftRouteUpdateDTO(BaseModel):
    """DTO for updating a craft route."""
    code: Optional[str] = Field(None, min_length=1, max_length=50, description="Unique code within craft")
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="Display name for this route")
    order: Optional[int] = Field(None, ge=0, description="Order in the craft workflow")
    measurement_types: Optional[List[str]] = Field(None, description="Available measurement types")
    registration_types: Optional[List[str]] = Field(None, description="Available registration types")
    notes: Optional[str] = Field(None, max_length=1000, description="Additional notes")
    is_required: Optional[bool] = Field(None, description="Whether this step is required")


class CraftRouteResponseDTO(CraftRouteBaseDTO):
    """DTO for craft route response."""
    id: int = Field(..., description="Craft route ID")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    
    class Config:
        from_attributes = True


class CraftRouteDetailDTO(BaseModel):
    """DTO for detailed craft route with skill information."""
    id: int = Field(..., description="Craft route ID")
    craft_code: str = Field(..., description="Craft code")
    skill_code: str = Field(..., description="Skill code")
    skill_name: str = Field(..., description="Skill name")
    code: str = Field(..., description="Unique code within craft")
    name: str = Field(..., description="Display name for this route")
    order: int = Field(..., description="Order in the craft workflow")
    measurement_types: List[str] = Field(..., description="Available measurement types")
    registration_types: List[str] = Field(..., description="Available registration types")
    notes: Optional[str] = Field(None, description="Additional notes")
    is_required: bool = Field(..., description="Whether this step is required")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")


class CraftWithRoutesDTO(CraftResponseDTO):
    """DTO for craft with its routes."""
    routes: List[CraftRouteDetailDTO] = Field(..., description="Craft routes ordered by order field")


class CraftRouteListDTO(BaseModel):
    """DTO for craft route list response."""
    routes: List[CraftRouteDetailDTO] = Field(..., description="List of craft routes")
    total: int = Field(..., description="Total number of routes")


class CraftSearchDTO(BaseModel):
    """DTO for craft search criteria."""
    search_term: Optional[str] = Field(None, description="Search by code, name, or description")
    enabled: Optional[bool] = Field(None, description="Filter by enabled status")
    min_priority: Optional[int] = Field(None, ge=0, description="Minimum priority")
    max_priority: Optional[int] = Field(None, ge=0, description="Maximum priority")


class CraftRouteSearchDTO(BaseModel):
    """DTO for craft route search criteria."""
    craft_code: Optional[str] = Field(None, description="Filter by craft code")
    skill_code: Optional[str] = Field(None, description="Filter by skill code")
    is_required: Optional[bool] = Field(None, description="Filter by required status")


class ReorderRoutesDTO(BaseModel):
    """DTO for reordering craft routes."""
    route_orders: List[tuple] = Field(..., description="List of (route_id, new_order) tuples")


class CraftOperationResultDTO(BaseModel):
    """DTO for craft operation results."""
    success: bool = Field(..., description="Whether operation succeeded")
    message: str = Field(..., description="Result message")
    craft_id: Optional[int] = Field(None, description="Craft ID affected")
    craft_route_id: Optional[int] = Field(None, description="Craft route ID affected")
    details: Optional[dict] = Field(None, description="Additional operation details")


class BulkCraftRouteCreateDTO(BaseModel):
    """DTO for creating multiple craft routes at once."""
    craft_code: str = Field(..., description="Craft code for all routes")
    routes: List[CraftRouteCreateDTO] = Field(..., description="List of routes to create")


class BulkCraftRouteOperationResultDTO(BaseModel):
    """DTO for bulk craft route operation results."""
    success: bool = Field(..., description="Whether operation succeeded overall")
    message: str = Field(..., description="Overall result message")
    results: List[CraftOperationResultDTO] = Field(..., description="Individual operation results")
    successful_count: int = Field(..., description="Number of successful operations")
    failed_count: int = Field(..., description="Number of failed operations")