from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum


class CompletionGranularityDTO(str, Enum):
    """完成粒度枚举"""
    BUNDLE = "bundle"  # 按扎登记
    BED = "bed"        # 按床登记  
    ORDER = "order"    # 整单登记


class SettlementStatusDTO(str, Enum):
    """结算状态枚举"""
    PENDING = "pending"       # 待结算
    INCLUDED = "included"     # 已纳入账单
    SETTLED = "settled"       # 已结算
    DISPUTED = "disputed"     # 有争议
    EXCLUDED = "excluded"     # 排除结算


class CraftInstanceCreateDTO(BaseModel):
    """DTO for creating craft instances - worker completion records."""
    order_craft_route_id: int = Field(..., description="订单工艺路线ID")
    completion_granularity: CompletionGranularityDTO = Field(..., description="完成粒度")
    order_no: str = Field(..., min_length=1, max_length=100, description="订单号")
    
    # Multiple parts/bundles support
    order_part_nos: Optional[List[str]] = Field(None, description="订单部位号列表 (床级别或扎级别时填写)")
    order_bundle_nos: Optional[List[str]] = Field(None, description="订单扎号列表 (扎级别时填写)")
    
    # Single part/bundle support (for backward compatibility)
    order_part_no: Optional[str] = Field(None, max_length=100, description="订单部位号 (兼容性字段)")
    order_bundle_no: Optional[str] = Field(None, max_length=100, description="订单扎号 (兼容性字段)")
    
    worker_user_id: int = Field(..., description="完成工人ID")
    completed_quantity: int = Field(..., ge=0, description="完成数量")
    quality_level: Optional[str] = Field(None, max_length=10, description="质量等级 A/B/C")
    started_at: Optional[datetime] = Field(None, description="开始时间")
    qr_code_scanned: Optional[str] = Field(None, max_length=200, description="扫描的二维码内容")
    scan_location: Optional[str] = Field(None, max_length=100, description="扫码位置")
    device_info: Optional[Dict[str, Any]] = Field(None, description="设备信息")
    measurement_data: Optional[Dict[str, Any]] = Field(None, description="测量数据")
    registration_data: Optional[Dict[str, Any]] = Field(None, description="登记数据")
    notes: Optional[str] = Field(None, max_length=1000, description="备注")


class CraftInstanceUpdateDTO(BaseModel):
    """DTO for updating craft instances."""
    completed_quantity: Optional[int] = Field(None, ge=0, description="完成数量")
    quality_level: Optional[str] = Field(None, max_length=10, description="质量等级 A/B/C")
    status: Optional[str] = Field(None, description="状态: completed, verified, rejected")
    settlement_status: Optional[SettlementStatusDTO] = Field(None, description="结算状态")
    measurement_data: Optional[Dict[str, Any]] = Field(None, description="测量数据")
    registration_data: Optional[Dict[str, Any]] = Field(None, description="登记数据")
    notes: Optional[str] = Field(None, max_length=1000, description="备注")


class CraftInstanceResponseDTO(BaseModel):
    """DTO for craft instance response."""
    id: int = Field(..., description="实例ID")
    factory_id: int = Field(..., description="工厂ID")
    order_craft_route_id: int = Field(..., description="订单工艺路线ID")
    completion_granularity: CompletionGranularityDTO = Field(..., description="完成粒度")
    order_no: str = Field(..., description="订单号")
    
    # Multiple parts/bundles support
    order_part_nos: Optional[List[str]] = Field(None, description="订单部位号列表")
    order_bundle_nos: Optional[List[str]] = Field(None, description="订单扎号列表")
    
    # Single part/bundle support (for backward compatibility)
    order_part_no: Optional[str] = Field(None, description="主要订单部位号")
    order_bundle_no: Optional[str] = Field(None, description="主要订单扎号")
    
    worker_user_id: int = Field(..., description="完成工人ID")
    worker_name: Optional[str] = Field(None, description="工人姓名")
    completed_quantity: int = Field(..., description="完成数量")
    quality_level: Optional[str] = Field(None, description="质量等级")
    status: str = Field(..., description="状态")
    settlement_status: SettlementStatusDTO = Field(..., description="结算状态")
    started_at: Optional[datetime] = Field(None, description="开始时间")
    completed_at: datetime = Field(..., description="完成时间")
    work_duration_minutes: Optional[int] = Field(None, description="工作时长(分钟)")
    qr_code_scanned: Optional[str] = Field(None, description="扫描的二维码内容")
    scan_location: Optional[str] = Field(None, description="扫码位置")
    device_info: Optional[Dict[str, Any]] = Field(None, description="设备信息")
    measurement_data: Optional[Dict[str, Any]] = Field(None, description="测量数据")
    registration_data: Optional[Dict[str, Any]] = Field(None, description="登记数据")
    notes: Optional[str] = Field(None, description="备注")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    # Related info
    craft_route_name: Optional[str] = Field(None, description="工艺路线名称")
    skill_code: Optional[str] = Field(None, description="技能代码")
    skill_name: Optional[str] = Field(None, description="技能名称")
    
    class Config:
        from_attributes = True


class CraftInstanceVerificationDTO(BaseModel):
    """DTO for verifying craft instances."""
    verified_by_user_id: int = Field(..., description="验证人ID")
    notes: Optional[str] = Field(None, max_length=1000, description="验证备注")


class CraftInstanceRejectionDTO(BaseModel):
    """DTO for rejecting craft instances."""
    rejected_by_user_id: int = Field(..., description="拒绝人ID")
    reason: str = Field(..., min_length=1, max_length=1000, description="拒绝原因")


class CraftInstanceSearchDTO(BaseModel):
    """DTO for searching craft instances."""
    order_no: Optional[str] = Field(None, description="订单号")
    worker_user_id: Optional[int] = Field(None, description="工人ID")
    completion_granularity: Optional[CompletionGranularityDTO] = Field(None, description="完成粒度")
    status: Optional[str] = Field(None, description="状态")
    settlement_status: Optional[SettlementStatusDTO] = Field(None, description="结算状态")
    quality_level: Optional[str] = Field(None, description="质量等级")
    start_date: Optional[datetime] = Field(None, description="开始日期")
    end_date: Optional[datetime] = Field(None, description="结束日期")
    date_field: Optional[str] = Field("completed_at", description="日期字段")


class CraftInstanceListDTO(BaseModel):
    """DTO for craft instance list response."""
    instances: List[CraftInstanceResponseDTO] = Field(..., description="实例列表")
    total: int = Field(..., description="总数量")
    total_quantity: int = Field(..., description="总完成数量")


class CraftInstanceOperationResultDTO(BaseModel):
    """DTO for craft instance operation results."""
    success: bool = Field(..., description="操作是否成功")
    message: str = Field(..., description="结果消息")
    instance_id: Optional[int] = Field(None, description="实例ID")
    details: Optional[dict] = Field(None, description="详细信息")


class CraftInstanceStatisticsDTO(BaseModel):
    """DTO for craft instance statistics."""
    total_instances: int = Field(..., description="总实例数")
    total_quantity: int = Field(..., description="总完成数量")
    total_workers: int = Field(..., description="参与工人数")
    average_quality_score: Optional[float] = Field(None, description="平均质量分数")
    status_breakdown: Dict[str, int] = Field(..., description="状态分解")
    settlement_breakdown: Dict[str, int] = Field(..., description="结算状态分解")
    granularity_breakdown: Dict[str, int] = Field(..., description="粒度分解")
    quality_breakdown: Dict[str, int] = Field(..., description="质量等级分解")
    daily_completion_trend: List[Dict[str, Any]] = Field(..., description="每日完成趋势")


class CraftInstanceBatchCreateDTO(BaseModel):
    """DTO for batch creating craft instances."""
    instances: List[CraftInstanceCreateDTO] = Field(..., min_items=1, description="实例列表")


class CraftInstanceQRScanDTO(BaseModel):
    """DTO for QR code scanning registration."""
    qr_code_content: str = Field(..., min_length=1, description="二维码内容")
    scan_location: Optional[str] = Field(None, description="扫码位置")
    device_info: Optional[Dict[str, Any]] = Field(None, description="设备信息")
    completed_quantity: int = Field(..., ge=1, description="完成数量")
    quality_level: Optional[str] = Field(None, description="质量等级")
    measurement_data: Optional[Dict[str, Any]] = Field(None, description="测量数据")
    registration_data: Optional[Dict[str, Any]] = Field(None, description="登记数据")
    notes: Optional[str] = Field(None, description="备注")