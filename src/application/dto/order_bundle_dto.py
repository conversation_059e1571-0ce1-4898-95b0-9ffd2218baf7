from typing import List, Optional
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum


class BundleStatusEnum(str, Enum):
    """Bundle status enumeration for DTOs."""
    PLANNED = "planned"
    CUTTING = "cutting"
    CUT_COMPLETED = "cut_completed"
    SEWING = "sewing"
    SEW_COMPLETED = "sew_completed"
    QUALITY_CHECK = "quality_check"
    COMPLETED = "completed"
    REWORK = "rework"
    ON_HOLD = "on_hold"
    CANCELLED = "cancelled"


class OrderBundleBaseDTO(BaseModel):
    """Base order bundle DTO."""
    size: str = Field(..., min_length=1, max_length=20, description="尺码")
    quantity: int = Field(..., ge=0, description="扎件数")
    color: str = Field(..., min_length=1, max_length=50, description="颜色")
    notes: Optional[str] = Field(None, max_length=1000, description="备注")
    planned_start_date: Optional[datetime] = Field(None, description="计划开始时间")
    planned_end_date: Optional[datetime] = Field(None, description="计划完成时间")
    cutting_machine: Optional[str] = Field(None, max_length=50, description="裁剪机床")
    sewing_machine: Optional[str] = Field(None, max_length=50, description="缝制机床")


class OrderBundleCreateDTO(OrderBundleBaseDTO):
    """DTO for creating a new order bundle."""
    order_no: str = Field(..., min_length=1, max_length=100, description="订单号")
    order_part_no: str = Field(..., min_length=1, max_length=100, description="订单部位号")
    skc_no: str = Field(..., min_length=1, max_length=100, description="款号")
    bundle_sequence: int = Field(..., ge=1, description="扎序号(同一部位、同一尺码内)")
    cutter_user_id: Optional[int] = Field(None, description="裁剪工ID")
    sewer_user_id: Optional[int] = Field(None, description="缝制工ID")
    qc_user_id: Optional[int] = Field(None, description="质检员ID")


class OrderBundleUpdateDTO(BaseModel):
    """DTO for updating an order bundle."""
    quantity: Optional[int] = Field(None, ge=0, description="扎件数")
    status: Optional[BundleStatusEnum] = Field(None, description="扎状态")
    completed_quantity: Optional[int] = Field(None, ge=0, description="已完成件数")
    defective_quantity: Optional[int] = Field(None, ge=0, description="次品件数")
    rework_quantity: Optional[int] = Field(None, ge=0, description="返工件数")
    notes: Optional[str] = Field(None, max_length=1000, description="备注")
    planned_start_date: Optional[datetime] = Field(None, description="计划开始时间")
    planned_end_date: Optional[datetime] = Field(None, description="计划完成时间")
    cutting_machine: Optional[str] = Field(None, max_length=50, description="裁剪机床")
    sewing_machine: Optional[str] = Field(None, max_length=50, description="缝制机床")
    cutter_user_id: Optional[int] = Field(None, description="裁剪工ID")
    sewer_user_id: Optional[int] = Field(None, description="缝制工ID")
    qc_user_id: Optional[int] = Field(None, description="质检员ID")
    quality_level: Optional[str] = Field(None, max_length=20, description="质量等级(A/B/C)")
    quality_notes: Optional[str] = Field(None, max_length=1000, description="质量备注")


class OrderBundleResponseDTO(OrderBundleBaseDTO):
    """DTO for order bundle response."""
    id: int = Field(..., description="订单扎ID")
    factory_id: int = Field(..., description="工厂ID")
    order_no: str = Field(..., description="订单号")
    order_bundle_no: str = Field(..., description="订单扎号")
    order_part_no: str = Field(..., description="订单部位号")
    skc_no: str = Field(..., description="款号")
    bundle_sequence: int = Field(..., description="扎序号")
    status: BundleStatusEnum = Field(..., description="扎状态")
    completed_quantity: int = Field(..., description="已完成件数")
    defective_quantity: int = Field(..., description="次品件数")
    rework_quantity: int = Field(..., description="返工件数")
    progress_percentage: int = Field(..., description="完成百分比")
    
    # Timing information
    actual_start_date: Optional[datetime] = Field(None, description="实际开始时间")
    actual_end_date: Optional[datetime] = Field(None, description="实际完成时间")
    cut_start_date: Optional[datetime] = Field(None, description="裁剪开始时间")
    cut_end_date: Optional[datetime] = Field(None, description="裁剪完成时间")
    sew_start_date: Optional[datetime] = Field(None, description="缝制开始时间")
    sew_end_date: Optional[datetime] = Field(None, description="缝制完成时间")
    qc_start_date: Optional[datetime] = Field(None, description="质检开始时间")
    qc_end_date: Optional[datetime] = Field(None, description="质检完成时间")
    
    # Worker information
    cutter_user_id: Optional[int] = Field(None, description="裁剪工ID")
    sewer_user_id: Optional[int] = Field(None, description="缝制工ID")
    qc_user_id: Optional[int] = Field(None, description="质检员ID")
    cutter_name: Optional[str] = Field(None, description="裁剪工姓名")
    sewer_name: Optional[str] = Field(None, description="缝制工姓名")
    qc_user_name: Optional[str] = Field(None, description="质检员姓名")
    
    # Quality information
    quality_level: Optional[str] = Field(None, description="质量等级(A/B/C)")
    quality_notes: Optional[str] = Field(None, description="质量备注")
    good_quantity: Optional[int] = Field(None, description="良品数量")
    
    # Calculated fields
    processing_time: Optional[int] = Field(None, description="总加工时长(分钟)")
    cutting_time: Optional[int] = Field(None, description="裁剪时长(分钟)")
    sewing_time: Optional[int] = Field(None, description="缝制时长(分钟)")
    qc_time: Optional[int] = Field(None, description="质检时长(分钟)")
    
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    class Config:
        from_attributes = True


class OrderBundleListDTO(BaseModel):
    """DTO for order bundle list response."""
    order_bundles: List[OrderBundleResponseDTO] = Field(..., description="订单扎列表")
    total: int = Field(..., description="总数量")


class OrderBundleSearchDTO(BaseModel):
    """DTO for order bundle search criteria."""
    search_term: Optional[str] = Field(None, description="搜索关键词 (订单扎号、订单号、订单部位号等)")
    order_no: Optional[str] = Field(None, description="订单号")
    order_part_no: Optional[str] = Field(None, description="订单部位号")
    status: Optional[BundleStatusEnum] = Field(None, description="扎状态")
    size: Optional[str] = Field(None, description="尺码")
    color: Optional[str] = Field(None, description="颜色")
    cutter_user_id: Optional[int] = Field(None, description="裁剪工ID")
    sewer_user_id: Optional[int] = Field(None, description="缝制工ID")
    qc_user_id: Optional[int] = Field(None, description="质检员ID")
    quality_level: Optional[str] = Field(None, description="质量等级")
    start_date: Optional[datetime] = Field(None, description="开始日期")
    end_date: Optional[datetime] = Field(None, description="结束日期")
    date_field: Optional[str] = Field("created_at", description="日期字段 (created_at, planned_start_date, actual_start_date)")


class OrderBundleStatusUpdateDTO(BaseModel):
    """DTO for updating order bundle status."""
    status: BundleStatusEnum = Field(..., description="新状态")
    notes: Optional[str] = Field(None, description="状态变更备注")
    user_id: Optional[int] = Field(None, description="操作用户ID")
    machine: Optional[str] = Field(None, description="机床编号")


class OrderBundleProductionUpdateDTO(BaseModel):
    """DTO for updating order bundle production."""
    completed_quantity: Optional[int] = Field(None, ge=0, description="已完成件数")
    defective_quantity: Optional[int] = Field(None, ge=0, description="次品件数")
    rework_quantity: Optional[int] = Field(None, ge=0, description="返工件数")
    quality_level: Optional[str] = Field(None, description="质量等级")
    quality_notes: Optional[str] = Field(None, description="质量备注")
    notes: Optional[str] = Field(None, description="生产备注")


class BulkOrderBundleCreateDTO(BaseModel):
    """DTO for creating multiple order bundles."""
    order_no: str = Field(..., description="订单号")
    order_part_no: str = Field(..., description="订单部位号")
    order_bundles: List[OrderBundleCreateDTO] = Field(..., min_items=1, description="订单扎列表")


class OrderBundleOperationResultDTO(BaseModel):
    """DTO for order bundle operation results."""
    success: bool = Field(..., description="操作是否成功")
    message: str = Field(..., description="结果消息")
    order_bundle_id: Optional[int] = Field(None, description="订单扎ID")
    order_bundle_no: Optional[str] = Field(None, description="订单扎号")
    details: Optional[dict] = Field(None, description="详细信息")


class OrderBundleStatisticsDTO(BaseModel):
    """DTO for order bundle statistics."""
    total_order_bundles: int = Field(..., description="总订单扎数")
    total_quantity: int = Field(..., description="总件数")
    completed_quantity: int = Field(..., description="已完成件数")
    defective_quantity: int = Field(..., description="次品件数")
    rework_quantity: int = Field(..., description="返工件数")
    good_quantity: int = Field(..., description="良品件数")
    completion_percentage: float = Field(..., description="完成百分比")
    quality_rate: float = Field(..., description="质量合格率")
    status_breakdown: dict = Field(..., description="状态分解")
    planned_bundles: int = Field(..., description="计划中扎数")
    cutting_bundles: int = Field(..., description="裁剪中扎数")
    cut_completed_bundles: int = Field(..., description="裁剪完成扎数")
    sewing_bundles: int = Field(..., description="缝制中扎数")
    sew_completed_bundles: int = Field(..., description="缝制完成扎数")
    quality_check_bundles: int = Field(..., description="质检中扎数")
    completed_bundles: int = Field(..., description="已完成扎数")
    rework_bundles: int = Field(..., description="返工扎数")
    on_hold_bundles: int = Field(..., description="暂停扎数")
    cancelled_bundles: int = Field(..., description="已取消扎数")


class OrderBundleSummaryDTO(BaseModel):
    """DTO for order bundle summary in lists."""
    id: int = Field(..., description="订单扎ID")
    order_bundle_no: str = Field(..., description="订单扎号")
    order_no: str = Field(..., description="订单号")
    order_part_no: str = Field(..., description="订单部位号")
    size: str = Field(..., description="尺码")
    status: BundleStatusEnum = Field(..., description="扎状态")
    quantity: int = Field(..., description="扎件数")
    completed_quantity: int = Field(..., description="已完成件数")
    progress_percentage: int = Field(..., description="完成百分比")
    cutter_name: Optional[str] = Field(None, description="裁剪工姓名")
    sewer_name: Optional[str] = Field(None, description="缝制工姓名")
    qc_user_name: Optional[str] = Field(None, description="质检员姓名")
    created_at: datetime = Field(..., description="创建时间")