from typing import List, Optional
from pydantic import BaseModel, Field
from datetime import datetime


class SkillBaseDTO(BaseModel):
    """Base skill DTO."""
    code: str = Field(..., min_length=1, max_length=50, description="Unique skill code")
    name: str = Field(..., min_length=1, max_length=100, description="Skill name")
    description: Optional[str] = Field(None, max_length=1000, description="Skill description")
    category: Optional[str] = Field(None, max_length=50, description="Skill category")
    is_active: bool = Field(True, description="Whether the skill is active")


class SkillCreateDTO(SkillBaseDTO):
    """DTO for creating a new skill."""
    pass


class SkillUpdateDTO(BaseModel):
    """DTO for updating a skill."""
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="Skill name")
    description: Optional[str] = Field(None, max_length=1000, description="Skill description")
    category: Optional[str] = Field(None, max_length=50, description="Skill category")
    is_active: Optional[bool] = Field(None, description="Whether the skill is active")


class SkillResponseDTO(SkillBaseDTO):
    """DTO for skill response."""
    id: int = Field(..., description="Skill ID")
    
    class Config:
        from_attributes = True


class SkillListDTO(BaseModel):
    """DTO for skill list response."""
    skills: List[SkillResponseDTO] = Field(..., description="List of skills")
    total: int = Field(..., description="Total number of skills")


class UserFactorySkillDTO(BaseModel):
    """DTO for user factory skill relationship."""
    id: int = Field(..., description="User factory skill ID")
    skill: SkillResponseDTO = Field(..., description="Skill information")
    proficiency_level: str = Field(..., description="Proficiency level (BEGINNER, INTERMEDIATE, ADVANCED, EXPERT)")
    certified: bool = Field(..., description="Whether user is certified")
    certification_date: Optional[str] = Field(None, description="Certification date (ISO format)")
    certification_expires: Optional[str] = Field(None, description="Certification expiry date (ISO format)")
    notes: Optional[str] = Field(None, description="Additional notes")
    assigned_by: Optional[int] = Field(None, description="ID of user who assigned/updated this skill")
    assigned_at: str = Field(..., description="When skill was assigned (ISO format)")
    updated_at: str = Field(..., description="Last update time (ISO format)")
    
    class Config:
        from_attributes = True


class UserSkillsDTO(BaseModel):
    """DTO for user's skills in a factory."""
    user_factory_id: int = Field(..., description="User factory relationship ID")
    skills: List[UserFactorySkillDTO] = Field(..., description="List of user's skills")
    total_skills: int = Field(..., description="Total number of skills")
    certified_skills: int = Field(..., description="Number of certified skills")


class AssignSkillDTO(BaseModel):
    """DTO for assigning skill to user."""
    user_id: int = Field(..., description="User ID")
    skill_id: int = Field(..., description="Skill ID")
    proficiency_level: str = Field("BEGINNER", description="Initial proficiency level")
    notes: Optional[str] = Field(None, description="Additional notes")


class AssignSkillsDTO(BaseModel):
    """DTO for assigning multiple skills to user."""
    user_id: int = Field(..., description="User ID")
    skills: List[AssignSkillDTO] = Field(..., description="List of skills to assign")


class UpdateSkillProficiencyDTO(BaseModel):
    """DTO for updating skill proficiency."""
    user_factory_skill_id: int = Field(..., description="User factory skill ID")
    proficiency_level: str = Field(..., description="New proficiency level")
    notes: Optional[str] = Field(None, description="Updated notes")


class CertifySkillDTO(BaseModel):
    """DTO for certifying user in skill."""
    user_factory_skill_id: int = Field(..., description="User factory skill ID")
    certification_expires: Optional[str] = Field(None, description="Certification expiry date (ISO format)")
    notes: Optional[str] = Field(None, description="Certification notes")


class RemoveSkillDTO(BaseModel):
    """DTO for removing skill from user."""
    user_factory_skill_id: int = Field(..., description="User factory skill ID")
    reason: Optional[str] = Field(None, description="Reason for removal")


class SkillSearchDTO(BaseModel):
    """DTO for skill search criteria."""
    search_term: Optional[str] = Field(None, description="Search by code, name, or description")
    category: Optional[str] = Field(None, description="Filter by category")
    is_active: Optional[bool] = Field(None, description="Filter by active status")


class SkillOperationResultDTO(BaseModel):
    """DTO for skill operation results."""
    success: bool = Field(..., description="Whether operation succeeded")
    message: str = Field(..., description="Result message")
    skill_id: Optional[int] = Field(None, description="Skill ID affected")
    user_factory_skill_id: Optional[int] = Field(None, description="User factory skill ID affected")
    details: Optional[dict] = Field(None, description="Additional operation details")