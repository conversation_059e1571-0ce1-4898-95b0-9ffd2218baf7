from typing import List, Optional, TYPE_CHECKING
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum

if TYPE_CHECKING:
    from .bundle_dto import BundleResponseDTO


class PartTypeEnum(str, Enum):
    """Part type enumeration for DTOs."""
    FRONT_BODY = "front_body"
    BACK_BODY = "back_body"
    SLEEVE = "sleeve"
    COLLAR = "collar"
    POCKET = "pocket"
    CUFF = "cuff"
    WAISTBAND = "waistband"
    LEG = "leg"
    ZIPPER = "zipper"
    BUTTON_PLACKET = "button_placket"
    LINING = "lining"
    ACCESSORIES = "accessories"
    OTHER = "other"


class PartStatusEnum(str, Enum):
    """Part status enumeration for DTOs."""
    PLANNED = "planned"
    CUTTING = "cutting"
    SEWING = "sewing"
    QUALITY_CHECK = "quality_check"
    COMPLETED = "completed"
    ON_HOLD = "on_hold"
    CANCELLED = "cancelled"


class PartBaseDTO(BaseModel):
    """Base part DTO."""
    part_name: str = Field(..., min_length=1, max_length=100, description="部位名称")
    part_type: PartTypeEnum = Field(..., description="部位类型")
    color: str = Field(..., min_length=1, max_length=50, description="颜色")
    total_quantity: int = Field(..., ge=0, description="部位总件数")
    description: Optional[str] = Field(None, max_length=1000, description="部位描述")
    notes: Optional[str] = Field(None, max_length=1000, description="备注")
    planned_start_date: Optional[datetime] = Field(None, description="计划开始时间")
    planned_end_date: Optional[datetime] = Field(None, description="计划完成时间")
    machine_no: Optional[str] = Field(None, max_length=50, description="机床编号")
    process_route: Optional[str] = Field(None, max_length=200, description="工序路线")
    supervisor_user_id: Optional[int] = Field(None, description="负责人用户ID")


class PartCreateDTO(PartBaseDTO):
    """DTO for creating a new part."""
    order_no: str = Field(..., min_length=1, max_length=100, description="订单号")
    skc_no: str = Field(..., min_length=1, max_length=100, description="款号")


class PartUpdateDTO(BaseModel):
    """DTO for updating a part."""
    part_name: Optional[str] = Field(None, min_length=1, max_length=100, description="部位名称")
    total_quantity: Optional[int] = Field(None, ge=0, description="部位总件数")
    completed_quantity: Optional[int] = Field(None, ge=0, description="已完成件数")
    status: Optional[PartStatusEnum] = Field(None, description="部位状态")
    description: Optional[str] = Field(None, max_length=1000, description="部位描述")
    notes: Optional[str] = Field(None, max_length=1000, description="备注")
    planned_start_date: Optional[datetime] = Field(None, description="计划开始时间")
    planned_end_date: Optional[datetime] = Field(None, description="计划完成时间")
    actual_start_date: Optional[datetime] = Field(None, description="实际开始时间")
    actual_end_date: Optional[datetime] = Field(None, description="实际完成时间")
    machine_no: Optional[str] = Field(None, max_length=50, description="机床编号")
    process_route: Optional[str] = Field(None, max_length=200, description="工序路线")
    supervisor_user_id: Optional[int] = Field(None, description="负责人用户ID")


class PartResponseDTO(PartBaseDTO):
    """DTO for part response."""
    id: int = Field(..., description="部位ID")
    part_no: str = Field(..., description="部位号")
    order_no: str = Field(..., description="订单号")
    skc_no: str = Field(..., description="款号")
    part_sequence: int = Field(..., description="部位序号")
    completed_quantity: int = Field(..., description="已完成件数")
    status: PartStatusEnum = Field(..., description="部位状态")
    progress_percentage: int = Field(..., description="完成百分比")
    actual_start_date: Optional[datetime] = Field(None, description="实际开始时间")
    actual_end_date: Optional[datetime] = Field(None, description="实际完成时间")
    supervisor_name: Optional[str] = Field(None, description="负责人姓名")
    bundles_count: int = Field(0, description="扎数量")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    class Config:
        from_attributes = True


class PartWithBundlesDTO(PartResponseDTO):
    """DTO for part with its bundles."""
    bundles: List["BundleResponseDTO"] = Field(..., description="扎列表")


class PartListDTO(BaseModel):
    """DTO for part list response."""
    parts: List[PartResponseDTO] = Field(..., description="部位列表")
    total: int = Field(..., description="总数量")


class PartSearchDTO(BaseModel):
    """DTO for part search criteria."""
    search_term: Optional[str] = Field(None, description="搜索关键词 (部位号、部位名称等)")
    order_no: Optional[str] = Field(None, description="订单号")
    part_type: Optional[PartTypeEnum] = Field(None, description="部位类型")
    status: Optional[PartStatusEnum] = Field(None, description="部位状态")
    supervisor_user_id: Optional[int] = Field(None, description="负责人用户ID")
    color: Optional[str] = Field(None, description="颜色")


class PartOperationResultDTO(BaseModel):
    """DTO for part operation results."""
    success: bool = Field(..., description="操作是否成功")
    message: str = Field(..., description="结果消息")
    part_id: Optional[int] = Field(None, description="部位ID")
    part_no: Optional[str] = Field(None, description="部位号")
    details: Optional[dict] = Field(None, description="详细信息")


class PartStatusUpdateDTO(BaseModel):
    """DTO for updating part status."""
    status: PartStatusEnum = Field(..., description="新状态")
    notes: Optional[str] = Field(None, description="状态变更备注")


class PartProductionUpdateDTO(BaseModel):
    """DTO for updating part production."""
    completed_quantity: int = Field(..., ge=0, description="已完成数量")
    notes: Optional[str] = Field(None, description="生产备注")


class BulkPartCreateDTO(BaseModel):
    """DTO for creating multiple parts."""
    order_no: str = Field(..., description="订单号")
    parts: List[PartCreateDTO] = Field(..., min_length=1, description="部位列表")
