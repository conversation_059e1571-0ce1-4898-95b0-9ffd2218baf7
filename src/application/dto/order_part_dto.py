from typing import List, Optional, TYPE_CHECKING
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum

if TYPE_CHECKING:
    from .order_bundle_dto import OrderBundleResponseDTO


class PartTypeEnum(str, Enum):
    """Part type enumeration for DTOs."""
    FRONT_BODY = "front_body"
    BACK_BODY = "back_body"
    SLEEVE = "sleeve"
    COLLAR = "collar"
    POCKET = "pocket"
    CUFF = "cuff"
    WAISTBAND = "waistband"
    LEG = "leg"
    ZIPPER = "zipper"
    BUTTON_PLACKET = "button_placket"
    LINING = "lining"
    ACCESSORIES = "accessories"
    OTHER = "other"


class PartStatusEnum(str, Enum):
    """Part status enumeration for DTOs."""
    PLANNED = "planned"
    CUTTING = "cutting"
    SEWING = "sewing"
    QUALITY_CHECK = "quality_check"
    COMPLETED = "completed"
    ON_HOLD = "on_hold"
    CANCELLED = "cancelled"


class OrderPartBaseDTO(BaseModel):
    """Base order part DTO."""
    part_name: str = Field(..., min_length=1, max_length=100, description="部位名称")
    part_type: PartTypeEnum = Field(..., description="部位类型")
    color: str = Field(..., min_length=1, max_length=50, description="颜色")
    total_quantity: int = Field(..., ge=0, description="部位总件数")
    description: Optional[str] = Field(None, max_length=1000, description="部位描述")
    notes: Optional[str] = Field(None, max_length=1000, description="备注")
    planned_start_date: Optional[datetime] = Field(None, description="计划开始时间")
    planned_end_date: Optional[datetime] = Field(None, description="计划完成时间")
    machine_no: Optional[str] = Field(None, max_length=50, description="机床编号")
    process_route: Optional[str] = Field(None, max_length=200, description="工序路线")
    supervisor_user_id: Optional[int] = Field(None, description="负责人用户ID")


class OrderPartCreateDTO(OrderPartBaseDTO):
    """DTO for creating a new order part."""
    order_no: str = Field(..., min_length=1, max_length=100, description="订单号")
    skc_no: str = Field(..., min_length=1, max_length=100, description="款号")
    part_sequence: int = Field(..., ge=1, description="部位序号(同一订单内)")
    order_bundles: Optional[List["OrderBundleCreateDTO"]] = Field(None, description="订单扎配置列表")


class OrderPartUpdateDTO(BaseModel):
    """DTO for updating an order part."""
    part_name: Optional[str] = Field(None, min_length=1, max_length=100, description="部位名称")
    color: Optional[str] = Field(None, min_length=1, max_length=50, description="颜色")
    total_quantity: Optional[int] = Field(None, ge=0, description="部位总件数")
    status: Optional[PartStatusEnum] = Field(None, description="部位状态")
    completed_quantity: Optional[int] = Field(None, ge=0, description="已完成件数")
    description: Optional[str] = Field(None, max_length=1000, description="部位描述")
    notes: Optional[str] = Field(None, max_length=1000, description="备注")
    planned_start_date: Optional[datetime] = Field(None, description="计划开始时间")
    planned_end_date: Optional[datetime] = Field(None, description="计划完成时间")
    actual_start_date: Optional[datetime] = Field(None, description="实际开始时间")
    actual_end_date: Optional[datetime] = Field(None, description="实际完成时间")
    machine_no: Optional[str] = Field(None, max_length=50, description="机床编号")
    process_route: Optional[str] = Field(None, max_length=200, description="工序路线")
    supervisor_user_id: Optional[int] = Field(None, description="负责人用户ID")


class OrderPartResponseDTO(OrderPartBaseDTO):
    """DTO for order part response."""
    id: int = Field(..., description="订单部位ID")
    factory_id: int = Field(..., description="工厂ID")
    order_no: str = Field(..., description="订单号")
    order_part_no: str = Field(..., description="订单部位号")
    skc_no: str = Field(..., description="款号")
    part_sequence: int = Field(..., description="部位序号")
    status: PartStatusEnum = Field(..., description="部位状态")
    completed_quantity: int = Field(..., description="已完成件数")
    progress_percentage: int = Field(..., description="完成百分比")
    actual_start_date: Optional[datetime] = Field(None, description="实际开始时间")
    actual_end_date: Optional[datetime] = Field(None, description="实际完成时间")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    supervisor_name: Optional[str] = Field(None, description="负责人姓名")
    
    class Config:
        from_attributes = True


class OrderPartWithBundlesDTO(OrderPartResponseDTO):
    """DTO for order part with its order bundles."""
    order_bundles: List["OrderBundleResponseDTO"] = Field(..., description="订单扎列表")


class OrderPartListDTO(BaseModel):
    """DTO for order part list response."""
    order_parts: List[OrderPartResponseDTO] = Field(..., description="订单部位列表")
    total: int = Field(..., description="总数量")


class OrderPartSearchDTO(BaseModel):
    """DTO for order part search criteria."""
    search_term: Optional[str] = Field(None, description="搜索关键词 (订单部位号、订单号、款号等)")
    order_no: Optional[str] = Field(None, description="订单号")
    status: Optional[PartStatusEnum] = Field(None, description="部位状态")
    part_type: Optional[PartTypeEnum] = Field(None, description="部位类型")
    supervisor_user_id: Optional[int] = Field(None, description="负责人用户ID")
    color: Optional[str] = Field(None, description="颜色")
    start_date: Optional[datetime] = Field(None, description="开始日期")
    end_date: Optional[datetime] = Field(None, description="结束日期")
    date_field: Optional[str] = Field("created_at", description="日期字段 (created_at, planned_start_date, actual_start_date)")


class OrderPartStatusUpdateDTO(BaseModel):
    """DTO for updating order part status."""
    status: PartStatusEnum = Field(..., description="新状态")
    notes: Optional[str] = Field(None, description="状态变更备注")


class BulkOrderPartCreateDTO(BaseModel):
    """DTO for creating multiple order parts."""
    order_no: str = Field(..., description="订单号")
    order_parts: List[OrderPartCreateDTO] = Field(..., min_items=1, description="订单部位列表")


class OrderPartOperationResultDTO(BaseModel):
    """DTO for order part operation results."""
    success: bool = Field(..., description="操作是否成功")
    message: str = Field(..., description="结果消息")
    order_part_id: Optional[int] = Field(None, description="订单部位ID")
    order_part_no: Optional[str] = Field(None, description="订单部位号")
    details: Optional[dict] = Field(None, description="详细信息")


class OrderPartStatisticsDTO(BaseModel):
    """DTO for order part statistics."""
    total_order_parts: int = Field(..., description="总订单部位数")
    total_quantity: int = Field(..., description="总件数")
    completed_quantity: int = Field(..., description="已完成件数")
    completion_percentage: float = Field(..., description="完成百分比")
    status_breakdown: dict = Field(..., description="状态分解")
    planned_parts: int = Field(..., description="计划中部位数")
    cutting_parts: int = Field(..., description="裁剪中部位数")
    sewing_parts: int = Field(..., description="缝制中部位数")
    quality_check_parts: int = Field(..., description="质检中部位数")
    completed_parts: int = Field(..., description="已完成部位数")
    on_hold_parts: int = Field(..., description="暂停部位数")
    cancelled_parts: int = Field(..., description="已取消部位数")


class OrderPartSummaryDTO(BaseModel):
    """DTO for order part summary in lists."""
    id: int = Field(..., description="订单部位ID")
    order_part_no: str = Field(..., description="订单部位号")
    order_no: str = Field(..., description="订单号")
    part_name: str = Field(..., description="部位名称")
    part_type: PartTypeEnum = Field(..., description="部位类型")
    status: PartStatusEnum = Field(..., description="部位状态")
    total_quantity: int = Field(..., description="总件数")
    completed_quantity: int = Field(..., description="已完成件数")
    progress_percentage: int = Field(..., description="完成百分比")
    supervisor_name: Optional[str] = Field(None, description="负责人姓名")
    created_at: datetime = Field(..., description="创建时间")


# Import order_bundle_dto to resolve forward references
from .order_bundle_dto import OrderBundleCreateDTO, OrderBundleResponseDTO