from typing import Optional, List
from pydantic import BaseModel, Field


class PermissionBaseDTO(BaseModel):
    """Base permission DTO."""
    code: str = Field(..., min_length=1, max_length=100, description="Unique permission code")
    name: str = Field(..., min_length=1, max_length=200, description="Permission name")
    parent_id: Optional[int] = Field(None, description="Parent permission ID for hierarchical structure")


class PermissionCreateDTO(PermissionBaseDTO):
    """DTO for creating a new permission."""
    pass


class PermissionUpdateDTO(BaseModel):
    """DTO for updating a permission."""
    name: Optional[str] = Field(None, min_length=1, max_length=200, description="Permission name")
    parent_id: Optional[int] = Field(None, description="Parent permission ID")


class PermissionResponseDTO(PermissionBaseDTO):
    """DTO for permission response."""
    id: int = Field(..., description="Permission ID")
    
    class Config:
        from_attributes = True


class PermissionTreeDTO(BaseModel):
    """DTO for hierarchical permission tree."""
    id: int = Field(..., description="Permission ID")
    code: str = Field(..., description="Permission code")
    name: str = Field(..., description="Permission name")
    parent_id: Optional[int] = Field(None, description="Parent permission ID")
    children: List["PermissionTreeDTO"] = Field(default_factory=list, description="Child permissions")
    
    class Config:
        from_attributes = True


class PermissionListDTO(BaseModel):
    """DTO for permission list response."""
    permissions: List[PermissionResponseDTO] = Field(..., description="List of permissions")
    total: int = Field(..., description="Total number of permissions")


# Update forward references
PermissionTreeDTO.model_rebuild()