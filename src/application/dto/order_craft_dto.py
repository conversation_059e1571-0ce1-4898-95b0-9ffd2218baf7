from typing import List, Optional
from pydantic import BaseModel, Field
from datetime import datetime
from decimal import Decimal


class OrderCraftRouteCreateDTO(BaseModel):
    """DTO for creating order craft routes."""
    skill_code: str = Field(..., min_length=1, max_length=50, description="技能代码")
    name: Optional[str] = Field(None, max_length=100, description="路线名称")
    code: Optional[str] = Field(None, max_length=50, description="路线代码")
    order: int = Field(..., ge=0, description="工序顺序")
    measurement_types: Optional[List[str]] = Field(None, description="测量类型列表")
    registration_types: Optional[List[str]] = Field(None, description="登记类型列表")
    is_required: bool = Field(True, description="是否必需")
    estimated_duration_minutes: Optional[int] = Field(None, ge=0, description="预计耗时(分钟)")
    assigned_user_id: Optional[int] = Field(None, description="指定用户ID")
    price: Optional[Decimal] = Field(None, ge=0, description="单价")
    total_cost: Optional[Decimal] = Field(None, ge=0, description="总成本")
    notes: Optional[str] = Field(None, max_length=1000, description="备注")


class OrderCraftCreateDTO(BaseModel):
    """DTO for creating order crafts."""
    craft_code: str = Field(..., min_length=1, max_length=50, description="工艺代码")
    craft_name: Optional[str] = Field(None, max_length=100, description="工艺名称")
    order: int = Field(..., ge=0, description="工艺顺序")
    is_required: bool = Field(True, description="是否必需")
    estimated_duration_hours: Optional[int] = Field(None, ge=0, description="预计耗时(小时)")
    notes: Optional[str] = Field(None, max_length=1000, description="备注")
    order_craft_routes: List[OrderCraftRouteCreateDTO] = Field(default_factory=list, description="工艺路线列表")


class OrderCraftRouteResponseDTO(BaseModel):
    """DTO for order craft route response."""
    id: int = Field(..., description="ID")
    order_craft_id: int = Field(..., description="订单工艺ID")
    skill_code: str = Field(..., description="技能代码")
    skill_name: Optional[str] = Field(None, description="技能名称")
    name: Optional[str] = Field(None, description="路线名称")
    code: Optional[str] = Field(None, description="路线代码")
    order: int = Field(..., description="工序顺序")
    measurement_types: Optional[List[str]] = Field(None, description="测量类型列表")
    registration_types: Optional[List[str]] = Field(None, description="登记类型列表")
    is_required: bool = Field(..., description="是否必需")
    is_active: bool = Field(..., description="是否活跃")
    status: str = Field(..., description="状态")
    started_at: Optional[datetime] = Field(None, description="开始时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")
    assigned_user_id: Optional[int] = Field(None, description="指定用户ID")
    assigned_user_name: Optional[str] = Field(None, description="指定用户姓名")
    estimated_duration_minutes: Optional[int] = Field(None, description="预计耗时(分钟)")
    actual_duration_minutes: Optional[int] = Field(None, description="实际耗时(分钟)")
    price: Optional[Decimal] = Field(None, description="单价")
    total_cost: Optional[Decimal] = Field(None, description="总成本")
    quality_score: Optional[int] = Field(None, description="质量分数")
    notes: Optional[str] = Field(None, description="备注")
    completion_notes: Optional[str] = Field(None, description="完成备注")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    class Config:
        from_attributes = True


class OrderCraftResponseDTO(BaseModel):
    """DTO for order craft response."""
    id: int = Field(..., description="ID")
    order_no: str = Field(..., description="订单号")
    craft_code: str = Field(..., description="工艺代码")
    craft_name: Optional[str] = Field(None, description="工艺名称")
    order: int = Field(..., description="工艺顺序")
    is_required: bool = Field(..., description="是否必需")
    is_active: bool = Field(..., description="是否活跃")
    status: str = Field(..., description="状态")
    started_at: Optional[datetime] = Field(None, description="开始时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")
    estimated_duration_hours: Optional[int] = Field(None, description="预计耗时(小时)")
    actual_duration_hours: Optional[int] = Field(None, description="实际耗时(小时)")
    completion_percentage: float = Field(0.0, description="完成百分比")
    notes: Optional[str] = Field(None, description="备注")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    order_craft_routes: List[OrderCraftRouteResponseDTO] = Field(default_factory=list, description="工艺路线列表")
    
    class Config:
        from_attributes = True


class OrderCraftUpdateDTO(BaseModel):
    """DTO for updating order crafts."""
    order: Optional[int] = Field(None, ge=0, description="工艺顺序")
    is_required: Optional[bool] = Field(None, description="是否必需")
    is_active: Optional[bool] = Field(None, description="是否活跃")
    estimated_duration_hours: Optional[int] = Field(None, ge=0, description="预计耗时(小时)")
    notes: Optional[str] = Field(None, max_length=1000, description="备注")


class OrderCraftRouteUpdateDTO(BaseModel):
    """DTO for updating order craft routes."""
    order: Optional[int] = Field(None, ge=0, description="工序顺序")
    is_required: Optional[bool] = Field(None, description="是否必需")
    is_active: Optional[bool] = Field(None, description="是否活跃")
    assigned_user_id: Optional[int] = Field(None, description="指定用户ID")
    estimated_duration_minutes: Optional[int] = Field(None, ge=0, description="预计耗时(分钟)")
    quality_score: Optional[int] = Field(None, ge=0, le=100, description="质量分数")
    notes: Optional[str] = Field(None, max_length=1000, description="备注")
    completion_notes: Optional[str] = Field(None, max_length=1000, description="完成备注")


class OrderCraftStatusUpdateDTO(BaseModel):
    """DTO for updating order craft status."""
    status: str = Field(..., description="新状态 (pending, in_progress, completed, skipped)")
    notes: Optional[str] = Field(None, description="状态变更备注")


class OrderCraftRouteStatusUpdateDTO(BaseModel):
    """DTO for updating order craft route status."""
    status: str = Field(..., description="新状态 (pending, in_progress, completed, skipped)")
    quality_score: Optional[int] = Field(None, ge=0, le=100, description="质量分数")
    completion_notes: Optional[str] = Field(None, description="完成备注")


class OrderCraftWorkflowDTO(BaseModel):
    """DTO for order craft workflow configuration."""
    order_crafts: List[OrderCraftCreateDTO] = Field(..., min_length=1, description="订单工艺配置列表")


class OrderCraftOperationResultDTO(BaseModel):
    """DTO for order craft operation results."""
    success: bool = Field(..., description="操作是否成功")
    message: str = Field(..., description="结果消息")
    order_craft_id: Optional[int] = Field(None, description="订单工艺ID")
    order_craft_route_id: Optional[int] = Field(None, description="订单工艺路线ID")
    details: Optional[dict] = Field(None, description="详细信息")


class OrderCraftStatisticsDTO(BaseModel):
    """DTO for order craft statistics."""
    total_order_crafts: int = Field(..., description="总订单工艺数")
    pending_crafts: int = Field(..., description="待处理工艺数")
    in_progress_crafts: int = Field(..., description="进行中工艺数")
    completed_crafts: int = Field(..., description="已完成工艺数")
    skipped_crafts: int = Field(..., description="跳过工艺数")
    active_crafts: int = Field(..., description="活跃工艺数")
    inactive_crafts: int = Field(..., description="非活跃工艺数")
    status_breakdown: dict = Field(..., description="状态分解")
    active_breakdown: dict = Field(..., description="活跃状态分解")