from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, Field
from enum import Enum


class UserFactoryStatusEnum(str, Enum):
    """Status enum for API responses."""
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"
    SUSPENDED = "suspended"
    RESIGNED = "resigned"


class UserFactoryRoleEnum(str, Enum):
    """Role enum for API responses."""
    WORKER = "worker"
    SUPERVISOR = "supervisor"
    MANAGER = "manager"
    ADMIN = "admin"


class FactoryJoinRequestDTO(BaseModel):
    """DTO for requesting to join a factory."""
    factory_id: int = Field(..., description="Factory ID to join")
    department_id: Optional[int] = Field(None, description="Optional department ID")
    message: Optional[str] = Field(None, max_length=500, description="Optional message to manager")


class FactoryJoinApprovalDTO(BaseModel):
    """DTO for approving/rejecting factory join requests."""
    user_factory_id: int = Field(..., description="UserFactory record ID")
    action: str = Field(..., description="approve or reject")
    employee_id: Optional[str] = Field(None, max_length=50, description="Employee ID (if approved)")
    position: Optional[str] = Field(None, max_length=100, description="Position title (if approved)")
    department_id: Optional[int] = Field(None, description="Department assignment (if approved)")
    role: Optional[UserFactoryRoleEnum] = Field(UserFactoryRoleEnum.WORKER, description="User role in factory")
    reason: Optional[str] = Field(None, max_length=500, description="Rejection reason (if rejected)")
    start_date: Optional[datetime] = Field(None, description="Start date (if approved)")


class UserFactoryResponseDTO(BaseModel):
    """Response DTO for UserFactory relationships."""
    id: int
    user_id: int
    factory_id: int
    department_id: Optional[int]
    status: UserFactoryStatusEnum
    role: UserFactoryRoleEnum
    employee_id: Optional[str]
    position: Optional[str]
    start_date: Optional[datetime]
    end_date: Optional[datetime]
    requested_at: datetime
    approved_by: Optional[int]
    approved_at: Optional[datetime]
    rejected_reason: Optional[str]
    
    # Related objects
    factory_name: Optional[str] = None
    department_name: Optional[str] = None
    user_name: Optional[str] = None
    approver_name: Optional[str] = None
    
    class Config:
        from_attributes = True


class FactoryMemberDTO(BaseModel):
    """DTO for factory members."""
    user_id: int
    username: str
    full_name: Optional[str]
    email: str
    phone: Optional[str]
    employee_id: Optional[str]
    position: Optional[str]
    role: UserFactoryRoleEnum
    status: UserFactoryStatusEnum
    department_id: Optional[int]
    department_name: Optional[str]
    start_date: Optional[datetime]


class PendingRequestDTO(BaseModel):
    """DTO for pending factory join requests."""
    id: int
    user_id: int
    username: str
    full_name: Optional[str]
    email: str
    phone: Optional[str]
    requested_at: datetime
    message: Optional[str] = None
    department_id: Optional[int]
    department_name: Optional[str]


class FactoryDetailDTO(BaseModel):
    """Detailed factory information with members."""
    id: int
    name: str
    code: str
    address: Optional[str]
    phone: Optional[str]
    email: Optional[str]
    manager_name: Optional[str]
    is_active: bool
    description: Optional[str]
    created_at: datetime
    updated_at: datetime
    
    # Statistics
    total_members: int = 0
    active_members: int = 0
    pending_requests: int = 0
    
    # Members (if requested)
    members: Optional[List[FactoryMemberDTO]] = None
    pending_requests_list: Optional[List[PendingRequestDTO]] = None
    
    class Config:
        from_attributes = True


class UserFactoryListDTO(BaseModel):
    """DTO for user's factory list."""
    factories: List[UserFactoryResponseDTO]
    total_count: int


class MyFactoriesDTO(BaseModel):
    """DTO for current user's factories."""
    active_factories: List[UserFactoryResponseDTO]
    pending_requests: List[UserFactoryResponseDTO]
    managed_factories: List[UserFactoryResponseDTO]