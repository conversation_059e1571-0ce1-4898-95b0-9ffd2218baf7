from datetime import datetime
from typing import Optional
from pydantic import BaseModel, EmailStr, Field


class UserCreateDTO(BaseModel):
    username: str = Field(..., min_length=3, max_length=50)
    email: EmailStr
    password: str = Field(..., min_length=8)
    full_name: Optional[str] = Field(None, max_length=100)


class UserUpdateDTO(BaseModel):
    full_name: Optional[str] = Field(None, max_length=100)
    avatar_url: Optional[str] = None
    is_active: Optional[bool] = None


class UserResponseDTO(BaseModel):
    id: int
    username: str
    email: str
    full_name: Optional[str]
    is_active: bool
    is_superuser: bool
    avatar_url: Optional[str]
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class UserLoginDTO(BaseModel):
    username: str
    password: str


class FactoryContextDTO(BaseModel):
    """Factory context information for user session."""
    factory_id: Optional[int] = None
    factory_name: Optional[str] = None
    department_id: Optional[int] = None
    role: Optional[str] = None
    is_manager: bool = False


class TokenResponseDTO(BaseModel):
    access_token: str
    token_type: str = "bearer"
    session_id: Optional[str] = None
    factory_context: Optional[FactoryContextDTO] = None