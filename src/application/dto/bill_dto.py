from typing import Optional, List, Dict, Any
from datetime import date, datetime
from decimal import Decimal
from pydantic import BaseModel, Field
from src.domain.entities.daily_worker_bill import BillStatus, PaymentMethod


class BillBaseDTO(BaseModel):
    """Base DTO for bill operations."""
    
    factory_id: int = Field(..., description="Factory ID")
    worker_user_id: int = Field(..., description="Worker user ID")
    bill_date: date = Field(..., description="Bill date")
    total_completed_quantity: int = Field(default=0, description="Total completed quantity")
    total_work_instances: int = Field(default=0, description="Total work instances")
    total_work_duration_minutes: int = Field(default=0, description="Total work duration in minutes")
    base_amount: Decimal = Field(default=Decimal('0.00'), description="Base amount")
    bonus_amount: Decimal = Field(default=Decimal('0.00'), description="Bonus amount")
    deduction_amount: Decimal = Field(default=Decimal('0.00'), description="Deduction amount")
    total_amount: Decimal = Field(default=Decimal('0.00'), description="Total amount")
    quality_score_average: Optional[Decimal] = Field(None, description="Average quality score")
    defect_count: int = Field(default=0, description="Defect count")
    rework_count: int = Field(default=0, description="Rework count")
    breakdown_by_granularity: Optional[Dict[str, Any]] = Field(None, description="Breakdown by granularity")
    breakdown_by_craft_route: Optional[Dict[str, Any]] = Field(None, description="Breakdown by craft route")
    breakdown_by_order: Optional[Dict[str, Any]] = Field(None, description="Breakdown by order")
    notes: Optional[str] = Field(None, description="Notes")


class CreateBillRequestDTO(BillBaseDTO):
    """DTO for creating a new bill."""
    pass


class UpdateBillRequestDTO(BaseModel):
    """DTO for updating a bill."""
    
    total_completed_quantity: Optional[int] = Field(None, description="Total completed quantity")
    total_work_instances: Optional[int] = Field(None, description="Total work instances")
    total_work_duration_minutes: Optional[int] = Field(None, description="Total work duration in minutes")
    base_amount: Optional[Decimal] = Field(None, description="Base amount")
    bonus_amount: Optional[Decimal] = Field(None, description="Bonus amount")
    deduction_amount: Optional[Decimal] = Field(None, description="Deduction amount")
    total_amount: Optional[Decimal] = Field(None, description="Total amount")
    quality_score_average: Optional[Decimal] = Field(None, description="Average quality score")
    defect_count: Optional[int] = Field(None, description="Defect count")
    rework_count: Optional[int] = Field(None, description="Rework count")
    breakdown_by_granularity: Optional[Dict[str, Any]] = Field(None, description="Breakdown by granularity")
    breakdown_by_craft_route: Optional[Dict[str, Any]] = Field(None, description="Breakdown by craft route")
    breakdown_by_order: Optional[Dict[str, Any]] = Field(None, description="Breakdown by order")
    notes: Optional[str] = Field(None, description="Notes")


class BillSubmitForReviewRequestDTO(BaseModel):
    """DTO for submitting a bill for review."""
    pass


class BillApprovalRequestDTO(BaseModel):
    """DTO for approving a bill."""
    
    review_notes: Optional[str] = Field(None, description="Review notes")


class BillRejectionRequestDTO(BaseModel):
    """DTO for rejecting a bill."""
    
    rejection_reason: str = Field(..., description="Rejection reason")


class BillPaymentRequestDTO(BaseModel):
    """DTO for marking a bill as paid."""
    
    payment_method: PaymentMethod = Field(..., description="Payment method")
    payment_reference: Optional[str] = Field(None, description="Payment reference")
    payment_notes: Optional[str] = Field(None, description="Payment notes")


class BillCancellationRequestDTO(BaseModel):
    """DTO for cancelling a bill."""
    
    cancellation_reason: str = Field(..., description="Cancellation reason")


class InstanceDisputeRequestDTO(BaseModel):
    """DTO for disputing a bill instance."""
    
    dispute_reason: str = Field(..., description="Dispute reason")


class InstanceDisputeResolutionRequestDTO(BaseModel):
    """DTO for resolving an instance dispute."""
    
    resolution_notes: str = Field(..., description="Resolution notes")
    include_in_settlement: bool = Field(..., description="Whether to include in settlement")


class UserInfoDTO(BaseModel):
    """DTO for user information in bill responses."""
    
    id: int
    username: str
    full_name: Optional[str] = None
    
    class Config:
        from_attributes = True


class FactoryInfoDTO(BaseModel):
    """DTO for factory information in bill responses."""
    
    id: int
    name: str
    code: str
    
    class Config:
        from_attributes = True


class BillResponseDTO(BaseModel):
    """DTO for bill response."""
    
    id: int
    factory_id: int
    worker_user_id: int
    bill_date: date
    bill_no: str
    total_completed_quantity: int
    total_work_instances: int
    total_work_duration_minutes: int
    base_amount: Decimal
    bonus_amount: Decimal
    deduction_amount: Decimal
    total_amount: Decimal
    status: BillStatus
    reviewed_by_user_id: Optional[int] = None
    reviewed_at: Optional[datetime] = None
    review_notes: Optional[str] = None
    payment_method: Optional[PaymentMethod] = None
    paid_by_user_id: Optional[int] = None
    paid_at: Optional[datetime] = None
    payment_reference: Optional[str] = None
    payment_notes: Optional[str] = None
    quality_score_average: Optional[Decimal] = None
    defect_count: int
    rework_count: int
    breakdown_by_granularity: Optional[Dict[str, Any]] = None
    breakdown_by_craft_route: Optional[Dict[str, Any]] = None
    breakdown_by_order: Optional[Dict[str, Any]] = None
    is_auto_generated: bool
    generated_at: Optional[datetime] = None
    notes: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    
    # Related entities
    worker_user: Optional[UserInfoDTO] = None
    factory: Optional[FactoryInfoDTO] = None
    reviewed_by_user: Optional[UserInfoDTO] = None
    paid_by_user: Optional[UserInfoDTO] = None
    
    class Config:
        from_attributes = True


class BillListResponseDTO(BaseModel):
    """DTO for bill list response."""
    
    bills: List[BillResponseDTO]
    total: int
    skip: int
    limit: int


class BillOperationResponseDTO(BaseModel):
    """DTO for bill operation response."""
    
    success: bool
    message: str
    bill: Optional[BillResponseDTO] = None


class BillSettlementSummaryDTO(BaseModel):
    """DTO for bill settlement summary."""
    
    class BillSummaryDTO(BaseModel):
        pending_count: int
        approved_count: int
        paid_count: int
        pending_amount: Decimal
        approved_amount: Decimal
        paid_amount: Decimal
    
    class InstanceSummaryDTO(BaseModel):
        pending_count: int
        included_count: int
        settled_count: int
        disputed_count: int
    
    bills: BillSummaryDTO
    instances: InstanceSummaryDTO


class BillSearchRequestDTO(BaseModel):
    """DTO for bill search request."""
    
    worker_user_id: Optional[int] = Field(None, description="Worker user ID")
    status: Optional[BillStatus] = Field(None, description="Bill status")
    bill_date: Optional[date] = Field(None, description="Bill date")
    start_date: Optional[date] = Field(None, description="Start date for date range search")
    end_date: Optional[date] = Field(None, description="End date for date range search")
    skip: int = Field(default=0, ge=0, description="Number of records to skip")
    limit: int = Field(default=100, ge=1, le=1000, description="Number of records to return")