from abc import ABC, abstractmethod
from typing import List, Optional
from src.domain.entities.factory import Factory


class FactoryRepositoryInterface(ABC):
    """Abstract interface for factory repository."""
    
    @abstractmethod
    async def create(self, factory: Factory) -> Factory:
        """Create a new factory."""
        pass
    
    @abstractmethod
    async def get_by_id(self, factory_id: int) -> Optional[Factory]:
        """Get factory by ID."""
        pass
    
    @abstractmethod
    async def get_by_code(self, code: str) -> Optional[Factory]:
        """Get factory by code."""
        pass
    
    @abstractmethod
    async def get_by_name(self, name: str) -> Optional[Factory]:
        """Get factory by name."""
        pass
    
    @abstractmethod
    async def update(self, factory: Factory) -> Factory:
        """Update an existing factory."""
        pass
    
    @abstractmethod
    async def delete(self, factory_id: int) -> bool:
        """Delete a factory by ID."""
        pass
    
    @abstractmethod
    async def get_all(self, skip: int = 0, limit: int = 100, is_active: Optional[bool] = None) -> List[Factory]:
        """Get all factories with pagination and optional filtering."""
        pass
    
    @abstractmethod
    async def get_with_departments(self, factory_id: int) -> Optional[Factory]:
        """Get factory with its departments."""
        pass