from abc import ABC, abstractmethod
from typing import List, Optional
from datetime import datetime
from src.domain.entities.order import Order, OrderStatus


class OrderRepositoryInterface(ABC):
    """Interface for order repository operations."""
    
    @abstractmethod
    async def create(self, order: Order) -> Order:
        """Create a new order."""
        pass
    
    @abstractmethod
    async def get_by_id(self, order_id: int) -> Optional[Order]:
        """Get order by ID."""
        pass
    
    @abstractmethod
    async def get_by_order_no(self, order_no: str) -> Optional[Order]:
        """Get order by order number."""
        pass
    
    @abstractmethod
    async def get_by_order_no_and_factory(self, order_no: str, factory_id: int, session=None) -> Optional[Order]:
        """Get order by order number and factory ID."""
        pass
    
    @abstractmethod
    async def get_by_external_order_no(self, external_order_no: str) -> List[Order]:
        """Get orders by external order number."""
        pass
    
    @abstractmethod
    async def get_by_skc_no(self, skc_no: str) -> List[Order]:
        """Get orders by SKC number."""
        pass
    
    @abstractmethod
    async def get_all(self, skip: int = 0, limit: int = 100) -> List[Order]:
        """Get all orders."""
        pass
    
    @abstractmethod
    async def get_by_status(self, status: OrderStatus) -> List[Order]:
        """Get orders by status."""
        pass
    
    @abstractmethod
    async def get_by_owner(self, owner_user_id: int) -> List[Order]:
        """Get orders by owner user ID."""
        pass
    
    @abstractmethod
    async def get_by_date_range(
        self, 
        start_date: datetime, 
        end_date: datetime,
        date_field: str = "created_at"
    ) -> List[Order]:
        """Get orders within date range."""
        pass
    
    @abstractmethod
    async def get_active_orders(self) -> List[Order]:
        """Get all active orders (pending, in_progress, on_hold, delayed)."""
        pass
    
    @abstractmethod
    async def get_overdue_orders(self, current_date: Optional[datetime] = None) -> List[Order]:
        """Get orders that are overdue based on expect_finished_at."""
        pass
    
    @abstractmethod
    async def search_orders(self, search_term: str) -> List[Order]:
        """Search orders by order_no, skc_no, external_order_no, etc."""
        pass
    
    @abstractmethod
    async def get_by_current_craft(self, craft_code: str) -> List[Order]:
        """Get orders currently at a specific craft."""
        pass
    
    @abstractmethod
    async def update(self, order: Order) -> Order:
        """Update order."""
        pass
    
    @abstractmethod
    async def delete(self, order_id: int) -> bool:
        """Delete order."""
        pass
    
    @abstractmethod
    async def get_orders_by_craft_route(self, craft_route_id: int) -> List[Order]:
        """Get orders currently at a specific craft route."""
        pass
    
    @abstractmethod
    async def get_order_statistics(self, user_id: Optional[int] = None) -> dict:
        """Get order statistics, optionally filtered by user."""
        pass