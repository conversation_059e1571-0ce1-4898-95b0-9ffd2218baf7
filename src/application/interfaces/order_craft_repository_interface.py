from abc import ABC, abstractmethod
from typing import List, Optional
from src.domain.entities.order_craft import OrderCraft


class OrderCraftRepositoryInterface(ABC):
    """Interface for order craft repository operations."""
    
    @abstractmethod
    async def create(self, order_craft: OrderCraft) -> OrderCraft:
        """Create a new order craft."""
        pass
    
    @abstractmethod
    async def get_by_id(self, order_craft_id: int) -> Optional[OrderCraft]:
        """Get order craft by ID."""
        pass
    
    @abstractmethod
    async def get_by_order_no(self, order_no: str) -> List[OrderCraft]:
        """Get all order crafts for a specific order."""
        pass
    
    @abstractmethod
    async def get_by_order_and_craft(self, order_no: str, craft_code: str) -> Optional[OrderCraft]:
        """Get order craft by order number and craft code."""
        pass
    
    @abstractmethod
    async def get_by_craft_code(self, craft_code: str) -> List[OrderCraft]:
        """Get all order crafts using a specific craft."""
        pass
    
    @abstractmethod
    async def get_active_by_order(self, order_no: str) -> List[OrderCraft]:
        """Get active order crafts for an order."""
        pass
    
    @abstractmethod
    async def get_by_status(self, status: str) -> List[OrderCraft]:
        """Get order crafts by status."""
        pass
    
    @abstractmethod
    async def get_next_pending_craft(self, order_no: str) -> Optional[OrderCraft]:
        """Get the next pending craft in the workflow for an order."""
        pass
    
    @abstractmethod
    async def get_current_in_progress_craft(self, order_no: str) -> Optional[OrderCraft]:
        """Get the currently in-progress craft for an order."""
        pass
    
    @abstractmethod
    async def update(self, order_craft: OrderCraft) -> OrderCraft:
        """Update order craft."""
        pass
    
    @abstractmethod
    async def delete(self, order_craft_id: int) -> bool:
        """Delete order craft."""
        pass
    
    @abstractmethod
    async def delete_by_order_no(self, order_no: str) -> int:
        """Delete all order crafts for an order. Returns number of deleted records."""
        pass
    
    @abstractmethod
    async def bulk_create(self, order_crafts: List[OrderCraft]) -> List[OrderCraft]:
        """Create multiple order crafts at once."""
        pass
    
    @abstractmethod
    async def reorder_crafts(self, order_no: str, craft_orders: List[tuple]) -> bool:
        """Reorder crafts for an order. craft_orders is list of (craft_code, new_order) tuples."""
        pass
    
    @abstractmethod
    async def get_order_craft_statistics(self, order_no: Optional[str] = None) -> dict:
        """Get order craft statistics, optionally filtered by order."""
        pass
    
    @abstractmethod
    async def bulk_upsert(self, order_crafts: List[OrderCraft], session=None) -> List[OrderCraft]:
        """Create or update multiple order crafts. Uses order_no + craft_code as unique key."""
        pass
    
    @abstractmethod
    async def get_by_order_and_codes(self, order_no: str, craft_codes: List[str], session=None) -> List[OrderCraft]:
        """Get order crafts by order number and craft codes."""
        pass