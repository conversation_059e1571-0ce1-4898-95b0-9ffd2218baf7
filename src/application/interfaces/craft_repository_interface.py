from abc import ABC, abstractmethod
from typing import List, Optional
from src.domain.entities.craft import Craft


class CraftRepositoryInterface(ABC):
    """Interface for craft repository operations."""
    
    @abstractmethod
    async def create(self, craft: Craft) -> Craft:
        """Create a new craft."""
        pass
    
    @abstractmethod
    async def get_by_id(self, craft_id: int) -> Optional[Craft]:
        """Get craft by ID."""
        pass
    
    @abstractmethod
    async def get_by_code(self, code: str) -> Optional[Craft]:
        """Get craft by code."""
        pass
    
    @abstractmethod
    async def get_all(self, skip: int = 0, limit: int = 100) -> List[Craft]:
        """Get all crafts."""
        pass
    
    @abstractmethod
    async def get_enabled_crafts(self) -> List[Craft]:
        """Get all enabled crafts."""
        pass
    
    @abstractmethod
    async def get_by_priority_range(self, min_priority: int, max_priority: int) -> List[Craft]:
        """Get crafts within priority range."""
        pass
    
    @abstractmethod
    async def search_crafts(self, search_term: str) -> List[Craft]:
        """Search crafts by code, name, or description."""
        pass
    
    @abstractmethod
    async def update(self, craft: Craft) -> Craft:
        """Update craft."""
        pass
    
    @abstractmethod
    async def delete(self, craft_id: int) -> bool:
        """Delete craft."""
        pass
    
    @abstractmethod
    async def get_crafts_by_codes(self, codes: List[str]) -> List[Craft]:
        """Get multiple crafts by their codes."""
        pass