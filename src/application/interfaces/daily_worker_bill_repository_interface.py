from abc import ABC, abstractmethod
from typing import List, Optional
from datetime import date
from sqlalchemy.ext.asyncio import AsyncSession
from src.domain.entities.daily_worker_bill import DailyWorkerBill, BillStatus


class DailyWorkerBillRepositoryInterface(ABC):
    """Daily worker bill repository interface."""
    
    @abstractmethod
    async def create(self, bill: DailyWorkerBill, session: AsyncSession) -> DailyWorkerBill:
        """Create a new daily worker bill."""
        pass
    
    @abstractmethod
    async def get_by_id(self, bill_id: int, session: AsyncSession) -> Optional[DailyWorkerBill]:
        """Get daily worker bill by ID."""
        pass
    
    @abstractmethod
    async def get_by_bill_no(self, bill_no: str, session: AsyncSession) -> Optional[DailyWorkerBill]:
        """Get daily worker bill by bill number."""
        pass
    
    @abstractmethod
    async def get_by_worker_and_date(
        self, 
        worker_user_id: int, 
        factory_id: int, 
        bill_date: date,
        session: AsyncSession
    ) -> Optional[DailyWorkerBill]:
        """Get daily worker bill by worker and date."""
        pass
    
    @abstractmethod
    async def get_by_factory_and_date(
        self, 
        factory_id: int, 
        bill_date: date,
        session: AsyncSession
    ) -> List[DailyWorkerBill]:
        """Get all daily worker bills for a factory on a specific date."""
        pass
    
    @abstractmethod
    async def get_by_worker_and_date_range(
        self,
        worker_user_id: int,
        factory_id: int,
        start_date: date,
        end_date: date,
        session: AsyncSession
    ) -> List[DailyWorkerBill]:
        """Get daily worker bills for a worker within a date range."""
        pass
    
    @abstractmethod
    async def get_by_status(
        self,
        factory_id: int,
        status: BillStatus,
        session: AsyncSession
    ) -> List[DailyWorkerBill]:
        """Get daily worker bills by status."""
        pass
    
    @abstractmethod
    async def get_pending_bills(self, factory_id: int, session: AsyncSession) -> List[DailyWorkerBill]:
        """Get all pending bills for a factory."""
        pass
    
    @abstractmethod
    async def update(self, bill: DailyWorkerBill, session: AsyncSession) -> DailyWorkerBill:
        """Update daily worker bill."""
        pass
    
    @abstractmethod
    async def delete(self, bill_id: int, session: AsyncSession) -> bool:
        """Delete daily worker bill."""
        pass
    
    @abstractmethod
    async def get_all(
        self, 
        factory_id: int, 
        session: AsyncSession,
        skip: int = 0, 
        limit: int = 100
    ) -> List[DailyWorkerBill]:
        """Get all daily worker bills with pagination."""
        pass