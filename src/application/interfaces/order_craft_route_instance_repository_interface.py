from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from src.domain.entities.order_craft_route_instance import OrderCraftRouteInstance, SettlementStatus, CompletionGranularity


class OrderCraftRouteInstanceRepositoryInterface(ABC):
    """Order craft route instance repository interface."""
    
    @abstractmethod
    async def create(self, instance: OrderCraftRouteInstance, session: AsyncSession) -> OrderCraftRouteInstance:
        """Create a new order craft route instance."""
        pass
    
    @abstractmethod
    async def get_by_id(self, instance_id: int, session: AsyncSession) -> Optional[OrderCraftRouteInstance]:
        """Get order craft route instance by ID."""
        pass
    
    @abstractmethod
    async def get_by_craft_route(
        self, 
        order_craft_route_id: int,
        session: AsyncSession
    ) -> List[OrderCraftRouteInstance]:
        """Get all instances for a specific craft route."""
        pass
    
    @abstractmethod
    async def get_by_worker(
        self,
        worker_user_id: int,
        factory_id: int,
        session: AsyncSession
    ) -> List[OrderCraftRouteInstance]:
        """Get all instances for a specific worker."""
        pass
    
    @abstractmethod
    async def get_completed_in_date_range(
        self,
        factory_id: int,
        start_datetime: datetime,
        end_datetime: datetime,
        session: AsyncSession
    ) -> List[OrderCraftRouteInstance]:
        """Get completed instances within a date range."""
        pass
    
    @abstractmethod
    async def get_worker_completed_in_date_range(
        self,
        factory_id: int,
        worker_user_id: int,
        start_datetime: datetime,
        end_datetime: datetime,
        session: AsyncSession
    ) -> List[OrderCraftRouteInstance]:
        """Get completed instances for a specific worker within a date range."""
        pass
    
    @abstractmethod
    async def get_by_settlement_status(
        self,
        factory_id: int,
        settlement_status: SettlementStatus,
        session: AsyncSession
    ) -> List[OrderCraftRouteInstance]:
        """Get instances by settlement status."""
        pass
    
    @abstractmethod
    async def get_by_granularity(
        self,
        factory_id: int,
        granularity: CompletionGranularity,
        session: AsyncSession
    ) -> List[OrderCraftRouteInstance]:
        """Get instances by completion granularity."""
        pass
    
    @abstractmethod
    async def get_by_order_no(
        self,
        factory_id: int,
        order_no: str,
        session: AsyncSession
    ) -> List[OrderCraftRouteInstance]:
        """Get instances by order number."""
        pass
    
    @abstractmethod
    async def get_by_order_bundle_no(
        self,
        factory_id: int,
        order_bundle_no: str,
        session: AsyncSession
    ) -> List[OrderCraftRouteInstance]:
        """Get instances by order bundle number."""
        pass
    
    @abstractmethod
    async def update(self, instance: OrderCraftRouteInstance, session: AsyncSession) -> OrderCraftRouteInstance:
        """Update order craft route instance."""
        pass
    
    @abstractmethod
    async def bulk_update(self, instances: List[OrderCraftRouteInstance], session: AsyncSession) -> List[OrderCraftRouteInstance]:
        """Bulk update order craft route instances."""
        pass
    
    @abstractmethod
    async def delete(self, instance_id: int, session: AsyncSession) -> bool:
        """Delete order craft route instance."""
        pass
    
    @abstractmethod
    async def get_all(
        self, 
        factory_id: int, 
        session: AsyncSession,
        skip: int = 0, 
        limit: int = 100
    ) -> List[OrderCraftRouteInstance]:
        """Get all order craft route instances with pagination."""
        pass
    
    @abstractmethod
    async def get_registered_summary_by_order(
        self,
        factory_id: int,
        order_no: str,
        session: AsyncSession
    ) -> Dict[str, Any]:
        """Get registered quantity summary by order number."""
        pass
    
    @abstractmethod
    async def get_registered_summary_by_craft_route(
        self,
        factory_id: int,
        order_craft_route_id: int,
        session: AsyncSession
    ) -> Dict[str, Any]:
        """Get registered quantity summary by craft route."""
        pass
    
    @abstractmethod
    async def get_registered_parts_and_bundles(
        self,
        factory_id: int,
        order_no: str,
        session: AsyncSession,
        order_craft_route_id: Optional[int] = None
    ) -> Dict[str, Dict[str, int]]:
        """Get registered parts and bundles with quantities."""
        pass