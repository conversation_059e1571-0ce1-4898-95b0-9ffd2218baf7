from abc import ABC, abstractmethod
from typing import List, Optional
from src.domain.entities.order_bundle import OrderBundle, BundleStatus


class OrderBundleRepositoryInterface(ABC):
    """Interface for order bundle repository operations."""
    
    @abstractmethod
    async def create(self, order_bundle: OrderBundle) -> OrderBundle:
        """Create a new order bundle."""
        pass
    
    @abstractmethod
    async def get_by_id(self, order_bundle_id: int) -> Optional[OrderBundle]:
        """Get order bundle by ID."""
        pass
    
    @abstractmethod
    async def get_by_order_bundle_no_and_factory(self, order_bundle_no: str, factory_id: int, order_no: str, session=None) -> Optional[OrderBundle]:
        """Get order bundle by order bundle number, factory ID, and order number."""
        pass
    
    @abstractmethod
    async def get_by_order_part_no_and_factory(self, order_part_no: str, factory_id: int, order_no: str, session=None) -> List[OrderBundle]:
        """Get all order bundles for a specific order part, factory, and order."""
        pass
    
    @abstractmethod
    async def get_by_order_no_and_factory(self, order_no: str, factory_id: int, session=None) -> List[OrderBundle]:
        """Get all order bundles for a specific order and factory."""
        pass
    
    @abstractmethod
    async def get_by_status(self, status: BundleStatus, factory_id: int) -> List[OrderBundle]:
        """Get order bundles by status and factory."""
        pass
    
    @abstractmethod
    async def get_by_size(self, size: str, factory_id: int) -> List[OrderBundle]:
        """Get order bundles by size and factory."""
        pass
    
    @abstractmethod
    async def get_by_user(self, user_id: int, factory_id: int, user_type: str = "any") -> List[OrderBundle]:
        """Get order bundles by user (cutter, sewer, qc_user) and factory."""
        pass
    
    @abstractmethod
    async def get_all(self, factory_id: int, skip: int = 0, limit: int = 100) -> List[OrderBundle]:
        """Get all order bundles for a factory."""
        pass
    
    @abstractmethod
    async def update(self, order_bundle: OrderBundle) -> OrderBundle:
        """Update order bundle."""
        pass
    
    @abstractmethod
    async def delete(self, order_bundle_id: int) -> bool:
        """Delete order bundle."""
        pass
    
    @abstractmethod
    async def delete_by_order_part_no_and_factory(self, order_part_no: str, factory_id: int, order_no: str) -> int:
        """Delete all order bundles for an order part, factory, and order."""
        pass
    
    @abstractmethod
    async def delete_by_order_no_and_factory(self, order_no: str, factory_id: int) -> int:
        """Delete all order bundles for an order and factory."""
        pass
    
    @abstractmethod
    async def bulk_create(self, order_bundles: List[OrderBundle], session=None) -> List[OrderBundle]:
        """Create multiple order bundles at once."""
        pass
    
    @abstractmethod
    async def get_order_bundle_statistics(self, factory_id: int, order_no: Optional[str] = None, order_part_no: Optional[str] = None) -> dict:
        """Get order bundle statistics for a factory, optionally filtered by order or order part."""
        pass