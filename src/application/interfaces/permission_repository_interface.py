from abc import ABC, abstractmethod
from typing import List, Optional
from src.domain.entities.permission import Permission


class PermissionRepositoryInterface(ABC):
    """Interface for Permission repository."""

    @abstractmethod
    async def create(self, permission: Permission) -> Permission:
        """Create a new permission."""
        pass

    @abstractmethod
    async def get_by_id(self, permission_id: int) -> Optional[Permission]:
        """Get permission by ID."""
        pass

    @abstractmethod
    async def get_by_code(self, code: str) -> Optional[Permission]:
        """Get permission by code."""
        pass

    @abstractmethod
    async def get_all(self) -> List[Permission]:
        """Get all permissions."""
        pass

    @abstractmethod
    async def get_root_permissions(self) -> List[Permission]:
        """Get all root permissions (no parent)."""
        pass

    @abstractmethod
    async def get_children(self, parent_id: int) -> List[Permission]:
        """Get all child permissions of a parent."""
        pass

    @abstractmethod
    async def update(self, permission: Permission) -> Permission:
        """Update permission."""
        pass

    @abstractmethod
    async def delete(self, permission_id: int) -> bool:
        """Delete permission by ID."""
        pass

    @abstractmethod
    async def get_permission_tree(self) -> List[Permission]:
        """Get hierarchical permission tree."""
        pass