from abc import ABC, abstractmethod
from typing import List, Optional
from src.domain.entities.order_line import OrderLine


class OrderLineRepositoryInterface(ABC):
    """Interface for order line repository operations."""
    
    @abstractmethod
    async def create(self, order_line: OrderLine) -> OrderLine:
        """Create a new order line."""
        pass
    
    @abstractmethod
    async def get_by_id(self, order_line_id: int) -> Optional[OrderLine]:
        """Get order line by ID."""
        pass
    
    @abstractmethod
    async def get_by_order_line_no(self, order_line_no: str) -> Optional[OrderLine]:
        """Get order line by order line number."""
        pass
    
    @abstractmethod
    async def get_by_order_line_no_and_factory(self, order_line_no: str, factory_id: int, session=None) -> Optional[OrderLine]:
        """Get order line by order line number and factory ID."""
        pass
    
    @abstractmethod
    async def get_by_order_no(self, order_no: str) -> List[OrderLine]:
        """Get all order lines for a specific order."""
        pass
    
    @abstractmethod
    async def get_by_order_no_and_factory(self, order_no: str, factory_id: int, session=None) -> List[OrderLine]:
        """Get all order lines for a specific order and factory."""
        pass
    
    @abstractmethod
    async def get_by_size(self, size: str) -> List[OrderLine]:
        """Get order lines by size."""
        pass
    
    @abstractmethod
    async def get_all(self, skip: int = 0, limit: int = 100) -> List[OrderLine]:
        """Get all order lines."""
        pass
    
    @abstractmethod
    async def get_incomplete_lines(self) -> List[OrderLine]:
        """Get order lines that are not yet completed."""
        pass
    
    @abstractmethod
    async def get_in_progress_lines(self) -> List[OrderLine]:
        """Get order lines that are in progress."""
        pass
    
    @abstractmethod
    async def get_lines_by_order_and_size(self, order_no: str, size: str) -> Optional[OrderLine]:
        """Get specific order line by order number and size."""
        pass
    
    @abstractmethod
    async def update(self, order_line: OrderLine) -> OrderLine:
        """Update order line."""
        pass
    
    @abstractmethod
    async def delete(self, order_line_id: int) -> bool:
        """Delete order line."""
        pass
    
    @abstractmethod
    async def delete_by_order_no(self, order_no: str) -> int:
        """Delete all order lines for an order. Returns number of deleted lines."""
        pass
    
    @abstractmethod
    async def bulk_create(self, order_lines: List[OrderLine]) -> List[OrderLine]:
        """Create multiple order lines at once."""
        pass
    
    @abstractmethod
    async def bulk_update_production(self, updates: List[tuple]) -> bool:
        """Bulk update production quantities. updates is list of (order_line_id, produced_qty) tuples."""
        pass
    
    @abstractmethod
    async def bulk_update_completion(self, updates: List[tuple]) -> bool:
        """Bulk update completion quantities. updates is list of (order_line_id, completed_qty) tuples."""
        pass
    
    @abstractmethod
    async def get_order_line_statistics(self, order_no: Optional[str] = None) -> dict:
        """Get order line statistics, optionally filtered by order."""
        pass