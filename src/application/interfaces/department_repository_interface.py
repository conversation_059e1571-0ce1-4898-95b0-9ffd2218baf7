from abc import ABC, abstractmethod
from typing import List, Optional
from src.domain.entities.department import Department


class DepartmentRepositoryInterface(ABC):
    """Abstract interface for department repository."""
    
    @abstractmethod
    async def create(self, department: Department) -> Department:
        """Create a new department."""
        pass
    
    @abstractmethod
    async def get_by_id(self, department_id: int) -> Optional[Department]:
        """Get department by ID."""
        pass
    
    @abstractmethod
    async def get_by_code_and_factory(self, code: str, factory_id: int) -> Optional[Department]:
        """Get department by code within a factory."""
        pass
    
    @abstractmethod
    async def get_by_factory_id(self, factory_id: int, skip: int = 0, limit: int = 100) -> List[Department]:
        """Get departments by factory ID with pagination."""
        pass
    
    @abstractmethod
    async def update(self, department: Department) -> Department:
        """Update an existing department."""
        pass
    
    @abstractmethod
    async def delete(self, department_id: int) -> bool:
        """Delete a department by ID."""
        pass
    
    @abstractmethod
    async def get_all(self, skip: int = 0, limit: int = 100, is_active: Optional[bool] = None) -> List[Department]:
        """Get all departments with pagination and optional filtering."""
        pass
    
    @abstractmethod
    async def get_with_users(self, department_id: int) -> Optional[Department]:
        """Get department with its users."""
        pass