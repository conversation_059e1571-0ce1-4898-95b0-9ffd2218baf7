from abc import ABC, abstractmethod
from typing import List, Optional
from src.domain.entities.bundle import Bundle, BundleStatus


class BundleRepositoryInterface(ABC):
    """Interface for bundle repository operations."""
    
    @abstractmethod
    async def create(self, bundle: Bundle) -> Bundle:
        """Create a new bundle."""
        pass
    
    @abstractmethod
    async def get_by_id(self, bundle_id: int) -> Optional[Bundle]:
        """Get bundle by ID."""
        pass
    
    @abstractmethod
    async def get_by_bundle_no(self, bundle_no: str) -> Optional[Bundle]:
        """Get bundle by bundle number."""
        pass
    
    @abstractmethod
    async def get_by_part_no(self, part_no: str) -> List[Bundle]:
        """Get all bundles for a specific part."""
        pass
    
    @abstractmethod
    async def get_by_order_no(self, order_no: str) -> List[Bundle]:
        """Get all bundles for a specific order."""
        pass
    
    @abstractmethod
    async def get_by_part_and_size(self, part_no: str, size: str) -> List[Bundle]:
        """Get bundles by part and size."""
        pass
    
    @abstractmethod
    async def get_by_status(self, status: BundleStatus) -> List[Bundle]:
        """Get bundles by status."""
        pass
    
    @abstractmethod
    async def get_by_size(self, size: str) -> List[Bundle]:
        """Get bundles by size."""
        pass
    
    @abstractmethod
    async def get_by_color(self, color: str) -> List[Bundle]:
        """Get bundles by color."""
        pass
    
    @abstractmethod
    async def get_by_worker(self, worker_user_id: int, worker_type: str) -> List[Bundle]:
        """Get bundles by worker (cutter, sewer, qc_user)."""
        pass
    
    @abstractmethod
    async def get_all(self, skip: int = 0, limit: int = 100) -> List[Bundle]:
        """Get all bundles."""
        pass
    
    @abstractmethod
    async def search_bundles(self, search_term: str) -> List[Bundle]:
        """Search bundles by bundle_no, part_no, etc."""
        pass
    
    @abstractmethod
    async def get_incomplete_bundles(self) -> List[Bundle]:
        """Get bundles that are not yet completed."""
        pass
    
    @abstractmethod
    async def get_in_progress_bundles(self) -> List[Bundle]:
        """Get bundles that are in progress."""
        pass
    
    @abstractmethod
    async def get_rework_bundles(self) -> List[Bundle]:
        """Get bundles that need rework."""
        pass
    
    @abstractmethod
    async def get_bundles_by_machine(self, machine: str, machine_type: str) -> List[Bundle]:
        """Get bundles by machine (cutting_machine, sewing_machine)."""
        pass
    
    @abstractmethod
    async def update(self, bundle: Bundle) -> Bundle:
        """Update bundle."""
        pass
    
    @abstractmethod
    async def delete(self, bundle_id: int) -> bool:
        """Delete bundle."""
        pass
    
    @abstractmethod
    async def delete_by_part_no(self, part_no: str) -> int:
        """Delete all bundles for a part. Returns number of deleted bundles."""
        pass
    
    @abstractmethod
    async def delete_by_order_no(self, order_no: str) -> int:
        """Delete all bundles for an order. Returns number of deleted bundles."""
        pass
    
    @abstractmethod
    async def bulk_create(self, bundles: List[Bundle]) -> List[Bundle]:
        """Create multiple bundles at once."""
        pass
    
    @abstractmethod
    async def bulk_update_status(self, updates: List[tuple]) -> bool:
        """Bulk update bundle status. updates is list of (bundle_id, status) tuples."""
        pass
    
    @abstractmethod
    async def bulk_update_progress(self, updates: List[tuple]) -> bool:
        """Bulk update bundle progress. updates is list of (bundle_id, completed_qty) tuples."""
        pass
    
    @abstractmethod
    async def bulk_update_quality(self, updates: List[tuple]) -> bool:
        """Bulk update bundle quality. updates is list of (bundle_id, defective_qty, quality_level) tuples."""
        pass
    
    @abstractmethod
    async def get_bundle_statistics(self, part_no: Optional[str] = None, order_no: Optional[str] = None) -> dict:
        """Get bundle statistics, optionally filtered by part or order."""
        pass
    
    @abstractmethod
    async def get_bundles_by_date_range(self, start_date, end_date, date_field: str = "created_at") -> List[Bundle]:
        """Get bundles within date range."""
        pass
    
    @abstractmethod
    async def get_production_summary_by_worker(self, worker_user_id: int, worker_type: str) -> dict:
        """Get production summary for a specific worker."""
        pass
