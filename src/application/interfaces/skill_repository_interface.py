from abc import ABC, abstractmethod
from typing import List, Optional
from src.domain.entities.skill import Skill


class SkillRepositoryInterface(ABC):
    """Interface for Skill repository."""

    @abstractmethod
    async def create(self, skill: Skill) -> Skill:
        """Create a new skill."""
        pass

    @abstractmethod
    async def get_by_id(self, skill_id: int) -> Optional[Skill]:
        """Get skill by ID."""
        pass

    @abstractmethod
    async def get_by_code(self, code: str) -> Optional[Skill]:
        """Get skill by code."""
        pass

    @abstractmethod
    async def get_all(self) -> List[Skill]:
        """Get all skills."""
        pass

    @abstractmethod
    async def get_active_skills(self) -> List[Skill]:
        """Get all active skills."""
        pass

    @abstractmethod
    async def get_by_category(self, category: str) -> List[Skill]:
        """Get skills by category."""
        pass

    @abstractmethod
    async def update(self, skill: Skill) -> Skill:
        """Update skill."""
        pass

    @abstractmethod
    async def delete(self, skill_id: int) -> bool:
        """Delete skill by ID."""
        pass

    @abstractmethod
    async def search_skills(self, search_term: str) -> List[Skill]:
        """Search skills by name or description."""
        pass