from abc import ABC, abstractmethod
from typing import List, Optional
from src.domain.entities.user_factory import UserFactory, UserFactoryStatus, UserFactoryRole


class UserFactoryRepositoryInterface(ABC):
    """Abstract interface for user factory repository."""
    
    @abstractmethod
    async def create(self, user_factory: UserFactory) -> UserFactory:
        """Create a new user factory relationship."""
        pass
    
    @abstractmethod
    async def get_by_id(self, user_factory_id: int) -> Optional[UserFactory]:
        """Get user factory relationship by ID."""
        pass
    
    @abstractmethod
    async def get_by_user_and_factory(self, user_id: int, factory_id: int) -> Optional[UserFactory]:
        """Get user factory relationship by user and factory."""
        pass
    
    @abstractmethod
    async def get_by_user_id(self, user_id: int) -> List[UserFactory]:
        """Get all factory relationships for a user."""
        pass
    
    @abstractmethod
    async def get_by_factory_id(self, factory_id: int) -> List[UserFactory]:
        """Get all user relationships for a factory."""
        pass
    
    @abstractmethod
    async def get_active_members_by_factory(self, factory_id: int) -> List[UserFactory]:
        """Get all active members of a factory."""
        pass
    
    @abstractmethod
    async def get_pending_requests_by_factory(self, factory_id: int) -> List[UserFactory]:
        """Get all pending join requests for a factory."""
        pass
    
    @abstractmethod
    async def get_by_status(self, status: UserFactoryStatus, skip: int = 0, limit: int = 100) -> List[UserFactory]:
        """Get user factory relationships by status."""
        pass
    
    @abstractmethod
    async def get_managers_by_factory(self, factory_id: int) -> List[UserFactory]:
        """Get all managers of a factory."""
        pass
    
    @abstractmethod
    async def get_by_user_and_role(self, user_id: int, role: UserFactoryRole) -> List[UserFactory]:
        """Get factories where user has specific role."""
        pass
    
    @abstractmethod
    async def update(self, user_factory: UserFactory) -> UserFactory:
        """Update user factory relationship."""
        pass
    
    @abstractmethod
    async def delete(self, user_factory_id: int) -> bool:
        """Delete user factory relationship."""
        pass
    
    @abstractmethod
    async def get_factory_statistics(self, factory_id: int) -> dict:
        """Get statistics for a factory (member counts, etc.)."""
        pass
    
    @abstractmethod
    async def get_user_by_employee_id(self, factory_id: int, employee_id: str) -> Optional[UserFactory]:
        """Get user by employee ID within a factory."""
        pass