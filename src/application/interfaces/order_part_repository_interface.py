from abc import ABC, abstractmethod
from typing import List, Optional
from src.domain.entities.order_part import OrderPart, PartStatus


class OrderPartRepositoryInterface(ABC):
    """Interface for order part repository operations."""
    
    @abstractmethod
    async def create(self, order_part: OrderPart) -> OrderPart:
        """Create a new order part."""
        pass
    
    @abstractmethod
    async def get_by_id(self, order_part_id: int) -> Optional[OrderPart]:
        """Get order part by ID."""
        pass
    
    @abstractmethod
    async def get_by_order_part_no_and_factory(self, order_part_no: str, factory_id: int, order_no: str, session=None) -> Optional[OrderPart]:
        """Get order part by order part number, factory ID, and order number."""
        pass
    
    @abstractmethod
    async def get_by_order_no_and_factory(self, order_no: str, factory_id: int, session=None) -> List[OrderPart]:
        """Get all order parts for a specific order and factory."""
        pass
    
    @abstractmethod
    async def get_by_status(self, status: PartStatus, factory_id: int) -> List[OrderPart]:
        """Get order parts by status and factory."""
        pass
    
    @abstractmethod
    async def get_by_supervisor(self, supervisor_user_id: int, factory_id: int) -> List[OrderPart]:
        """Get order parts by supervisor and factory."""
        pass
    
    @abstractmethod
    async def get_all(self, factory_id: int, skip: int = 0, limit: int = 100) -> List[OrderPart]:
        """Get all order parts for a factory."""
        pass
    
    @abstractmethod
    async def update(self, order_part: OrderPart) -> OrderPart:
        """Update order part."""
        pass
    
    @abstractmethod
    async def delete(self, order_part_id: int) -> bool:
        """Delete order part."""
        pass
    
    @abstractmethod
    async def delete_by_order_no_and_factory(self, order_no: str, factory_id: int) -> int:
        """Delete all order parts for an order and factory."""
        pass
    
    @abstractmethod
    async def bulk_create(self, order_parts: List[OrderPart], session=None) -> List[OrderPart]:
        """Create multiple order parts at once."""
        pass
    
    @abstractmethod
    async def get_order_part_statistics(self, factory_id: int, order_no: Optional[str] = None) -> dict:
        """Get order part statistics for a factory, optionally filtered by order."""
        pass