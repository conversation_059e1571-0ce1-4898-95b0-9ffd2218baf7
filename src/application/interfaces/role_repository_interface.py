from abc import ABC, abstractmethod
from typing import List, Optional
from src.domain.entities.role import Role
from src.domain.entities.permission import Permission


class RoleRepositoryInterface(ABC):
    """Interface for Role repository."""

    @abstractmethod
    async def create(self, role: Role) -> Role:
        """Create a new role."""
        pass

    @abstractmethod
    async def get_by_id(self, role_id: int) -> Optional[Role]:
        """Get role by ID."""
        pass

    @abstractmethod
    async def get_by_name(self, name: str) -> Optional[Role]:
        """Get role by name."""
        pass

    @abstractmethod
    async def get_all(self) -> List[Role]:
        """Get all roles."""
        pass

    @abstractmethod
    async def get_active_roles(self) -> List[Role]:
        """Get all active roles."""
        pass

    @abstractmethod
    async def update(self, role: Role) -> Role:
        """Update role."""
        pass

    @abstractmethod
    async def delete(self, role_id: int) -> bool:
        """Delete role by ID."""
        pass

    @abstractmethod
    async def add_permission_to_role(self, role_id: int, permission_id: int) -> bool:
        """Add permission to role."""
        pass

    @abstractmethod
    async def remove_permission_from_role(self, role_id: int, permission_id: int) -> bool:
        """Remove permission from role."""
        pass

    @abstractmethod
    async def get_role_permissions(self, role_id: int) -> List[Permission]:
        """Get all permissions for a role."""
        pass

    @abstractmethod
    async def get_roles_with_permission(self, permission_id: int) -> List[Role]:
        """Get all roles that have a specific permission."""
        pass