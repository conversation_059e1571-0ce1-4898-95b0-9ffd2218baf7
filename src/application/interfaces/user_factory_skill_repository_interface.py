from abc import ABC, abstractmethod
from typing import List, Optional
from src.domain.entities.user_factory_skill import UserFactorySkill


class UserFactorySkillRepositoryInterface(ABC):
    """Interface for UserFactorySkill repository."""

    @abstractmethod
    async def create(self, user_factory_skill: UserFactorySkill) -> UserFactorySkill:
        """Create a new user factory skill."""
        pass

    @abstractmethod
    async def get_by_id(self, skill_id: int) -> Optional[UserFactorySkill]:
        """Get user factory skill by ID."""
        pass

    @abstractmethod
    async def get_by_user_factory_and_skill(self, user_factory_id: int, skill_id: int) -> Optional[UserFactorySkill]:
        """Get user factory skill by user factory and skill."""
        pass

    @abstractmethod
    async def get_by_user_factory(self, user_factory_id: int) -> List[UserFactorySkill]:
        """Get all skills for a user factory relationship."""
        pass

    @abstractmethod
    async def get_by_skill(self, skill_id: int) -> List[UserFactorySkill]:
        """Get all user factory relationships for a skill."""
        pass

    @abstractmethod
    async def get_certified_skills(self, user_factory_id: int) -> List[UserFactorySkill]:
        """Get all certified skills for a user factory relationship."""
        pass

    @abstractmethod
    async def update(self, user_factory_skill: UserFactorySkill) -> UserFactorySkill:
        """Update user factory skill."""
        pass

    @abstractmethod
    async def delete(self, skill_id: int) -> bool:
        """Delete user factory skill by ID."""
        pass

    @abstractmethod
    async def delete_by_user_factory_and_skill(self, user_factory_id: int, skill_id: int) -> bool:
        """Delete user factory skill by user factory and skill."""
        pass