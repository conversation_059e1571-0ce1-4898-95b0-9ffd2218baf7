from abc import ABC, abstractmethod
from typing import List, Optional
from src.domain.entities.order_craft_route import OrderCraftRoute


class OrderCraftRouteRepositoryInterface(ABC):
    """Interface for order craft route repository operations."""
    
    @abstractmethod
    async def create(self, order_craft_route: OrderCraftRoute) -> OrderCraftRoute:
        """Create a new order craft route."""
        pass
    
    @abstractmethod
    async def get_by_id(self, order_craft_route_id: int) -> Optional[OrderCraftRoute]:
        """Get order craft route by ID."""
        pass
    
    @abstractmethod
    async def get_by_order_craft_id(self, order_craft_id: int) -> List[OrderCraftRoute]:
        """Get all order craft routes for a specific order craft."""
        pass
    
    @abstractmethod
    async def get_by_order_craft_and_skill(self, order_craft_id: int, skill_code: str) -> Optional[OrderCraftRoute]:
        """Get order craft route by order craft ID and skill code."""
        pass
    
    @abstractmethod
    async def get_by_skill_code(self, skill_code: str) -> List[OrderCraftRoute]:
        """Get all order craft routes using a specific skill."""
        pass
    
    @abstractmethod
    async def get_by_order_no(self, order_no: str) -> List[OrderCraftRoute]:
        """Get all order craft routes for a specific order."""
        pass
    
    @abstractmethod
    async def get_active_by_order_craft(self, order_craft_id: int) -> List[OrderCraftRoute]:
        """Get active order craft routes for an order craft."""
        pass
    
    @abstractmethod
    async def get_by_status(self, status: str) -> List[OrderCraftRoute]:
        """Get order craft routes by status."""
        pass
    
    @abstractmethod
    async def get_by_assigned_user(self, user_id: int) -> List[OrderCraftRoute]:
        """Get order craft routes assigned to a specific user."""
        pass
    
    @abstractmethod
    async def get_next_pending_route(self, order_craft_id: int) -> Optional[OrderCraftRoute]:
        """Get the next pending route in the craft workflow."""
        pass
    
    @abstractmethod
    async def get_current_in_progress_route(self, order_craft_id: int) -> Optional[OrderCraftRoute]:
        """Get the currently in-progress route for an order craft."""
        pass
    
    @abstractmethod
    async def update(self, order_craft_route: OrderCraftRoute) -> OrderCraftRoute:
        """Update order craft route."""
        pass
    
    @abstractmethod
    async def delete(self, order_craft_route_id: int) -> bool:
        """Delete order craft route."""
        pass
    
    @abstractmethod
    async def delete_by_order_craft_id(self, order_craft_id: int) -> int:
        """Delete all order craft routes for an order craft. Returns number of deleted records."""
        pass
    
    @abstractmethod
    async def delete_by_order_no(self, order_no: str) -> int:
        """Delete all order craft routes for an order. Returns number of deleted records."""
        pass
    
    @abstractmethod
    async def bulk_create(self, order_craft_routes: List[OrderCraftRoute]) -> List[OrderCraftRoute]:
        """Create multiple order craft routes at once."""
        pass
    
    @abstractmethod
    async def reorder_routes(self, order_craft_id: int, route_orders: List[tuple]) -> bool:
        """Reorder routes for an order craft. route_orders is list of (skill_code, new_order) tuples."""
        pass
    
    @abstractmethod
    async def assign_user_to_route(self, order_craft_route_id: int, user_id: int) -> bool:
        """Assign a user to an order craft route."""
        pass
    
    @abstractmethod
    async def unassign_user_from_route(self, order_craft_route_id: int) -> bool:
        """Unassign user from an order craft route."""
        pass
    
    @abstractmethod
    async def bulk_assign_users(self, assignments: List[tuple]) -> bool:
        """Bulk assign users. assignments is list of (order_craft_route_id, user_id) tuples."""
        pass
    
    @abstractmethod
    async def get_route_statistics(self, order_craft_id: Optional[int] = None, order_no: Optional[str] = None) -> dict:
        """Get route statistics, optionally filtered by order craft or order."""
        pass
    
    @abstractmethod
    async def get_user_workload(self, user_id: int) -> dict:
        """Get workload statistics for a specific user."""
        pass
    
    @abstractmethod
    async def bulk_upsert(self, order_craft_routes: List[OrderCraftRoute], session=None) -> List[OrderCraftRoute]:
        """Create or update multiple order craft routes. Uses order_craft_id + skill_code as unique key."""
        pass
    
    @abstractmethod
    async def get_by_order_craft_and_skills(self, order_craft_id: int, skill_codes: List[str], session=None) -> List[OrderCraftRoute]:
        """Get order craft routes by order craft ID and skill codes."""
        pass