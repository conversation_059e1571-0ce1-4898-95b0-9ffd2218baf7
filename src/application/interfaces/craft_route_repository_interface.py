from abc import ABC, abstractmethod
from typing import List, Optional
from src.domain.entities.craft_route import CraftRoute


class CraftRouteRepositoryInterface(ABC):
    """Interface for craft route repository operations."""
    
    @abstractmethod
    async def create(self, craft_route: CraftRoute) -> CraftRoute:
        """Create a new craft route."""
        pass
    
    @abstractmethod
    async def get_by_id(self, craft_route_id: int) -> Optional[CraftRoute]:
        """Get craft route by ID."""
        pass
    
    @abstractmethod
    async def get_by_craft_and_skill(self, craft_code: str, skill_code: str) -> Optional[CraftRoute]:
        """Get craft route by craft code and skill code."""
        pass
    
    @abstractmethod
    async def get_by_craft_code(self, craft_code: str) -> List[CraftRoute]:
        """Get all routes for a specific craft."""
        pass
    
    @abstractmethod
    async def get_by_skill_code(self, skill_code: str) -> List[CraftRoute]:
        """Get all routes that use a specific skill."""
        pass
    
    @abstractmethod
    async def get_all(self, skip: int = 0, limit: int = 100) -> List[CraftRoute]:
        """Get all craft routes."""
        pass
    
    @abstractmethod
    async def update(self, craft_route: CraftRoute) -> CraftRoute:
        """Update craft route."""
        pass
    
    @abstractmethod
    async def delete(self, craft_route_id: int) -> bool:
        """Delete craft route."""
        pass
    
    @abstractmethod
    async def delete_by_craft_code(self, craft_code: str) -> int:
        """Delete all routes for a craft. Returns number of deleted routes."""
        pass
    
    @abstractmethod
    async def delete_by_skill_code(self, skill_code: str) -> int:
        """Delete all routes using a skill. Returns number of deleted routes."""
        pass
    
    @abstractmethod
    async def reorder_craft_routes(self, craft_code: str, route_orders: List[tuple]) -> bool:
        """Reorder routes for a craft. route_orders is list of (route_id, new_order) tuples."""
        pass