from abc import ABC, abstractmethod
from typing import Optional
from src.domain.entities.validation_code_record import ValidationCodeRecord
from src.domain.value_objects.validation_code import ValidationCodeType


class ValidationCodeRepositoryInterface(ABC):
    """Abstract interface for validation code repository."""
    
    @abstractmethod
    async def create(self, validation_code: ValidationCodeRecord) -> ValidationCodeRecord:
        """Create a new validation code record."""
        pass
    
    @abstractmethod
    async def get_latest_by_identifier_and_type(
        self, 
        identifier: str, 
        code_type: ValidationCodeType
    ) -> Optional[ValidationCodeRecord]:
        """Get the latest validation code by identifier and type."""
        pass
    
    @abstractmethod
    async def mark_as_used(self, validation_code_id: int) -> bool:
        """Mark validation code as used."""
        pass
    
    @abstractmethod
    async def delete_expired_codes(self) -> int:
        """Delete all expired validation codes and return count of deleted records."""
        pass
    
    @abstractmethod
    async def get_by_id(self, validation_code_id: int) -> Optional[ValidationCodeRecord]:
        """Get validation code by ID."""
        pass
    
    @abstractmethod
    async def count_recent_codes(
        self, 
        identifier: str, 
        code_type: ValidationCodeType, 
        minutes: int = 60
    ) -> int:
        """Count validation codes created for identifier in the last N minutes."""
        pass