from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from dependency_injector.wiring import Provide, inject
from src.application.use_cases.bill_use_cases import BillUseCases
from src.application.dto.bill_dto import (
    CreateBillRequestDTO, UpdateBillRequestDTO, BillResponseDTO, BillListResponseDTO,
    BillOperationResponseDTO, BillSettlementSummaryDTO, BillSearchRequestDTO,
    BillSubmitForReviewRequestDTO, BillApprovalRequestDTO, BillRejectionRequestDTO,
    BillPaymentRequestDTO, BillCancellationRequestDTO, InstanceDisputeRequestDTO,
    InstanceDisputeResolutionRequestDTO
)
from src.domain.entities.daily_worker_bill import BillStatus, PaymentMethod
from src.presentation.api.v1.auth import get_current_active_user
from src.presentation.api.v1.factory_management import get_current_factory_from_session
from src.domain.entities.user import User
from src.infrastructure.containers import Container
from datetime import date

router = APIRouter(prefix="/bills", tags=["bills"])


@router.post("/", response_model=BillOperationResponseDTO)
@inject
async def create_bill(
    request: CreateBillRequestDTO,
    current_user: User = Depends(get_current_active_user),
    factory_id: int = Depends(get_current_factory_from_session),
    bill_use_cases: BillUseCases = Depends(Provide[Container.bill_use_cases])
):
    """Create a new bill."""
    
    return await bill_use_cases.create_bill(
        current_user_id=current_user.id,
        factory_id=factory_id,
        request=request
    )


@router.get("/", response_model=BillListResponseDTO)
@inject
async def get_bills(
    worker_user_id: Optional[int] = Query(None, description="Worker user ID"),
    status: Optional[BillStatus] = Query(None, description="Bill status"),
    bill_date: Optional[date] = Query(None, description="Bill date"),
    start_date: Optional[date] = Query(None, description="Start date for date range search"),
    end_date: Optional[date] = Query(None, description="End date for date range search"),
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of records to return"),
    current_user: User = Depends(get_current_active_user),
    factory_id: int = Depends(get_current_factory_from_session),
    bill_use_cases: BillUseCases = Depends(Provide[Container.bill_use_cases])
):
    """Get bills with filtering and pagination."""
    
    search_request = BillSearchRequestDTO(
        worker_user_id=worker_user_id,
        status=status,
        bill_date=bill_date,
        start_date=start_date,
        end_date=end_date,
        skip=skip,
        limit=limit
    )
    
    return await bill_use_cases.get_bills(factory_id, search_request)


@router.get("/{bill_id}", response_model=BillResponseDTO)
@inject
async def get_bill(
    bill_id: int,
    current_user: User = Depends(get_current_active_user),
    factory_id: int = Depends(get_current_factory_from_session),
    bill_use_cases: BillUseCases = Depends(Provide[Container.bill_use_cases])
):
    """Get a bill by ID."""
    
    bill = await bill_use_cases.get_bill(bill_id, factory_id)
    
    if not bill:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Bill not found"
        )
    
    return bill


@router.put("/{bill_id}", response_model=BillOperationResponseDTO)
@inject
async def update_bill(
    bill_id: int,
    request: UpdateBillRequestDTO,
    current_user: User = Depends(get_current_active_user),
    factory_id: int = Depends(get_current_factory_from_session),
    bill_use_cases: BillUseCases = Depends(Provide[Container.bill_use_cases])
):
    """Update a bill."""
    
    return await bill_use_cases.update_bill(bill_id, factory_id, request)


@router.post("/{bill_id}/submit", response_model=BillOperationResponseDTO)
@inject
async def submit_bill_for_review(
    bill_id: int,
    request: BillSubmitForReviewRequestDTO,
    current_user: User = Depends(get_current_active_user),
    factory_id: int = Depends(get_current_factory_from_session),
    bill_use_cases: BillUseCases = Depends(Provide[Container.bill_use_cases])
):
    """Submit a bill for review."""
    
    return await bill_use_cases.submit_bill_for_review(bill_id, factory_id)


@router.post("/{bill_id}/approve", response_model=BillOperationResponseDTO)
@inject
async def approve_bill(
    bill_id: int,
    request: BillApprovalRequestDTO,
    current_user: User = Depends(get_current_active_user),
    factory_id: int = Depends(get_current_factory_from_session),
    bill_use_cases: BillUseCases = Depends(Provide[Container.bill_use_cases])
):
    """Approve a bill."""
    
    return await bill_use_cases.approve_bill(
        bill_id=bill_id,
        factory_id=factory_id,
        reviewer_user_id=current_user.id,
        review_notes=request.review_notes
    )


@router.post("/{bill_id}/reject", response_model=BillOperationResponseDTO)
@inject
async def reject_bill(
    bill_id: int,
    request: BillRejectionRequestDTO,
    current_user: User = Depends(get_current_active_user),
    factory_id: int = Depends(get_current_factory_from_session),
    bill_use_cases: BillUseCases = Depends(Provide[Container.bill_use_cases])
):
    """Reject a bill."""
    
    return await bill_use_cases.reject_bill(
        bill_id=bill_id,
        factory_id=factory_id,
        reviewer_user_id=current_user.id,
        rejection_reason=request.rejection_reason
    )


@router.post("/{bill_id}/pay", response_model=BillOperationResponseDTO)
@inject
async def mark_bill_as_paid(
    bill_id: int,
    request: BillPaymentRequestDTO,
    current_user: User = Depends(get_current_active_user),
    factory_id: int = Depends(get_current_factory_from_session),
    bill_use_cases: BillUseCases = Depends(Provide[Container.bill_use_cases])
):
    """Mark a bill as paid."""
    
    return await bill_use_cases.mark_bill_as_paid(
        bill_id=bill_id,
        factory_id=factory_id,
        payer_user_id=current_user.id,
        payment_method=request.payment_method,
        payment_reference=request.payment_reference,
        payment_notes=request.payment_notes
    )


@router.post("/{bill_id}/cancel", response_model=BillOperationResponseDTO)
@inject
async def cancel_bill(
    bill_id: int,
    request: BillCancellationRequestDTO,
    current_user: User = Depends(get_current_active_user),
    factory_id: int = Depends(get_current_factory_from_session),
    bill_use_cases: BillUseCases = Depends(Provide[Container.bill_use_cases])
):
    """Cancel a bill."""
    
    return await bill_use_cases.cancel_bill(
        bill_id=bill_id,
        factory_id=factory_id,
        cancellation_reason=request.cancellation_reason
    )


@router.get("/settlement/summary", response_model=BillSettlementSummaryDTO)
@inject
async def get_settlement_summary(
    current_user: User = Depends(get_current_active_user),
    factory_id: int = Depends(get_current_factory_from_session),
    bill_use_cases: BillUseCases = Depends(Provide[Container.bill_use_cases])
):
    """Get settlement summary for the factory."""
    
    return await bill_use_cases.get_settlement_summary(factory_id)


@router.post("/instances/{instance_id}/dispute")
@inject
async def dispute_instance(
    instance_id: int,
    request: InstanceDisputeRequestDTO,
    current_user: User = Depends(get_current_active_user),
    factory_id: int = Depends(get_current_factory_from_session),
    bill_use_cases: BillUseCases = Depends(Provide[Container.bill_use_cases])
):
    """Dispute a bill instance."""
    
    result = await bill_use_cases.dispute_instance(
        instance_id=instance_id,
        factory_id=factory_id,
        dispute_reason=request.dispute_reason
    )
    
    if not result["success"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=result["message"]
        )
    
    return {"message": result["message"]}


@router.post("/instances/{instance_id}/resolve-dispute")
@inject
async def resolve_instance_dispute(
    instance_id: int,
    request: InstanceDisputeResolutionRequestDTO,
    current_user: User = Depends(get_current_active_user),
    factory_id: int = Depends(get_current_factory_from_session),
    bill_use_cases: BillUseCases = Depends(Provide[Container.bill_use_cases])
):
    """Resolve an instance dispute."""
    
    result = await bill_use_cases.resolve_instance_dispute(
        instance_id=instance_id,
        factory_id=factory_id,
        resolution_notes=request.resolution_notes,
        include_in_settlement=request.include_in_settlement
    )
    
    if not result["success"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=result["message"]
        )
    
    return {"message": result["message"]}


@router.get("/pending", response_model=BillListResponseDTO)
@inject
async def get_pending_bills(
    current_user: User = Depends(get_current_active_user),
    factory_id: int = Depends(get_current_factory_from_session),
    bill_use_cases: BillUseCases = Depends(Provide[Container.bill_use_cases])
):
    """Get all pending bills for the factory."""
    
    search_request = BillSearchRequestDTO(
        status=BillStatus.PENDING,
        skip=0,
        limit=1000
    )
    
    return await bill_use_cases.get_bills(factory_id, search_request)