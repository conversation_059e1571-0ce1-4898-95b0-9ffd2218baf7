import logging
from typing import List
from fastapi import <PERSON>Rout<PERSON>, Depends, HTTPException, status
from dependency_injector.wiring import Provide, inject

from src.infrastructure.containers import Container
from src.application.use_cases.permission_use_cases import PermissionUseCases
from src.application.dto.permission_dto import PermissionTreeDTO, PermissionListDTO
from src.presentation.api.v1.auth import get_current_active_user
from src.domain.entities.user import User

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/permissions", tags=["permissions"])


@router.get("/tree", response_model=List[PermissionTreeDTO])
@inject
async def get_permission_tree(
    current_user: User = Depends(get_current_active_user),
    permission_use_cases: PermissionUseCases = Depends(Provide[Container.permission_use_cases])
):
    """Get hierarchical permission tree."""
    try:
        logger.info(f"User {current_user.username} requesting permission tree")
        
        # Check user permissions
        user_permissions = current_user.get_permission_codes()
        logger.info(f"User permissions: {user_permissions}")
        
        # Check if user has permission to view permissions or is admin
        has_permission = current_user.has_permission("permissions.view") or current_user.has_permission("ADMIN")
        logger.info(f"Permission check result: {has_permission}")
        
        if not has_permission:
            logger.warning(f"User {current_user.username} denied access to permissions tree")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to view permissions"
            )
        
        logger.info("Fetching permission tree from use cases")
        permission_tree = await permission_use_cases.get_permission_tree()
        logger.info(f"Retrieved {len(permission_tree) if permission_tree else 0} permission tree items")
        return permission_tree
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving permission tree: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve permission tree: {str(e)}"
        )


@router.get("/list", response_model=PermissionListDTO)
@inject
async def get_all_permissions(
    current_user: User = Depends(get_current_active_user),
    permission_use_cases: PermissionUseCases = Depends(Provide[Container.permission_use_cases])
):
    """Get all permissions as a flat list."""
    try:
        # Check if user has permission to view permissions
        if not current_user.has_permission("permissions.view"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to view permissions"
            )
        
        permissions = await permission_use_cases.get_all_permissions()
        return permissions
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve permissions"
        )