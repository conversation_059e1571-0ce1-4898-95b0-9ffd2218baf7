from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from dependency_injector.wiring import Provide, inject

from src.infrastructure.containers import Container
from src.infrastructure.logging.logger import get_logger
from src.application.use_cases.order_part_use_cases import OrderPartUseCases
from src.application.dto.order_part_dto import (
    OrderPartCreateDTO, OrderPartUpdateDTO, OrderPartResponseDTO, OrderPartWithBundlesDTO,
    OrderPartListDTO, OrderPartSearchDTO, OrderPartStatusUpdateDTO, BulkOrderPartCreateDTO,
    OrderPartOperationResultDTO, OrderPartStatisticsDTO
)
from src.presentation.api.v1.auth import get_current_active_user
from src.presentation.api.v1.factory_management import get_current_factory_from_session
from src.domain.entities.user import User

router = APIRouter(prefix="/order-parts", tags=["order-parts"])
logger = get_logger(__name__)


@router.post("/", response_model=OrderPartResponseDTO)
@inject
async def create_order_part(
    part_data: OrderPartCreateDTO,
    current_user: User = Depends(get_current_active_user),
    current_factory_id: int = Depends(get_current_factory_from_session),
    order_part_use_cases: OrderPartUseCases = Depends(Provide[Container.order_part_use_cases])
):
    """Create a new order part."""
    logger.info(
        "Creating new order part",
        user_id=current_user.id,
        factory_id=current_factory_id,
        order_no=part_data.order_no,
        part_name=part_data.part_name,
        part_type=part_data.part_type
    )
    
    try:
        # Check if user has permission to create order parts
        if not current_user.has_permission("order_parts.create"):
            logger.warning(
                "Permission denied for order part creation",
                user_id=current_user.id,
                required_permission="order_parts.create"
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to create order parts"
            )
        
        order_part = await order_part_use_cases.create_order_part(part_data, current_factory_id)
        
        logger.info(
            "Successfully created order part",
            order_part_id=order_part.id,
            order_part_no=order_part.order_part_no,
            created_by=current_user.id
        )
        return order_part
        
    except ValueError as e:
        logger.error(
            "ValueError in create_order_part",
            error=str(e),
            user_id=current_user.id,
            order_no=part_data.order_no
        )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in create_order_part",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            order_no=part_data.order_no,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create order part: {str(e)}"
        )


@router.get("/", response_model=OrderPartListDTO)
@inject
async def search_order_parts(
    search_term: Optional[str] = Query(None, description="Search by order_part_no, part_name, etc."),
    order_no: Optional[str] = Query(None, description="Filter by order number"),
    part_type: Optional[str] = Query(None, description="Filter by part type"),
    status_filter: Optional[str] = Query(None, description="Filter by part status"),
    supervisor_user_id: Optional[int] = Query(None, description="Filter by supervisor user ID"),
    color: Optional[str] = Query(None, description="Filter by color"),
    skip: int = Query(0, ge=0, description="Skip items"),
    limit: int = Query(100, ge=1, le=1000, description="Limit items"),
    current_user: User = Depends(get_current_active_user),
    current_factory_id: int = Depends(get_current_factory_from_session),
    order_part_use_cases: OrderPartUseCases = Depends(Provide[Container.order_part_use_cases])
):
    """Search order parts with optional filtering."""
    logger.info(
        "Searching order parts",
        user_id=current_user.id,
        factory_id=current_factory_id,
        search_term=search_term,
        order_no=order_no,
        part_type=part_type,
        status_filter=status_filter
    )
    
    try:
        # Check if user has permission to view order parts
        if not current_user.has_any_permission(["order_parts.view", "order_parts.manage"]):
            logger.warning(
                "Permission denied for viewing order parts",
                user_id=current_user.id,
                required_permissions=["order_parts.view", "order_parts.manage"]
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to view order parts"
            )
        
        search_criteria = None
        if any([search_term, order_no, part_type, status_filter, supervisor_user_id, color]):
            search_criteria = OrderPartSearchDTO(
                search_term=search_term,
                order_no=order_no,
                part_type=part_type,
                status=status_filter,
                supervisor_user_id=supervisor_user_id,
                color=color
            )
        
        order_parts = await order_part_use_cases.get_all_order_parts(current_factory_id, search_criteria, skip, limit)
        
        logger.info(
            "Successfully retrieved order parts",
            user_id=current_user.id,
            factory_id=current_factory_id,
            total_order_parts=order_parts.total
        )
        return order_parts
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in search_order_parts",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            factory_id=current_factory_id,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to search order parts: {str(e)}"
        )


@router.get("/order/{order_no}", response_model=List[OrderPartResponseDTO])
@inject
async def get_order_parts_by_order(
    order_no: str,
    current_user: User = Depends(get_current_active_user),
    current_factory_id: int = Depends(get_current_factory_from_session),
    order_part_use_cases: OrderPartUseCases = Depends(Provide[Container.order_part_use_cases])
):
    """Get all order parts for a specific order."""
    logger.info("Getting order parts by order", user_id=current_user.id, factory_id=current_factory_id, order_no=order_no)
    
    try:
        # Check if user has permission to view order parts
        if not current_user.has_any_permission(["order_parts.view", "order_parts.manage"]):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to view order parts"
            )
        
        order_parts = await order_part_use_cases.get_order_parts_by_order(order_no, current_factory_id)
        
        logger.info(
            "Successfully retrieved order parts for order",
            user_id=current_user.id,
            factory_id=current_factory_id,
            order_no=order_no,
            total_order_parts=len(order_parts)
        )
        return order_parts
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in get_order_parts_by_order",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            factory_id=current_factory_id,
            order_no=order_no,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve order parts: {str(e)}"
        )


@router.get("/{order_part_id}", response_model=OrderPartResponseDTO)
@inject
async def get_order_part_by_id(
    order_part_id: int,
    current_user: User = Depends(get_current_active_user),
    order_part_use_cases: OrderPartUseCases = Depends(Provide[Container.order_part_use_cases])
):
    """Get order part by ID."""
    logger.info("Getting order part by ID", user_id=current_user.id, order_part_id=order_part_id)
    
    try:
        # Check if user has permission to view order parts
        if not current_user.has_any_permission(["order_parts.view", "order_parts.manage"]):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to view order parts"
            )
        
        order_part = await order_part_use_cases.get_order_part_by_id(order_part_id)
        if not order_part:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Order part not found"
            )
        
        logger.info(
            "Successfully retrieved order part",
            user_id=current_user.id,
            order_part_id=order_part_id,
            order_part_no=order_part.order_part_no
        )
        return order_part
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in get_order_part_by_id",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            order_part_id=order_part_id,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve order part: {str(e)}"
        )


@router.get("/{order_part_id}/with-bundles", response_model=OrderPartWithBundlesDTO)
@inject
async def get_order_part_with_bundles(
    order_part_id: int,
    current_user: User = Depends(get_current_active_user),
    order_part_use_cases: OrderPartUseCases = Depends(Provide[Container.order_part_use_cases])
):
    """Get order part by ID with order bundles."""
    logger.info("Getting order part with bundles", user_id=current_user.id, order_part_id=order_part_id)
    
    try:
        # Check if user has permission to view order parts
        if not current_user.has_any_permission(["order_parts.view", "order_parts.manage"]):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to view order parts"
            )
        
        order_part = await order_part_use_cases.get_order_part_with_bundles(order_part_id)
        if not order_part:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Order part not found"
            )
        
        logger.info(
            "Successfully retrieved order part with bundles",
            user_id=current_user.id,
            order_part_id=order_part_id,
            order_part_no=order_part.order_part_no,
            bundles_count=len(order_part.order_bundles)
        )
        return order_part
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in get_order_part_with_bundles",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            order_part_id=order_part_id,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve order part with bundles: {str(e)}"
        )


@router.put("/{order_part_id}", response_model=OrderPartResponseDTO)
@inject
async def update_order_part(
    order_part_id: int,
    part_data: OrderPartUpdateDTO,
    current_user: User = Depends(get_current_active_user),
    order_part_use_cases: OrderPartUseCases = Depends(Provide[Container.order_part_use_cases])
):
    """Update order part."""
    logger.info(
        "Updating order part",
        user_id=current_user.id,
        order_part_id=order_part_id,
        update_data=part_data.model_dump(exclude_unset=True)
    )
    
    try:
        # Check if user has permission to update order parts
        if not current_user.has_permission("order_parts.update"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to update order parts"
            )
        
        order_part = await order_part_use_cases.update_order_part(order_part_id, part_data)
        if not order_part:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Order part not found"
            )
        
        logger.info(
            "Successfully updated order part",
            user_id=current_user.id,
            order_part_id=order_part_id,
            order_part_no=order_part.order_part_no
        )
        return order_part
        
    except ValueError as e:
        logger.error(
            "ValueError in update_order_part",
            error=str(e),
            user_id=current_user.id,
            order_part_id=order_part_id
        )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in update_order_part",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            order_part_id=order_part_id,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update order part: {str(e)}"
        )


@router.put("/{order_part_id}/status", response_model=OrderPartOperationResultDTO)
@inject
async def update_order_part_status(
    order_part_id: int,
    status_data: OrderPartStatusUpdateDTO,
    current_user: User = Depends(get_current_active_user),
    order_part_use_cases: OrderPartUseCases = Depends(Provide[Container.order_part_use_cases])
):
    """Update order part status."""
    logger.info(
        "Updating order part status",
        user_id=current_user.id,
        order_part_id=order_part_id,
        new_status=status_data.status
    )
    
    try:
        # Check if user has permission to manage order parts
        if not current_user.has_permission("order_parts.manage"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to manage order part status"
            )
        
        result = await order_part_use_cases.update_order_part_status(order_part_id, status_data)
        
        logger.info(
            "Order part status update completed",
            user_id=current_user.id,
            order_part_id=order_part_id,
            success=result.success,
            message=result.message
        )
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in update_order_part_status",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            order_part_id=order_part_id,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update order part status: {str(e)}"
        )


@router.delete("/{order_part_id}")
@inject
async def delete_order_part(
    order_part_id: int,
    current_user: User = Depends(get_current_active_user),
    order_part_use_cases: OrderPartUseCases = Depends(Provide[Container.order_part_use_cases])
):
    """Delete order part and all its bundles."""
    logger.info("Deleting order part", user_id=current_user.id, order_part_id=order_part_id)
    
    try:
        # Check if user has permission to delete order parts
        if not current_user.has_permission("order_parts.delete"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to delete order parts"
            )
        
        result = await order_part_use_cases.delete_order_part(order_part_id)
        if not result:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Order part not found"
            )
        
        logger.info(
            "Successfully deleted order part",
            user_id=current_user.id,
            order_part_id=order_part_id
        )
        return {"success": True, "message": "Order part deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in delete_order_part",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            order_part_id=order_part_id,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete order part: {str(e)}"
        )


@router.post("/bulk", response_model=List[OrderPartResponseDTO])
@inject
async def bulk_create_order_parts(
    bulk_data: BulkOrderPartCreateDTO,
    current_user: User = Depends(get_current_active_user),
    current_factory_id: int = Depends(get_current_factory_from_session),
    order_part_use_cases: OrderPartUseCases = Depends(Provide[Container.order_part_use_cases])
):
    """Create multiple order parts at once."""
    logger.info(
        "Bulk creating order parts",
        user_id=current_user.id,
        factory_id=current_factory_id,
        order_no=bulk_data.order_no,
        parts_count=len(bulk_data.order_parts)
    )
    
    try:
        # Check if user has permission to create order parts
        if not current_user.has_permission("order_parts.create"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to create order parts"
            )
        
        results = await order_part_use_cases.bulk_create_order_parts(bulk_data, current_factory_id)
        
        logger.info(
            "Bulk order part creation completed",
            user_id=current_user.id,
            factory_id=current_factory_id,
            order_no=bulk_data.order_no,
            total_parts=len(results)
        )
        return results
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in bulk_create_order_parts",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            factory_id=current_factory_id,
            order_no=bulk_data.order_no,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create order parts: {str(e)}"
        )


@router.get("/statistics/summary", response_model=OrderPartStatisticsDTO)
@inject
async def get_order_part_statistics(
    order_no: Optional[str] = Query(None, description="Filter by order number"),
    current_user: User = Depends(get_current_active_user),
    current_factory_id: int = Depends(get_current_factory_from_session),
    order_part_use_cases: OrderPartUseCases = Depends(Provide[Container.order_part_use_cases])
):
    """Get order part statistics summary."""
    logger.info("Getting order part statistics", user_id=current_user.id, factory_id=current_factory_id, order_no=order_no)
    
    try:
        # Check if user has permission to view statistics
        if not current_user.has_any_permission(["order_parts.view", "order_parts.manage", "statistics.view"]):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to view statistics"
            )
        
        statistics = await order_part_use_cases.get_order_part_statistics(current_factory_id, order_no)
        
        logger.info(
            "Successfully retrieved order part statistics",
            user_id=current_user.id,
            factory_id=current_factory_id,
            order_no=order_no
        )
        return statistics
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in get_order_part_statistics",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            factory_id=current_factory_id,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve statistics: {str(e)}"
        )