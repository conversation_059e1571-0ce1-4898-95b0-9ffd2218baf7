from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from dependency_injector.wiring import Provide, inject

from src.infrastructure.containers import Container
from src.infrastructure.logging.logger import get_logger
from src.application.use_cases.department_use_cases import DepartmentUseCases
from src.application.dto.department_dto import (
    DepartmentCreateDTO, DepartmentUpdateDTO, DepartmentResponseDTO,
    DepartmentListDTO, DepartmentSearchDTO, DepartmentOperationResultDTO
)
from src.presentation.api.v1.auth import get_current_active_user
from src.presentation.api.v1.factory_management import get_current_factory_from_session
from src.domain.entities.user import User

router = APIRouter(prefix="/departments", tags=["departments"])
logger = get_logger(__name__)


@router.post("/", response_model=DepartmentResponseDTO, status_code=status.HTTP_201_CREATED)
@inject
async def create_department(
    department_data: DepartmentCreateDTO,
    current_user: User = Depends(get_current_active_user),
    current_factory_id: int = Depends(get_current_factory_from_session),
    department_use_cases: DepartmentUseCases = Depends(Provide[Container.department_use_cases])
):
    """Create a new department."""
    logger.info(
        "Creating department",
        user_id=current_user.id,
        factory_id=current_factory_id,
        department_name=department_data.name,
        department_code=department_data.code
    )
    
    try:
        # Check if user has permission to create departments
        if not current_user.has_permission("departments.create"):
            logger.warning(
                "Permission denied for department creation",
                user_id=current_user.id,
                required_permission="departments.create"
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to create departments"
            )
        
        # Set operator_id to current user
        department_data.operator_id = current_user.id
        
        department = await department_use_cases.create_department(department_data, current_factory_id)
        
        logger.info(
            "Successfully created department",
            department_id=department.id,
            department_name=department.name,
            created_by=current_user.id
        )
        return department
        
    except ValueError as e:
        logger.warning(
            "Department creation failed",
            user_id=current_user.id,
            factory_id=current_factory_id,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in create_department",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            factory_id=current_factory_id,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create department: {str(e)}"
        )


@router.get("/", response_model=DepartmentListDTO)
@inject
async def get_departments(
    skip: int = Query(0, ge=0, description="Skip items"),
    limit: int = Query(100, ge=1, le=1000, description="Limit items"),
    current_user: User = Depends(get_current_active_user),
    current_factory_id: int = Depends(get_current_factory_from_session),
    department_use_cases: DepartmentUseCases = Depends(Provide[Container.department_use_cases])
):
    """Get departments for current factory."""
    logger.info(
        "Getting departments",
        user_id=current_user.id,
        factory_id=current_factory_id,
        skip=skip,
        limit=limit
    )
    
    try:
        departments = await department_use_cases.get_departments_by_factory(
            current_factory_id, skip, limit
        )
        
        logger.info(
            "Successfully retrieved departments",
            user_id=current_user.id,
            factory_id=current_factory_id,
            count=len(departments.departments)
        )
        return departments
        
    except Exception as e:
        logger.error(
            "Unexpected error in get_departments",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            factory_id=current_factory_id,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve departments: {str(e)}"
        )


@router.get("/search", response_model=DepartmentListDTO)
@inject
async def search_departments(
    search_term: Optional[str] = Query(None, description="Search by name or code"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    manager_name: Optional[str] = Query(None, description="Filter by manager name"),
    skip: int = Query(0, ge=0, description="Skip items"),
    limit: int = Query(100, ge=1, le=1000, description="Limit items"),
    current_user: User = Depends(get_current_active_user),
    current_factory_id: int = Depends(get_current_factory_from_session),
    department_use_cases: DepartmentUseCases = Depends(Provide[Container.department_use_cases])
):
    """Search departments with optional filtering."""
    logger.info(
        "Searching departments",
        user_id=current_user.id,
        factory_id=current_factory_id,
        search_term=search_term,
        is_active=is_active,
        manager_name=manager_name
    )
    
    try:
        search_criteria = DepartmentSearchDTO(
            search_term=search_term,
            is_active=is_active,
            manager_name=manager_name
        )
        
        departments = await department_use_cases.search_departments(
            current_factory_id, search_criteria, skip, limit
        )
        
        logger.info(
            "Successfully searched departments",
            user_id=current_user.id,
            factory_id=current_factory_id,
            total_found=departments.total,
            returned_count=len(departments.departments)
        )
        return departments
        
    except Exception as e:
        logger.error(
            "Unexpected error in search_departments",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            factory_id=current_factory_id,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to search departments: {str(e)}"
        )


@router.get("/{department_id}", response_model=DepartmentResponseDTO)
@inject
async def get_department_by_id(
    department_id: int,
    current_user: User = Depends(get_current_active_user),
    current_factory_id: int = Depends(get_current_factory_from_session),
    department_use_cases: DepartmentUseCases = Depends(Provide[Container.department_use_cases])
):
    """Get department by ID."""
    logger.info(
        "Getting department by ID",
        user_id=current_user.id,
        department_id=department_id,
        factory_id=current_factory_id
    )
    
    try:
        department = await department_use_cases.get_department_by_id(department_id)
        
        if not department:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Department with ID {department_id} not found"
            )
        
        # Check if department belongs to current factory
        if department.factory_id != current_factory_id:
            logger.warning(
                "Access denied: department not in current factory",
                user_id=current_user.id,
                department_id=department_id,
                department_factory_id=department.factory_id,
                current_factory_id=current_factory_id
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied: department not in current factory"
            )
        
        logger.info(
            "Successfully retrieved department",
            user_id=current_user.id,
            department_id=department_id
        )
        return department
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in get_department_by_id",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            department_id=department_id,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve department: {str(e)}"
        )


@router.put("/{department_id}", response_model=DepartmentResponseDTO)
@inject
async def update_department(
    department_id: int,
    department_data: DepartmentUpdateDTO,
    current_user: User = Depends(get_current_active_user),
    current_factory_id: int = Depends(get_current_factory_from_session),
    department_use_cases: DepartmentUseCases = Depends(Provide[Container.department_use_cases])
):
    """Update a department."""
    logger.info(
        "Updating department",
        user_id=current_user.id,
        department_id=department_id,
        factory_id=current_factory_id
    )

    try:
        # Check if user has permission to update departments
        if not current_user.has_permission("departments.update"):
            logger.warning(
                "Permission denied for department update",
                user_id=current_user.id,
                required_permission="departments.update"
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to update departments"
            )

        # First check if department exists and belongs to current factory
        existing_department = await department_use_cases.get_department_by_id(department_id)
        if not existing_department:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Department with ID {department_id} not found"
            )

        if existing_department.factory_id != current_factory_id:
            logger.warning(
                "Access denied: department not in current factory",
                user_id=current_user.id,
                department_id=department_id,
                department_factory_id=existing_department.factory_id,
                current_factory_id=current_factory_id
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied: department not in current factory"
            )

        # Set operator_id to current user
        department_data.operator_id = current_user.id

        updated_department = await department_use_cases.update_department(department_id, department_data)

        if not updated_department:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Department with ID {department_id} not found"
            )

        logger.info(
            "Successfully updated department",
            department_id=department_id,
            updated_by=current_user.id
        )
        return updated_department

    except ValueError as e:
        logger.warning(
            "Department update failed",
            user_id=current_user.id,
            department_id=department_id,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in update_department",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            department_id=department_id,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update department: {str(e)}"
        )


@router.delete("/{department_id}", response_model=DepartmentOperationResultDTO)
@inject
async def delete_department(
    department_id: int,
    current_user: User = Depends(get_current_active_user),
    current_factory_id: int = Depends(get_current_factory_from_session),
    department_use_cases: DepartmentUseCases = Depends(Provide[Container.department_use_cases])
):
    """Delete a department."""
    logger.info(
        "Deleting department",
        user_id=current_user.id,
        department_id=department_id,
        factory_id=current_factory_id
    )

    try:
        # Check if user has permission to delete departments
        if not current_user.has_permission("departments.delete"):
            logger.warning(
                "Permission denied for department deletion",
                user_id=current_user.id,
                required_permission="departments.delete"
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to delete departments"
            )

        # First check if department exists and belongs to current factory
        existing_department = await department_use_cases.get_department_by_id(department_id)
        if not existing_department:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Department with ID {department_id} not found"
            )

        if existing_department.factory_id != current_factory_id:
            logger.warning(
                "Access denied: department not in current factory",
                user_id=current_user.id,
                department_id=department_id,
                department_factory_id=existing_department.factory_id,
                current_factory_id=current_factory_id
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied: department not in current factory"
            )

        result = await department_use_cases.delete_department(department_id, current_user.id)

        if not result.success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result.message
            )

        logger.info(
            "Successfully deleted department",
            department_id=department_id,
            deleted_by=current_user.id
        )
        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in delete_department",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            department_id=department_id,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete department: {str(e)}"
        )
