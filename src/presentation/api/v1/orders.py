from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from dependency_injector.wiring import Provide, inject

from src.infrastructure.containers import Container
from src.infrastructure.logging.logger import get_logger
from src.application.use_cases.order_use_cases import OrderUseCases
from src.application.dto.order_dto import (
    OrderCreateDTO, OrderUpdateDTO, OrderResponseDTO, OrderDetailResponseDTO,
    OrderListDTO, OrderSearchDTO, OrderStatusUpdateDTO, OrderCraftProgressDTO,
    OrderProductionUpdateDTO, BulkOrderLineCreateDTO, OrderOperationResultDTO,
    OrderStatisticsDTO, OrderDashboardDTO, OrderLineResponseDTO, OrderAmountUpdateDTO
)
from src.presentation.api.v1.auth import get_current_active_user
from src.domain.entities.user import User
from src.presentation.api.v1.factory_management import get_current_factory_from_session

router = APIRouter(prefix="/orders", tags=["orders"])
logger = get_logger(__name__)


@router.post("/", response_model=OrderDetailResponseDTO)
@inject
async def create_order(
    order_data: OrderCreateDTO,
    current_user: User = Depends(get_current_active_user),
    current_factory_id: int = Depends(get_current_factory_from_session),
    order_use_cases: OrderUseCases = Depends(Provide[Container.order_use_cases])
):
    """Create a new order with order lines."""
    logger.info(
        "Creating new order",
        user_id=current_user.id,
        order_no=order_data.order_no,
        skc_no=order_data.skc_no,
        order_lines_count=len(order_data.order_lines)
    )
    
    try:
        # Check if user has permission to create orders
        if not current_user.has_permission("orders.create"):
            logger.warning(
                "Permission denied for order creation",
                user_id=current_user.id,
                required_permission="orders.create"
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to create orders"
            )
        
        order = await order_use_cases.create_order(order_data, current_factory_id)
        
        logger.info(
            "Successfully created order",
            order_id=order.id,
            order_no=order.order_no,
            created_by=current_user.id
        )
        return order
        
    except ValueError as e:
        logger.error(
            "ValueError in create_order",
            error=str(e),
            user_id=current_user.id,
            order_no=order_data.order_no
        )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in create_order",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            order_no=order_data.order_no,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create order: {str(e)}"
        )


@router.get("/", response_model=OrderListDTO)
@inject
async def get_all_orders(
    search_term: Optional[str] = Query(None, description="Search by order_no, skc_no, etc."),
    status_filter: Optional[str] = Query(None, description="Filter by order status"),
    owner_user_id: Optional[int] = Query(None, description="Filter by owner user ID"),
    current_craft: Optional[str] = Query(None, description="Filter by current craft"),
    skip: int = Query(0, ge=0, description="Skip items"),
    limit: int = Query(100, ge=1, le=1000, description="Limit items"),
    current_user: User = Depends(get_current_active_user),
    order_use_cases: OrderUseCases = Depends(Provide[Container.order_use_cases])
):
    """Get all orders with optional filtering."""
    logger.info(
        "Getting all orders",
        user_id=current_user.id,
        search_term=search_term,
        status_filter=status_filter,
        owner_user_id=owner_user_id,
        current_craft=current_craft
    )
    
    try:
        # Check if user has permission to view orders
        if not current_user.has_any_permission(["orders.view", "orders.manage"]):
            logger.warning(
                "Permission denied for viewing orders",
                user_id=current_user.id,
                required_permissions=["orders.view", "orders.manage"]
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to view orders"
            )
        
        search_criteria = None
        if any([search_term, status_filter, owner_user_id, current_craft]):
            search_criteria = OrderSearchDTO(
                search_term=search_term,
                status=status_filter,
                owner_user_id=owner_user_id,
                current_craft=current_craft,
                start_date=None,
                end_date=None,
                date_field=None,
            )
        
        orders = await order_use_cases.get_all_orders(search_criteria, skip, limit)
        
        logger.info(
            "Successfully retrieved orders",
            user_id=current_user.id,
            total_orders=orders.total
        )
        return orders
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in get_all_orders",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve orders: {str(e)}"
        )


@router.get("/{order_no}", response_model=OrderDetailResponseDTO)
@inject
async def get_order_by_order_no_main(
    order_no: str,
    current_user: User = Depends(get_current_active_user),
    current_factory_id: int = Depends(get_current_factory_from_session),
    order_use_cases: OrderUseCases = Depends(Provide[Container.order_use_cases])
):
    """Get order by order number with order lines, crafts, and craft routes."""
    logger.info("Getting order by order_no", user_id=current_user.id, order_no=order_no)
    
    try:
        # Check if user has permission to view orders
        if not current_user.has_any_permission(["orders.view", "orders.manage"]):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to view orders"
            )
        
        order = await order_use_cases.get_order_by_order_no(order_no, current_factory_id)
        if not order:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Order not found"
            )
        
        logger.info(
            "Successfully retrieved order by order_no",
            user_id=current_user.id,
            order_no=order_no,
            order_id=order.id
        )
        return order
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in get_order_by_order_no_main",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            order_no=order_no,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve order: {str(e)}"
        )




@router.put("/{order_id}", response_model=OrderResponseDTO)
@inject
async def update_order(
    order_id: int,
    order_data: OrderUpdateDTO,
    current_user: User = Depends(get_current_active_user),
    order_use_cases: OrderUseCases = Depends(Provide[Container.order_use_cases])
):
    """Update order (admin or owner only)."""
    logger.info(
        "Updating order",
        user_id=current_user.id,
        order_id=order_id,
        update_data=order_data.model_dump(exclude_unset=True)
    )
    
    try:
        # Check if user has permission to update orders
        if not current_user.has_permission("orders.update"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to update orders"
            )
        
        order = await order_use_cases.update_order(order_id, order_data)
        if not order:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Order not found"
            )
        
        logger.info(
            "Successfully updated order",
            user_id=current_user.id,
            order_id=order_id,
            order_no=order.order_no
        )
        return order
        
    except ValueError as e:
        logger.error(
            "ValueError in update_order",
            error=str(e),
            user_id=current_user.id,
            order_id=order_id
        )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in update_order",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            order_id=order_id,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update order: {str(e)}"
        )


@router.put("/{order_id}/status", response_model=OrderOperationResultDTO)
@inject
async def update_order_status(
    order_id: int,
    status_data: OrderStatusUpdateDTO,
    current_user: User = Depends(get_current_active_user),
    order_use_cases: OrderUseCases = Depends(Provide[Container.order_use_cases])
):
    """Update order status."""
    logger.info(
        "Updating order status",
        user_id=current_user.id,
        order_id=order_id,
        new_status=status_data.status
    )
    
    try:
        # Check if user has permission to manage orders
        if not current_user.has_permission("orders.manage"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to manage order status"
            )
        
        result = await order_use_cases.update_order_status(order_id, status_data)
        
        logger.info(
            "Order status update completed",
            user_id=current_user.id,
            order_id=order_id,
            success=result.success,
            message=result.message
        )
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in update_order_status",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            order_id=order_id,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update order status: {str(e)}"
        )


@router.post("/{order_no}/start", response_model=OrderOperationResultDTO)
@inject
async def start_order(
    order_no: str,
    current_user: User = Depends(get_current_active_user),
    current_factory_id: int = Depends(get_current_factory_from_session),
    order_use_cases: OrderUseCases = Depends(Provide[Container.order_use_cases])
):
    """Start an order - change status from PENDING to IN_PROGRESS."""
    logger.info(
        "Starting order",
        user_id=current_user.id,
        order_no=order_no,
        factory_id=current_factory_id
    )
    
    try:
        # Check if user has permission to start orders
        if not current_user.has_permission("orders.manage"):
            logger.warning(
                "Permission denied for starting order",
                user_id=current_user.id,
                required_permission="orders.manage"
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to start orders"
            )

        result = await order_use_cases.start_order(order_no)
        
        if result.success:
            logger.info(
                "Order started successfully",
                user_id=current_user.id,
                order_no=order_no,
                order_id=result.order_id
            )
        else:
            logger.warning(
                "Failed to start order",
                user_id=current_user.id,
                order_no=order_no,
                error=result.message
            )
        
        return result
        
    except Exception as e:
        logger.error(
            "Unexpected error in start_order",
            error=str(e),
            error_type=type(e).__name__,
            order_no=order_no,
            user_id=current_user.id
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start order: {str(e)}"
        )


@router.put("/{order_id}/craft-progress", response_model=OrderOperationResultDTO)
@inject
async def update_craft_progress(
    order_id: int,
    progress_data: OrderCraftProgressDTO,
    current_user: User = Depends(get_current_active_user),
    order_use_cases: OrderUseCases = Depends(Provide[Container.order_use_cases])
):
    """Update order craft progress."""
    logger.info(
        "Updating order craft progress",
        user_id=current_user.id,
        order_id=order_id,
        craft_code=progress_data.craft_code,
        craft_route_id=progress_data.craft_route_id
    )
    
    try:
        # Check if user has permission to manage orders
        if not current_user.has_permission("orders.manage"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to update craft progress"
            )
        
        result = await order_use_cases.update_craft_progress(order_id, progress_data)
        
        logger.info(
            "Craft progress update completed",
            user_id=current_user.id,
            order_id=order_id,
            success=result.success,
            message=result.message
        )
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in update_craft_progress",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            order_id=order_id,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update craft progress: {str(e)}"
        )


@router.put("/{order_id}/amount", response_model=OrderOperationResultDTO)
@inject
async def update_order_amount(
    order_id: int,
    amount_data: OrderAmountUpdateDTO,
    current_user: User = Depends(get_current_active_user),
    order_use_cases: OrderUseCases = Depends(Provide[Container.order_use_cases])
):
    """Update order total amount."""
    logger.info(
        "Updating order amount",
        user_id=current_user.id,
        order_id=order_id,
        new_amount=amount_data.total_amount
    )
    
    try:
        # Check if user has permission to manage orders
        if not current_user.has_permission("orders.manage"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to update order amount"
            )
        
        result = await order_use_cases.update_order_amount(order_id, amount_data)
        
        logger.info(
            "Order amount update completed",
            user_id=current_user.id,
            order_id=order_id,
            success=result.success,
            message=result.message
        )
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in update_order_amount",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            order_id=order_id,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update order amount: {str(e)}"
        )


@router.delete("/{order_id}")
@inject
async def delete_order(
    order_id: int,
    current_user: User = Depends(get_current_active_user),
    order_use_cases: OrderUseCases = Depends(Provide[Container.order_use_cases])
):
    """Delete order (admin only)."""
    logger.info("Deleting order", user_id=current_user.id, order_id=order_id)
    
    try:
        # Check if user has permission to delete orders
        if not current_user.has_permission("orders.delete"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to delete orders"
            )
        
        deleted = await order_use_cases.delete_order(order_id)
        if not deleted:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Order not found"
            )
        
        logger.info(
            "Successfully deleted order",
            user_id=current_user.id,
            order_id=order_id
        )
        return {"success": True, "message": "Order deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in delete_order",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            order_id=order_id,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete order: {str(e)}"
        )


# Order Lines endpoints
@router.post("/order-lines/bulk", response_model=List[OrderLineResponseDTO])
@inject
async def add_order_lines(
    bulk_data: BulkOrderLineCreateDTO,
    current_user: User = Depends(get_current_active_user),
    current_factory_id: int = Depends(get_current_factory_from_session),
    order_use_cases: OrderUseCases = Depends(Provide[Container.order_use_cases])
):
    """Add multiple order lines to an existing order."""
    logger.info(
        "Adding order lines",
        user_id=current_user.id,
        order_no=bulk_data.order_no,
        lines_count=len(bulk_data.order_lines)
    )
    
    try:
        # Check if user has permission to manage orders
        if not current_user.has_permission("orders.manage"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to add order lines"
            )
        
        order_lines = await order_use_cases.add_order_lines(bulk_data, current_factory_id)
        
        logger.info(
            "Successfully added order lines",
            user_id=current_user.id,
            order_no=bulk_data.order_no,
            added_lines=len(order_lines)
        )
        return order_lines
        
    except ValueError as e:
        logger.error(
            "ValueError in add_order_lines",
            error=str(e),
            user_id=current_user.id,
            order_no=bulk_data.order_no
        )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in add_order_lines",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            order_no=bulk_data.order_no,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to add order lines: {str(e)}"
        )


@router.put("/production", response_model=OrderOperationResultDTO)
@inject
async def update_production(
    production_data: OrderProductionUpdateDTO,
    current_user: User = Depends(get_current_active_user),
    order_use_cases: OrderUseCases = Depends(Provide[Container.order_use_cases])
):
    """Update production quantities for order lines."""
    logger.info(
        "Updating production quantities",
        user_id=current_user.id,
        updates_count=len(production_data.order_line_updates)
    )
    
    try:
        # Check if user has permission to manage production
        if not current_user.has_permission("production.manage"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to update production"
            )
        
        result = await order_use_cases.update_production(production_data)
        
        logger.info(
            "Production update completed",
            user_id=current_user.id,
            success=result.success,
            message=result.message
        )
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in update_production",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update production: {str(e)}"
        )


# Statistics and Dashboard endpoints
@router.get("/statistics/summary", response_model=OrderStatisticsDTO)
@inject
async def get_order_statistics(
    user_id: Optional[int] = Query(None, description="Filter by user ID"),
    current_user: User = Depends(get_current_active_user),
    order_use_cases: OrderUseCases = Depends(Provide[Container.order_use_cases])
):
    """Get order statistics summary."""
    logger.info("Getting order statistics", user_id=current_user.id, filter_user_id=user_id)
    
    try:
        # Check if user has permission to view statistics
        if not current_user.has_any_permission(["orders.view", "orders.manage", "statistics.view"]):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to view statistics"
            )
        
        # If filtering by user and not admin, only allow viewing own statistics
        if user_id and not current_user.has_permission("statistics.view_all"):
            if user_id != current_user.id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Permission denied: can only view your own statistics"
                )
        
        statistics = await order_use_cases.get_order_statistics(user_id)
        
        logger.info(
            "Successfully retrieved order statistics",
            user_id=current_user.id,
            total_orders=statistics.total_orders
        )
        return statistics
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in get_order_statistics",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve statistics: {str(e)}"
        )


@router.get("/dashboard/data", response_model=OrderDashboardDTO)
@inject
async def get_dashboard_data(
    user_id: Optional[int] = Query(None, description="Filter by user ID"),
    current_user: User = Depends(get_current_active_user),
    order_use_cases: OrderUseCases = Depends(Provide[Container.order_use_cases])
):
    """Get comprehensive dashboard data."""
    logger.info("Getting dashboard data", user_id=current_user.id, filter_user_id=user_id)
    
    try:
        # Check if user has permission to view dashboard
        if not current_user.has_any_permission(["orders.view", "orders.manage", "dashboard.view"]):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to view dashboard"
            )
        
        # If filtering by user and not admin, only allow viewing own data
        if user_id and not current_user.has_permission("dashboard.view_all"):
            if user_id != current_user.id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Permission denied: can only view your own dashboard"
                )
        
        dashboard_data = await order_use_cases.get_dashboard_data(user_id)
        
        logger.info(
            "Successfully retrieved dashboard data",
            user_id=current_user.id,
            total_orders=dashboard_data.order_statistics.total_orders
        )
        return dashboard_data
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in get_dashboard_data",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve dashboard data: {str(e)}"
        )