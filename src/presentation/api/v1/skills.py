from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from dependency_injector.wiring import Provide, inject

from src.infrastructure.containers import Container
from src.infrastructure.logging.logger import get_logger
from src.application.use_cases.skill_use_cases import SkillUseCases
from src.application.dto.skill_dto import (
    SkillCreateDTO, SkillUpdateDTO, SkillResponseDTO, SkillListDTO,
    UserSkillsDTO, AssignSkillsDTO, UpdateSkillProficiencyDTO,
    CertifySkillDTO, RemoveSkillDTO, SkillSearchDTO, SkillOperationResultDTO
)
from src.presentation.api.v1.auth import get_current_active_user
from src.presentation.api.v1.factory_management import get_current_factory_from_session
from src.domain.entities.user import User

router = APIRouter(prefix="/skills", tags=["skills"])
logger = get_logger(__name__)


@router.post("/", response_model=SkillResponseDTO, status_code=status.HTTP_201_CREATED)
@inject
async def create_skill(
    skill_data: Skill<PERSON>reateDTO,
    current_user: User = Depends(get_current_active_user),
    skill_use_cases: SkillUseCases = Depends(Provide[Container.skill_use_cases])
):
    """Create a new skill."""
    logger.info(
        "Creating new skill",
        user_id=current_user.id,
        skill_code=skill_data.code,
        skill_name=skill_data.name,
        skill_category=skill_data.category
    )
    
    try:
        # Check if user has permission to create skills
        if not current_user.has_permission("skills.create"):
            logger.warning(
                "Permission denied for skill creation",
                user_id=current_user.id,
                required_permission="skills.create",
                skill_code=skill_data.code
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to create skills"
            )
        
        skill = await skill_use_cases.create_skill(skill_data)
        
        logger.info(
            "Successfully created skill",
            skill_id=skill.id,
            skill_code=skill.code,
            skill_name=skill.name,
            created_by=current_user.id
        )
        return skill
        
    except HTTPException:
        raise
    except ValueError as e:
        logger.error(
            "ValueError in create_skill",
            error=str(e),
            user_id=current_user.id,
            skill_code=skill_data.code
        )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(
            "Unexpected error in create_skill",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            skill_code=skill_data.code,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create skill: {str(e)}"
        )


@router.get("/", response_model=SkillListDTO)
@inject
async def get_all_skills(
    search_term: Optional[str] = Query(None, description="Search by code, name, or description"),
    category: Optional[str] = Query(None, description="Filter by category"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    current_user: User = Depends(get_current_active_user),
    skill_use_cases: SkillUseCases = Depends(Provide[Container.skill_use_cases])
):
    """Get all skills with optional filters."""
    logger.info(
        "Getting all skills",
        user_id=current_user.id,
        search_term=search_term,
        category=category,
        is_active=is_active
    )
    
    try:
        # Check if user has permission to view skills
        if not current_user.has_permission("skills.view"):
            logger.warning(
                "Permission denied for viewing skills",
                user_id=current_user.id,
                required_permission="skills.view"
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to view skills"
            )
        
        search_criteria = SkillSearchDTO(
            search_term=search_term,
            category=category,
            is_active=is_active
        ) if any([search_term, category, is_active is not None]) else None
        
        logger.debug(
            "Calling skill_use_cases.get_all_skills",
            user_id=current_user.id,
            has_search_criteria=search_criteria is not None,
            search_criteria=search_criteria.model_dump() if search_criteria else None
        )
        
        skills = await skill_use_cases.get_all_skills(search_criteria)
        
        logger.info(
            "Successfully retrieved skills",
            user_id=current_user.id,
            total_skills=skills.total,
            returned_skills=len(skills.skills)
        )
        return skills
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in get_all_skills",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            search_term=search_term,
            category=category,
            is_active=is_active,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve skills: {str(e)}"
        )


@router.get("/active", response_model=SkillListDTO)
@inject
async def get_active_skills(
    current_user: User = Depends(get_current_active_user),
    skill_use_cases: SkillUseCases = Depends(Provide[Container.skill_use_cases])
):
    """Get all active skills."""
    logger.info("Getting active skills", user_id=current_user.id)
    
    try:
        # Check if user has permission to view skills
        if not current_user.has_permission("skills.view"):
            logger.warning(
                "Permission denied for viewing active skills",
                user_id=current_user.id,
                required_permission="skills.view"
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to view skills"
            )
        
        logger.debug("Calling skill_use_cases.get_active_skills", user_id=current_user.id)
        skills = await skill_use_cases.get_active_skills()
        
        logger.info(
            "Successfully retrieved active skills",
            user_id=current_user.id,
            total_active_skills=skills.total,
            returned_skills=len(skills.skills)
        )
        return skills
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in get_active_skills",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve active skills: {str(e)}"
        )


@router.get("/{skill_id}", response_model=SkillResponseDTO)
@inject
async def get_skill_by_id(
    skill_id: int,
    current_user: User = Depends(get_current_active_user),
    skill_use_cases: SkillUseCases = Depends(Provide[Container.skill_use_cases])
):
    """Get skill by ID."""
    logger.info("Getting skill by ID", user_id=current_user.id, skill_id=skill_id)
    
    try:
        # Check if user has permission to view skills
        if not current_user.has_permission("skills.view"):
            logger.warning(
                "Permission denied for viewing skill",
                user_id=current_user.id,
                skill_id=skill_id,
                required_permission="skills.view"
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to view skills"
            )
        
        logger.debug("Calling skill_use_cases.get_skill_by_id", user_id=current_user.id, skill_id=skill_id)
        skill = await skill_use_cases.get_skill_by_id(skill_id)
        
        if not skill:
            logger.warning(
                "Skill not found",
                user_id=current_user.id,
                skill_id=skill_id
            )
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Skill not found"
            )
        
        logger.info(
            "Successfully retrieved skill",
            user_id=current_user.id,
            skill_id=skill_id,
            skill_code=skill.code,
            skill_name=skill.name
        )
        return skill
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in get_skill_by_id",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            skill_id=skill_id,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve skill: {str(e)}"
        )


@router.put("/{skill_id}", response_model=SkillResponseDTO)
@inject
async def update_skill(
    skill_id: int,
    skill_data: SkillUpdateDTO,
    current_user: User = Depends(get_current_active_user),
    skill_use_cases: SkillUseCases = Depends(Provide[Container.skill_use_cases])
):
    """Update skill."""
    logger.info(
        "Updating skill",
        user_id=current_user.id,
        skill_id=skill_id,
        update_data=skill_data.model_dump(exclude_unset=True)
    )
    
    try:
        # Check if user has permission to update skills
        if not current_user.has_permission("skills.update"):
            logger.warning(
                "Permission denied for skill update",
                user_id=current_user.id,
                skill_id=skill_id,
                required_permission="skills.update"
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to update skills"
            )
        
        logger.debug("Calling skill_use_cases.update_skill", user_id=current_user.id, skill_id=skill_id)
        skill = await skill_use_cases.update_skill(skill_id, skill_data)
        
        if not skill:
            logger.warning(
                "Skill not found for update",
                user_id=current_user.id,
                skill_id=skill_id
            )
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Skill not found"
            )
        
        logger.info(
            "Successfully updated skill",
            user_id=current_user.id,
            skill_id=skill_id,
            skill_code=skill.code,
            skill_name=skill.name
        )
        return skill
        
    except HTTPException:
        raise
    except ValueError as e:
        logger.error(
            "ValueError in update_skill",
            error=str(e),
            user_id=current_user.id,
            skill_id=skill_id
        )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(
            "Unexpected error in update_skill",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            skill_id=skill_id,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update skill: {str(e)}"
        )


@router.delete("/{skill_id}", status_code=status.HTTP_204_NO_CONTENT)
@inject
async def delete_skill(
    skill_id: int,
    current_user: User = Depends(get_current_active_user),
    skill_use_cases: SkillUseCases = Depends(Provide[Container.skill_use_cases])
):
    """Delete skill."""
    logger.info("Deleting skill", user_id=current_user.id, skill_id=skill_id)
    
    try:
        # Check if user has permission to delete skills
        if not current_user.has_permission("skills.delete"):
            logger.warning(
                "Permission denied for skill deletion",
                user_id=current_user.id,
                skill_id=skill_id,
                required_permission="skills.delete"
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to delete skills"
            )
        
        logger.debug("Calling skill_use_cases.delete_skill", user_id=current_user.id, skill_id=skill_id)
        deleted = await skill_use_cases.delete_skill(skill_id)
        
        if not deleted:
            logger.warning(
                "Skill not found for deletion",
                user_id=current_user.id,
                skill_id=skill_id
            )
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Skill not found"
            )
        
        logger.info(
            "Successfully deleted skill",
            user_id=current_user.id,
            skill_id=skill_id
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in delete_skill",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            skill_id=skill_id,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete skill: {str(e)}"
        )


@router.get("/user/{user_id}/skills", response_model=UserSkillsDTO)
@inject
async def get_user_skills(
    user_id: int,
    current_user: User = Depends(get_current_active_user),
    current_factory_id: int = Depends(get_current_factory_from_session),
    skill_use_cases: SkillUseCases = Depends(Provide[Container.skill_use_cases])
):
    """Get user's skills in current factory."""
    logger.info(
        "Getting user skills",
        requester_user_id=current_user.id,
        target_user_id=user_id,
        factory_id=current_factory_id
    )
    
    try:
        # Check if user has permission to view user skills
        if not current_user.has_any_permission(["factory.manage_users", "factory.view_users"]):
            logger.warning(
                "Permission denied for viewing user skills",
                requester_user_id=current_user.id,
                target_user_id=user_id,
                factory_id=current_factory_id,
                required_permissions=["factory.manage_users", "factory.view_users"]
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to view user skills"
            )
        
        logger.debug(
            "Calling skill_use_cases.get_user_skills",
            requester_user_id=current_user.id,
            target_user_id=user_id,
            factory_id=current_factory_id
        )
        
        user_skills = await skill_use_cases.get_user_skills(
            user_id, current_factory_id, current_user.id
        )
        
        if not user_skills:
            logger.warning(
                "User not found in factory",
                requester_user_id=current_user.id,
                target_user_id=user_id,
                factory_id=current_factory_id
            )
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found in factory"
            )
        
        logger.info(
            "Successfully retrieved user skills",
            requester_user_id=current_user.id,
            target_user_id=user_id,
            factory_id=current_factory_id,
            skills_count=len(user_skills.skills)
        )
        return user_skills
        
    except ValueError as e:
        logger.error(
            "ValueError in get_user_skills",
            error=str(e),
            requester_user_id=current_user.id,
            target_user_id=user_id,
            factory_id=current_factory_id
        )
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in get_user_skills",
            error=str(e),
            error_type=type(e).__name__,
            requester_user_id=current_user.id,
            target_user_id=user_id,
            factory_id=current_factory_id,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve user skills: {str(e)}"
        )


@router.post("/assign", response_model=List[SkillOperationResultDTO])
@inject
async def assign_skills_to_user(
    assign_data: AssignSkillsDTO,
    current_user: User = Depends(get_current_active_user),
    current_factory_id: int = Depends(get_current_factory_from_session),
    skill_use_cases: SkillUseCases = Depends(Provide[Container.skill_use_cases])
):
    """Assign skills to user in current factory."""
    logger.info(
        "Assigning skills to user",
        assigner_user_id=current_user.id,
        target_user_id=assign_data.user_id,
        factory_id=current_factory_id,
        skills_to_assign=len(assign_data.skill_assignments),
        skill_ids=[sa.skill_id for sa in assign_data.skill_assignments]
    )
    
    try:
        # Check if user has permission to manage user skills
        if not current_user.has_permission("factory.manage_users"):
            logger.warning(
                "Permission denied for skill assignment",
                assigner_user_id=current_user.id,
                target_user_id=assign_data.user_id,
                factory_id=current_factory_id,
                required_permission="factory.manage_users"
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to assign skills"
            )
        
        logger.debug(
            "Calling skill_use_cases.assign_skills_to_user",
            assigner_user_id=current_user.id,
            target_user_id=assign_data.user_id,
            factory_id=current_factory_id
        )
        
        results = await skill_use_cases.assign_skills_to_user(
            current_factory_id, assign_data, current_user.id
        )
        
        success_count = sum(1 for r in results if r.success)
        failure_count = len(results) - success_count
        
        logger.info(
            "Skill assignment completed",
            assigner_user_id=current_user.id,
            target_user_id=assign_data.user_id,
            factory_id=current_factory_id,
            total_assignments=len(results),
            successful_assignments=success_count,
            failed_assignments=failure_count
        )
        
        return results
        
    except ValueError as e:
        logger.error(
            "ValueError in assign_skills_to_user",
            error=str(e),
            assigner_user_id=current_user.id,
            target_user_id=assign_data.user_id,
            factory_id=current_factory_id
        )
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )
    except Exception as e:
        logger.error(
            "Unexpected error in assign_skills_to_user",
            error=str(e),
            error_type=type(e).__name__,
            assigner_user_id=current_user.id,
            target_user_id=assign_data.user_id,
            factory_id=current_factory_id,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to assign skills: {str(e)}"
        )


@router.put("/modify-proficiency", response_model=SkillOperationResultDTO)
@inject
async def modify_skill_proficiency(
    update_data: UpdateSkillProficiencyDTO,
    current_user: User = Depends(get_current_active_user),
    skill_use_cases: SkillUseCases = Depends(Provide[Container.skill_use_cases])
):
    """Modify user's skill proficiency level."""
    logger.info(
        "Modifying skill proficiency",
        modifier_user_id=current_user.id,
        user_factory_skill_id=update_data.user_factory_skill_id,
        new_proficiency=update_data.proficiency_level
    )
    
    try:
        # Check if user has permission to manage user skills
        if not current_user.has_permission("factory.manage_users"):
            logger.warning(
                "Permission denied for skill proficiency modification",
                modifier_user_id=current_user.id,
                user_factory_skill_id=update_data.user_factory_skill_id,
                required_permission="factory.manage_users"
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to modify skill proficiency"
            )
        
        logger.debug(
            "Calling skill_use_cases.update_skill_proficiency",
            modifier_user_id=current_user.id,
            user_factory_skill_id=update_data.user_factory_skill_id
        )
        
        result = await skill_use_cases.update_skill_proficiency(update_data, current_user.id)
        
        if not result.success:
            logger.warning(
                "Skill proficiency update failed",
                modifier_user_id=current_user.id,
                user_factory_skill_id=update_data.user_factory_skill_id,
                failure_reason=result.message
            )
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result.message
            )
        
        logger.info(
            "Successfully modified skill proficiency",
            modifier_user_id=current_user.id,
            user_factory_skill_id=update_data.user_factory_skill_id,
            new_proficiency=update_data.proficiency_level,
            result_message=result.message
        )
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in modify_skill_proficiency",
            error=str(e),
            error_type=type(e).__name__,
            modifier_user_id=current_user.id,
            user_factory_skill_id=update_data.user_factory_skill_id,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to modify skill proficiency: {str(e)}"
        )


@router.post("/certify", response_model=SkillOperationResultDTO)
@inject
async def certify_user_skill(
    certify_data: CertifySkillDTO,
    current_user: User = Depends(get_current_active_user),
    skill_use_cases: SkillUseCases = Depends(Provide[Container.skill_use_cases])
):
    """Certify user in skill."""
    logger.info(
        "Certifying user skill",
        certifier_user_id=current_user.id,
        user_factory_skill_id=certify_data.user_factory_skill_id,
        certification_level=certify_data.certification_level
    )
    
    try:
        # Check if user has permission to certify skills
        if not current_user.has_permission("factory.certify_skills"):
            logger.warning(
                "Permission denied for skill certification",
                certifier_user_id=current_user.id,
                user_factory_skill_id=certify_data.user_factory_skill_id,
                required_permission="factory.certify_skills"
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to certify skills"
            )
        
        logger.debug(
            "Calling skill_use_cases.certify_user_skill",
            certifier_user_id=current_user.id,
            user_factory_skill_id=certify_data.user_factory_skill_id
        )
        
        result = await skill_use_cases.certify_user_skill(certify_data, current_user.id)
        
        if not result.success:
            logger.warning(
                "Skill certification failed",
                certifier_user_id=current_user.id,
                user_factory_skill_id=certify_data.user_factory_skill_id,
                failure_reason=result.message
            )
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result.message
            )
        
        logger.info(
            "Successfully certified user skill",
            certifier_user_id=current_user.id,
            user_factory_skill_id=certify_data.user_factory_skill_id,
            certification_level=certify_data.certification_level,
            result_message=result.message
        )
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in certify_user_skill",
            error=str(e),
            error_type=type(e).__name__,
            certifier_user_id=current_user.id,
            user_factory_skill_id=certify_data.user_factory_skill_id,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to certify user skill: {str(e)}"
        )


@router.delete("/remove", response_model=SkillOperationResultDTO)
@inject
async def remove_user_skill(
    remove_data: RemoveSkillDTO,
    current_user: User = Depends(get_current_active_user),
    skill_use_cases: SkillUseCases = Depends(Provide[Container.skill_use_cases])
):
    """Remove skill from user."""
    logger.info(
        "Removing user skill",
        remover_user_id=current_user.id,
        user_factory_skill_id=remove_data.user_factory_skill_id
    )
    
    try:
        # Check if user has permission to manage user skills
        if not current_user.has_permission("factory.manage_users"):
            logger.warning(
                "Permission denied for skill removal",
                remover_user_id=current_user.id,
                user_factory_skill_id=remove_data.user_factory_skill_id,
                required_permission="factory.manage_users"
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to remove skills"
            )
        
        logger.debug(
            "Calling skill_use_cases.remove_user_skill",
            remover_user_id=current_user.id,
            user_factory_skill_id=remove_data.user_factory_skill_id
        )
        
        result = await skill_use_cases.remove_user_skill(remove_data, current_user.id)
        
        if not result.success:
            logger.warning(
                "Skill removal failed",
                remover_user_id=current_user.id,
                user_factory_skill_id=remove_data.user_factory_skill_id,
                failure_reason=result.message
            )
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result.message
            )
        
        logger.info(
            "Successfully removed user skill",
            remover_user_id=current_user.id,
            user_factory_skill_id=remove_data.user_factory_skill_id,
            result_message=result.message
        )
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in remove_user_skill",
            error=str(e),
            error_type=type(e).__name__,
            remover_user_id=current_user.id,
            user_factory_skill_id=remove_data.user_factory_skill_id,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to remove user skill: {str(e)}"
        )