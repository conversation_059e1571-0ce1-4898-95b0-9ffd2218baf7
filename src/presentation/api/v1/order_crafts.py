from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from dependency_injector.wiring import Provide, inject
from src.application.use_cases.order_craft_use_cases import OrderCraftUseCases
from src.application.dto.order_craft_dto import (
    Order<PERSON>raftCreateDTO, OrderCraftResponseDTO, Order<PERSON>raftUpdateDTO,
    OrderCraftRouteUpdateDTO, OrderCraftStatusUpdateDTO, OrderCraftRouteStatusUpdateDTO,
    Order<PERSON>raftOperationResultDTO, OrderCraftStatisticsDTO, OrderCraftWorkflowDTO
)
from src.infrastructure.containers import Container
from src.presentation.api.v1.auth import get_current_active_user
from src.presentation.api.v1.factory_management import get_current_factory_from_session
from src.domain.entities.user import User
from src.infrastructure.logging.logger import get_logger
import structlog

logger = get_logger(__name__)
router = APIRouter()


@router.post("/orders/{order_no}/crafts", response_model=List[OrderCraftResponseDTO])
@inject
async def create_order_crafts_for_order(
    order_no: str,
    workflow_data: Order<PERSON>raftWorkflowDTO,
    current_user: User = Depends(get_current_active_user),
    current_factory_id: int = Depends(get_current_factory_from_session),
    order_craft_use_cases: OrderCraftUseCases = Depends(Provide[Container.order_craft_use_cases])
):
    """Create order crafts configuration for an order."""
    try:
        logger.info(
            "Creating order crafts for order",
            user_id=current_user.id,
            order_no=order_no,
            factory_id=current_factory_id,
            craft_count=len(workflow_data.order_crafts)
        )
        
        order_crafts = await order_craft_use_cases.create_order_crafts_for_order(
            order_no, current_factory_id, workflow_data.order_crafts
        )
        
        logger.info(
            "Successfully created order crafts", 
            user_id=current_user.id,
            order_no=order_no,
            created_count=len(order_crafts)
        )
        
        return order_crafts
        
    except ValueError as e:
        logger.error(
            "Validation error in create_order_crafts_for_order",
            error=str(e),
            order_no=order_no,
            user_id=current_user.id
        )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(
            "Unexpected error in create_order_crafts_for_order",
            error=str(e),
            error_type=type(e).__name__,
            order_no=order_no,
            user_id=current_user.id
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create order crafts"
        )


@router.get("/orders/{order_no}/crafts", response_model=List[OrderCraftResponseDTO])
@inject
async def get_order_crafts_by_order(
    order_no: str,
    current_user: User = Depends(get_current_active_user),
    current_factory_id: int = Depends(get_current_factory_from_session),
    order_craft_use_cases: OrderCraftUseCases = Depends(Provide[Container.order_craft_use_cases])
):
    """Get all order crafts for an order."""
    try:
        logger.info(
            "Retrieving order crafts for order",
            user_id=current_user.id,
            order_no=order_no,
            factory_id=current_factory_id
        )
        
        order_crafts = await order_craft_use_cases.get_order_crafts_by_order(order_no)
        
        logger.info(
            "Successfully retrieved order crafts",
            user_id=current_user.id,
            order_no=order_no,
            craft_count=len(order_crafts)
        )
        
        return order_crafts
        
    except Exception as e:
        logger.error(
            "Unexpected error in get_order_crafts_by_order",
            error=str(e),
            error_type=type(e).__name__,
            order_no=order_no,
            user_id=current_user.id
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve order crafts"
        )


@router.get("/crafts/{order_craft_id}", response_model=OrderCraftResponseDTO)
@inject
async def get_order_craft_by_id(
    order_craft_id: int,
    current_user: User = Depends(get_current_active_user),
    current_factory_id: int = Depends(get_current_factory_from_session),
    order_craft_use_cases: OrderCraftUseCases = Depends(Provide[Container.order_craft_use_cases])
):
    """Get order craft by ID."""
    try:
        logger.info(
            "Retrieving order craft by ID",
            user_id=current_user.id,
            order_craft_id=order_craft_id,
            factory_id=current_factory_id
        )
        
        order_craft = await order_craft_use_cases.get_order_craft_by_id(order_craft_id)
        
        if not order_craft:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Order craft with ID {order_craft_id} not found"
            )
        
        logger.info(
            "Successfully retrieved order craft",
            user_id=current_user.id,
            order_craft_id=order_craft_id
        )
        
        return order_craft
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in get_order_craft_by_id",
            error=str(e),
            error_type=type(e).__name__,
            order_craft_id=order_craft_id,
            user_id=current_user.id
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve order craft"
        )


@router.put("/crafts/{order_craft_id}/status", response_model=OrderCraftOperationResultDTO)
@inject
async def update_order_craft_status(
    order_craft_id: int,
    status_data: OrderCraftStatusUpdateDTO,
    current_user: User = Depends(get_current_active_user),
    current_factory_id: int = Depends(get_current_factory_from_session),
    order_craft_use_cases: OrderCraftUseCases = Depends(Provide[Container.order_craft_use_cases])
):
    """Update order craft status."""
    try:
        logger.info(
            "Updating order craft status",
            user_id=current_user.id,
            order_craft_id=order_craft_id,
            new_status=status_data.status,
            factory_id=current_factory_id
        )
        
        result = await order_craft_use_cases.update_order_craft_status(
            order_craft_id, status_data
        )
        
        if not result.success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result.message
            )
        
        logger.info(
            "Successfully updated order craft status",
            user_id=current_user.id,
            order_craft_id=order_craft_id,
            new_status=status_data.status
        )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in update_order_craft_status",
            error=str(e),
            error_type=type(e).__name__,
            order_craft_id=order_craft_id,
            user_id=current_user.id
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update order craft status"
        )


@router.put("/craft-routes/{order_craft_route_id}/status", response_model=OrderCraftOperationResultDTO)
@inject
async def update_order_craft_route_status(
    order_craft_route_id: int,
    status_data: OrderCraftRouteStatusUpdateDTO,
    current_user: User = Depends(get_current_active_user),
    current_factory_id: int = Depends(get_current_factory_from_session),
    order_craft_use_cases: OrderCraftUseCases = Depends(Provide[Container.order_craft_use_cases])
):
    """Update order craft route status."""
    try:
        logger.info(
            "Updating order craft route status",
            user_id=current_user.id,
            order_craft_route_id=order_craft_route_id,
            new_status=status_data.status,
            factory_id=current_factory_id
        )
        
        result = await order_craft_use_cases.update_order_craft_route_status(
            order_craft_route_id, status_data
        )
        
        if not result.success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result.message
            )
        
        logger.info(
            "Successfully updated order craft route status",
            user_id=current_user.id,
            order_craft_route_id=order_craft_route_id,
            new_status=status_data.status
        )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in update_order_craft_route_status",
            error=str(e),
            error_type=type(e).__name__,
            order_craft_route_id=order_craft_route_id,
            user_id=current_user.id
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update order craft route status"
        )


@router.get("/orders/{order_no}/crafts/next", response_model=OrderCraftResponseDTO)
@inject
async def get_next_craft_for_order(
    order_no: str,
    current_user: User = Depends(get_current_active_user),
    current_factory_id: int = Depends(get_current_factory_from_session),
    order_craft_use_cases: OrderCraftUseCases = Depends(Provide[Container.order_craft_use_cases])
):
    """Get the next pending craft for an order."""
    try:
        logger.info(
            "Retrieving next craft for order",
            user_id=current_user.id,
            order_no=order_no,
            factory_id=current_factory_id
        )
        
        next_craft = await order_craft_use_cases.get_next_craft_for_order(order_no)
        
        if not next_craft:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"No pending crafts found for order {order_no}"
            )
        
        logger.info(
            "Successfully retrieved next craft for order",
            user_id=current_user.id,
            order_no=order_no,
            craft_id=next_craft.id
        )
        
        return next_craft
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in get_next_craft_for_order",
            error=str(e),
            error_type=type(e).__name__,
            order_no=order_no,
            user_id=current_user.id
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve next craft for order"
        )


@router.get("/orders/{order_no}/crafts/current", response_model=OrderCraftResponseDTO)
@inject
async def get_current_craft_for_order(
    order_no: str,
    current_user: User = Depends(get_current_active_user),
    current_factory_id: int = Depends(get_current_factory_from_session),
    order_craft_use_cases: OrderCraftUseCases = Depends(Provide[Container.order_craft_use_cases])
):
    """Get the currently in-progress craft for an order."""
    try:
        logger.info(
            "Retrieving current craft for order",
            user_id=current_user.id,
            order_no=order_no,
            factory_id=current_factory_id
        )
        
        current_craft = await order_craft_use_cases.get_current_craft_for_order(order_no)
        
        if not current_craft:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"No in-progress crafts found for order {order_no}"
            )
        
        logger.info(
            "Successfully retrieved current craft for order",
            user_id=current_user.id,
            order_no=order_no,
            craft_id=current_craft.id
        )
        
        return current_craft
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in get_current_craft_for_order",
            error=str(e),
            error_type=type(e).__name__,
            order_no=order_no,
            user_id=current_user.id
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve current craft for order"
        )


@router.get("/statistics", response_model=OrderCraftStatisticsDTO)
@inject
async def get_order_craft_statistics(
    order_no: Optional[str] = Query(None, description="Filter by order number"),
    current_user: User = Depends(get_current_active_user),
    current_factory_id: int = Depends(get_current_factory_from_session),
    order_craft_use_cases: OrderCraftUseCases = Depends(Provide[Container.order_craft_use_cases])
):
    """Get order craft statistics."""
    try:
        logger.info(
            "Retrieving order craft statistics",
            user_id=current_user.id,
            order_no=order_no,
            factory_id=current_factory_id
        )
        
        statistics = await order_craft_use_cases.get_order_craft_statistics(order_no)
        
        logger.info(
            "Successfully retrieved order craft statistics",
            user_id=current_user.id,
            order_no=order_no,
            total_crafts=statistics.total_order_crafts
        )
        
        return statistics
        
    except Exception as e:
        logger.error(
            "Unexpected error in get_order_craft_statistics",
            error=str(e),
            error_type=type(e).__name__,
            order_no=order_no,
            user_id=current_user.id
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve order craft statistics"
        )