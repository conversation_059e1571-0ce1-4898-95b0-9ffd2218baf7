from datetime import timed<PERSON><PERSON>
from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, status
from dependency_injector.wiring import Provide, inject

from config import settings
from src.infrastructure.containers import Container
from src.infrastructure.logging.logger import get_logger
from src.application.use_cases.auth_use_cases import AuthUseCases
from src.application.use_cases.session_use_cases import SessionUseCases
from src.application.dto.auth_dto import (
    PhonePasswordLoginDTO,
    PhoneSmsLoginDTO,
    SendSmsCodeDTO,
    GenerateImageCodeDTO,
    TokenResponseDTO,
    ImageCodeResponseDTO,
    SendSmsCodeResponseDTO
)
from src.presentation.api.v1.auth import create_access_token

router = APIRouter(prefix="/auth", tags=["phone-authentication"])

# Initialize logger
logger = get_logger(__name__)


@router.post("/generate-image-code", response_model=ImageCodeResponseDTO)
@inject
async def generate_image_code(
    request: GenerateImageCodeDTO,
    auth_use_cases: AuthUseCases = Depends(Provide[Container.auth_use_cases])
):
    """Generate image validation code."""
    logger.info(
        "Generate image code request received",
        endpoint="/api/v1/auth/generate-image-code",
        request_data={
            "session_id": request.session_id,
            "has_session_id": request.session_id is not None
        }
    )
    
    try:
        logger.debug("Calling auth use case to generate image validation code")
        result = await auth_use_cases.generate_image_validation_code(request)
        
        logger.info(
            "Image code generated successfully",
            endpoint="/api/v1/auth/generate-image-code",
            success=True,
            has_image_base64=bool(result.get('image_base64')),
            session_id=result.get('session_id'),
            expires_in_seconds=result.get('expires_in_seconds')
        )
        
        return ImageCodeResponseDTO(**result)
        
    except Exception as e:
        logger.error(
            "Failed to generate image code",
            endpoint="/api/v1/auth/generate-image-code",
            error=str(e),
            error_type=type(e).__name__,
            request_data={
                "session_id": request.session_id,
                "has_session_id": request.session_id is not None
            }
        )
        
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post("/send-sms-code", response_model=SendSmsCodeResponseDTO)
@inject
async def send_sms_code(
    request: SendSmsCodeDTO,
    auth_use_cases: AuthUseCases = Depends(Provide[Container.auth_use_cases])
):
    """Send SMS validation code."""
    try:
        result = await auth_use_cases.send_sms_validation_code(request)
        return SendSmsCodeResponseDTO(**result)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to send SMS code"
        )


@router.post("/login-phone-password", response_model=TokenResponseDTO)
@inject
async def login_with_phone_password(
    login_data: PhonePasswordLoginDTO,
    auth_use_cases: AuthUseCases = Depends(Provide[Container.auth_use_cases]),
    session_use_cases: SessionUseCases = Depends(Provide[Container.session_use_cases])
):
    """Login with phone + password + image validation code."""
    try:
        user = await auth_use_cases.authenticate_with_phone_password(login_data)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authentication failed"
            )
        
        # Create session
        session_id, user_session = await session_use_cases.create_user_session(user)
        
        # Create access token
        access_token_expires = timedelta(minutes=settings.jwt.access_token_expire_minutes)
        access_token = create_access_token(
            data={"sub": user.username, "session_id": session_id}, expires_delta=access_token_expires
        )
        
        # User info for response
        user_info = {
            "id": user.id,
            "username": user.username,
            "email": user.email,
            "full_name": user.full_name,
            "phone": user.phone,
            "is_superuser": user.is_superuser
        }
        
        # Factory context for response
        factory_context = {
            "factory_id": user_session.current_factory_id,
            "factory_name": user_session.current_factory_name,
            "department_id": user_session.current_department_id,
            "role": user_session.current_role,
            "is_manager": user_session.is_factory_manager
        }
        
        return TokenResponseDTO(
            access_token=access_token,
            token_type="bearer",
            session_id=session_id,
            user_info=user_info,
            factory_context=factory_context
        )
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Authentication failed"
        )


@router.post("/login-phone-sms", response_model=TokenResponseDTO)
@inject
async def login_with_phone_sms(
    login_data: PhoneSmsLoginDTO,
    auth_use_cases: AuthUseCases = Depends(Provide[Container.auth_use_cases]),
    session_use_cases: SessionUseCases = Depends(Provide[Container.session_use_cases])
):
    """Login with phone + SMS code + image validation code."""
    try:
        user = await auth_use_cases.authenticate_with_phone_sms(login_data)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authentication failed"
            )
        
        # Create session
        session_id, user_session = await session_use_cases.create_user_session(user)
        
        # Create access token
        access_token_expires = timedelta(minutes=settings.jwt.access_token_expire_minutes)
        access_token = create_access_token(
            data={"sub": user.username, "session_id": session_id}, expires_delta=access_token_expires
        )
        
        # User info for response
        user_info = {
            "id": user.id,
            "username": user.username,
            "email": user.email,
            "full_name": user.full_name,
            "phone": user.phone,
            "is_superuser": user.is_superuser
        }
        
        # Factory context for response
        factory_context = {
            "factory_id": user_session.current_factory_id,
            "factory_name": user_session.current_factory_name,
            "department_id": user_session.current_department_id,
            "role": user_session.current_role,
            "is_manager": user_session.is_factory_manager
        }
        
        return TokenResponseDTO(
            access_token=access_token,
            token_type="bearer",
            session_id=session_id,
            user_info=user_info,
            factory_context=factory_context
        )
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Authentication failed"
        )


@router.post("/cleanup-expired-codes")
@inject
async def cleanup_expired_codes(
    auth_use_cases: AuthUseCases = Depends(Provide[Container.auth_use_cases])
):
    """Clean up expired validation codes (admin endpoint)."""
    try:
        deleted_count = await auth_use_cases.cleanup_expired_codes()
        return {
            "success": True,
            "message": f"Cleaned up {deleted_count} expired validation codes"
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to cleanup expired codes"
        )