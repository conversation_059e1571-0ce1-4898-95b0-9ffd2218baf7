from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from dependency_injector.wiring import Provide, inject

from src.infrastructure.containers import Container
from src.application.use_cases.user_use_cases import UserUseCases
from src.application.dto.user_dto import User<PERSON>reateD<PERSON>, UserUpdateDTO, UserResponseDTO
from src.presentation.api.v1.auth import get_current_active_user
from src.domain.entities.user import User

router = APIRouter(prefix="/users", tags=["users"])


@router.post("/", response_model=UserResponseDTO, status_code=status.HTTP_201_CREATED)
@inject
async def create_user(
    user_data: UserCreateDTO,
    user_use_cases: UserUseCases = Depends(Provide[Container.user_use_cases])
):
    """Create a new user."""
    try:
        user = await user_use_cases.create_user(user_data)
        return user
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.get("/", response_model=List[UserResponseDTO])
@inject
async def get_users(
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_active_user),
    user_use_cases: UserUseCases = Depends(Provide[Container.user_use_cases])
):
    """Get all users."""
    users = await user_use_cases.get_all_users(skip=skip, limit=limit)
    return users


@router.get("/{user_id}", response_model=UserResponseDTO)
@inject
async def get_user(
    user_id: int,
    current_user: User = Depends(get_current_active_user),
    user_use_cases: UserUseCases = Depends(Provide[Container.user_use_cases])
):
    """Get user by ID."""
    user = await user_use_cases.get_user_by_id(user_id)
    if not user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")
    return user


@router.put("/{user_id}", response_model=UserResponseDTO)
@inject
async def update_user(
    user_id: int,
    user_data: UserUpdateDTO,
    current_user: User = Depends(get_current_active_user),
    user_use_cases: UserUseCases = Depends(Provide[Container.user_use_cases])
):
    """Update user."""
    user = await user_use_cases.update_user(user_id, user_data)
    if not user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")
    return user


@router.delete("/{user_id}", status_code=status.HTTP_204_NO_CONTENT)
@inject
async def delete_user(
    user_id: int,
    current_user: User = Depends(get_current_active_user),
    user_use_cases: UserUseCases = Depends(Provide[Container.user_use_cases])
):
    """Delete user."""
    success = await user_use_cases.delete_user(user_id)
    if not success:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")