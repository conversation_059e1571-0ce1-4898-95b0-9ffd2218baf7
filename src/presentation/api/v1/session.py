from fastapi import APIRouter, Depends, HTTPException, status, Header
from dependency_injector.wiring import Provide, inject
from typing import Optional

from src.infrastructure.containers import Container
from src.application.use_cases.session_use_cases import SessionUseCases
from src.application.dto.session_dto import (
    SwitchFactoryDTO,
    AvailableFactoriesDTO,
    AvailableFactoryDTO,
    UserSessionDTO,
    SessionFactoryContextDTO,
    SessionStatusDTO
)
from src.presentation.api.v1.auth import get_current_active_user
from src.domain.entities.user import User

router = APIRouter(prefix="/session", tags=["session"])


async def get_session_id_from_header(x_session_id: Optional[str] = Header(None)) -> str:
    """Extract session ID from header."""
    if not x_session_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Session ID header (X-Session-ID) is required"
        )
    return x_session_id


@router.get("/status", response_model=SessionStatusDTO)
@inject
async def get_session_status(
    session_id: str = Depends(get_session_id_from_header),
    session_use_cases: SessionUseCases = Depends(Provide[Container.session_use_cases])
):
    """Get current session status and factory context."""
    try:
        user_session = await session_use_cases.get_user_session(session_id)
        
        if not user_session:
            return SessionStatusDTO(
                is_valid=False,
                message="Session not found or expired"
            )
        
        # Build factory context
        factory_context = SessionFactoryContextDTO(
            factory_id=user_session.current_factory_id,
            factory_name=user_session.current_factory_name,
            department_id=user_session.current_department_id,
            role=user_session.current_role,
            is_manager=user_session.is_factory_manager
        )
        
        session_dto = UserSessionDTO(
            user_id=user_session.user_id,
            username=user_session.username,
            session_id=session_id,
            factory_context=factory_context,
            session_created_at=user_session.session_created_at
        )
        
        return SessionStatusDTO(
            is_valid=True,
            user_session=session_dto
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get session status"
        )


@router.get("/available-factories", response_model=AvailableFactoriesDTO)
@inject
async def get_available_factories(
    session_id: str = Depends(get_session_id_from_header),
    session_use_cases: SessionUseCases = Depends(Provide[Container.session_use_cases])
):
    """Get list of factories user can switch to."""
    try:
        factories_data = await session_use_cases.get_user_available_factories(session_id)
        
        factories = [
            AvailableFactoryDTO(**factory_data) 
            for factory_data in factories_data
        ]
        
        # Get current factory ID
        current_factory_id = None
        for factory in factories:
            if factory.is_current:
                current_factory_id = factory.factory_id
                break
        
        return AvailableFactoriesDTO(
            factories=factories,
            current_factory_id=current_factory_id
        )
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get available factories"
        )


@router.post("/switch-factory", response_model=UserSessionDTO)
@inject
async def switch_factory_context(
    switch_data: SwitchFactoryDTO,
    session_id: str = Depends(get_session_id_from_header),
    session_use_cases: SessionUseCases = Depends(Provide[Container.session_use_cases])
):
    """Switch user's factory context."""
    try:
        new_session = await session_use_cases.switch_factory_context(
            session_id, switch_data.factory_id
        )
        
        # Build factory context
        factory_context = SessionFactoryContextDTO(
            factory_id=new_session.current_factory_id,
            factory_name=new_session.current_factory_name,
            department_id=new_session.current_department_id,
            role=new_session.current_role,
            is_manager=new_session.is_factory_manager
        )
        
        return UserSessionDTO(
            user_id=new_session.user_id,
            username=new_session.username,
            session_id=session_id,
            factory_context=factory_context,
            session_created_at=new_session.session_created_at
        )
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to switch factory context"
        )


@router.post("/refresh-context", response_model=UserSessionDTO)
@inject
async def refresh_factory_context(
    session_id: str = Depends(get_session_id_from_header),
    session_use_cases: SessionUseCases = Depends(Provide[Container.session_use_cases])
):
    """Refresh factory context with latest data."""
    try:
        refreshed_session = await session_use_cases.refresh_factory_context(session_id)
        
        if not refreshed_session:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Session not found"
            )
        
        # Build factory context
        factory_context = SessionFactoryContextDTO(
            factory_id=refreshed_session.current_factory_id,
            factory_name=refreshed_session.current_factory_name,
            department_id=refreshed_session.current_department_id,
            role=refreshed_session.current_role,
            is_manager=refreshed_session.is_factory_manager
        )
        
        return UserSessionDTO(
            user_id=refreshed_session.user_id,
            username=refreshed_session.username,
            session_id=session_id,
            factory_context=factory_context,
            session_created_at=refreshed_session.session_created_at
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to refresh factory context"
        )


@router.post("/extend")
@inject
async def extend_session(
    session_id: str = Depends(get_session_id_from_header),
    session_use_cases: SessionUseCases = Depends(Provide[Container.session_use_cases])
):
    """Extend session expiration."""
    try:
        success = await session_use_cases.extend_session(session_id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Session not found"
            )
        
        return {"message": "Session extended successfully"}
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to extend session"
        )


@router.delete("/logout")
@inject
async def logout(
    session_id: str = Depends(get_session_id_from_header),
    session_use_cases: SessionUseCases = Depends(Provide[Container.session_use_cases])
):
    """Logout and destroy session."""
    try:
        success = await session_use_cases.destroy_session(session_id)
        
        return {
            "message": "Logged out successfully" if success else "Session already expired"
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to logout"
        )