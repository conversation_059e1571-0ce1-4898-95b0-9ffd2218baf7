from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from dependency_injector.wiring import Provide, inject

from src.infrastructure.containers import Container
from src.application.use_cases.role_use_cases import RoleUseCases
from src.application.dto.role_dto import (
    RoleCreateDTO, RoleUpdateDTO, RoleResponseDTO, RoleListDTO,
    RolePermissionAssignDTO, RolePermissionRemoveDTO, RoleSummaryDTO
)
from src.application.dto.permission_dto import PermissionResponseDTO
from src.presentation.api.v1.auth import get_current_active_user
from src.domain.entities.user import User

router = APIRouter(prefix="/roles", tags=["roles"])


@router.post("/", response_model=RoleResponseDTO, status_code=status.HTTP_201_CREATED)
@inject
async def create_role(
    role_data: RoleCreateDTO,
    current_user: User = Depends(get_current_active_user),
    role_use_cases: RoleUseCases = Depends(Provide[Container.role_use_cases])
):
    """Create a new role."""
    try:
        # Check if user has permission to create roles
        if not current_user.has_permission("roles.create"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to create roles"
            )
        
        role = await role_use_cases.create_role(role_data)
        return role
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create role"
        )


@router.get("/", response_model=RoleListDTO)
@inject
async def get_all_roles(
    current_user: User = Depends(get_current_active_user),
    role_use_cases: RoleUseCases = Depends(Provide[Container.role_use_cases])
):
    """Get all roles."""
    try:
        # Check if user has permission to view roles
        if not current_user.has_permission("roles.view"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to view roles"
            )
        
        roles = await role_use_cases.get_all_roles()
        return roles
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve roles"
        )


@router.get("/active", response_model=RoleListDTO)
@inject
async def get_active_roles(
    current_user: User = Depends(get_current_active_user),
    role_use_cases: RoleUseCases = Depends(Provide[Container.role_use_cases])
):
    """Get all active roles."""
    try:
        # Check if user has permission to view roles
        if not current_user.has_permission("roles.view"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to view roles"
            )
        
        roles = await role_use_cases.get_active_roles()
        return roles
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve active roles"
        )


@router.get("/summary", response_model=List[RoleSummaryDTO])
@inject
async def get_roles_summary(
    current_user: User = Depends(get_current_active_user),
    role_use_cases: RoleUseCases = Depends(Provide[Container.role_use_cases])
):
    """Get summary of all roles without full permission details."""
    try:
        # Check if user has permission to view roles
        if not current_user.has_permission("roles.view"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to view roles"
            )
        
        summaries = await role_use_cases.get_roles_summary()
        return summaries
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve role summaries"
        )


@router.get("/{role_id}", response_model=RoleResponseDTO)
@inject
async def get_role_by_id(
    role_id: int,
    current_user: User = Depends(get_current_active_user),
    role_use_cases: RoleUseCases = Depends(Provide[Container.role_use_cases])
):
    """Get role by ID."""
    try:
        # Check if user has permission to view roles
        if not current_user.has_permission("roles.view"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to view roles"
            )
        
        role = await role_use_cases.get_role_by_id(role_id)
        if not role:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Role not found"
            )
        return role
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve role"
        )


@router.put("/{role_id}", response_model=RoleResponseDTO)
@inject
async def update_role(
    role_id: int,
    role_data: RoleUpdateDTO,
    current_user: User = Depends(get_current_active_user),
    role_use_cases: RoleUseCases = Depends(Provide[Container.role_use_cases])
):
    """Update role."""
    try:
        # Check if user has permission to update roles
        if not current_user.has_permission("roles.update"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to update roles"
            )
        
        role = await role_use_cases.update_role(role_id, role_data)
        if not role:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Role not found"
            )
        return role
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update role"
        )


@router.delete("/{role_id}", status_code=status.HTTP_204_NO_CONTENT)
@inject
async def delete_role(
    role_id: int,
    current_user: User = Depends(get_current_active_user),
    role_use_cases: RoleUseCases = Depends(Provide[Container.role_use_cases])
):
    """Delete role."""
    try:
        # Check if user has permission to delete roles
        if not current_user.has_permission("roles.delete"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to delete roles"
            )
        
        deleted = await role_use_cases.delete_role(role_id)
        if not deleted:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Role not found"
            )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete role"
        )


@router.post("/{role_id}/permissions", response_model=RoleResponseDTO)
@inject
async def assign_permissions_to_role(
    role_id: int,
    assignment_data: RolePermissionAssignDTO,
    current_user: User = Depends(get_current_active_user),
    role_use_cases: RoleUseCases = Depends(Provide[Container.role_use_cases])
):
    """Assign permissions to role."""
    try:
        # Check if user has permission to manage role permissions
        if not current_user.has_permission("roles.manage_permissions"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to manage role permissions"
            )
        
        role = await role_use_cases.assign_permissions_to_role(role_id, assignment_data)
        if not role:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Role not found"
            )
        return role
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to assign permissions to role"
        )


@router.delete("/{role_id}/permissions", response_model=RoleResponseDTO)
@inject
async def remove_permissions_from_role(
    role_id: int,
    removal_data: RolePermissionRemoveDTO,
    current_user: User = Depends(get_current_active_user),
    role_use_cases: RoleUseCases = Depends(Provide[Container.role_use_cases])
):
    """Remove permissions from role."""
    try:
        # Check if user has permission to manage role permissions
        if not current_user.has_permission("roles.manage_permissions"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to manage role permissions"
            )
        
        role = await role_use_cases.remove_permissions_from_role(role_id, removal_data)
        if not role:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Role not found"
            )
        return role
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to remove permissions from role"
        )


@router.get("/{role_id}/permissions", response_model=List[PermissionResponseDTO])
@inject
async def get_role_permissions(
    role_id: int,
    current_user: User = Depends(get_current_active_user),
    role_use_cases: RoleUseCases = Depends(Provide[Container.role_use_cases])
):
    """Get permissions assigned to a role."""
    try:
        # Check if user has permission to view roles
        if not current_user.has_permission("roles.view"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to view role permissions"
            )
        
        permissions = await role_use_cases.get_role_permissions(role_id)
        if permissions is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Role not found"
            )
        return permissions
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve role permissions"
        )