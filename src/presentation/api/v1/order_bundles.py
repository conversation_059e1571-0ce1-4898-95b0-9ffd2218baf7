from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from dependency_injector.wiring import Provide, inject

from src.infrastructure.containers import Container
from src.infrastructure.logging.logger import get_logger
from src.application.use_cases.order_bundle_use_cases import OrderBundleUseCases
from src.application.dto.order_bundle_dto import (
    OrderBundleCreateDTO, OrderBundleUpdateDTO, OrderBundleResponseDTO, OrderBundleListDTO,
    OrderBundleSearchDTO, OrderBundleStatusUpdateDTO, OrderBundleProductionUpdateDTO,
    BulkOrderBundleCreateDTO, OrderBundleOperationResultDTO, OrderBundleStatisticsDTO
)
from src.presentation.api.v1.auth import get_current_active_user
from src.presentation.api.v1.factory_management import get_current_factory_from_session
from src.domain.entities.user import User

router = APIRouter(prefix="/order-bundles", tags=["order-bundles"])
logger = get_logger(__name__)


@router.post("/", response_model=OrderBundleResponseDTO)
@inject
async def create_order_bundle(
    bundle_data: OrderBundleCreateDTO,
    current_user: User = Depends(get_current_active_user),
    current_factory_id: int = Depends(get_current_factory_from_session),
    order_bundle_use_cases: OrderBundleUseCases = Depends(Provide[Container.order_bundle_use_cases])
):
    """Create a new order bundle."""
    logger.info(
        "Creating new order bundle",
        user_id=current_user.id,
        factory_id=current_factory_id,
        order_no=bundle_data.order_no,
        order_part_no=bundle_data.order_part_no,
        size=bundle_data.size,
        quantity=bundle_data.quantity
    )
    
    try:
        # Check if user has permission to create order bundles
        if not current_user.has_permission("order_bundles.create"):
            logger.warning(
                "Permission denied for order bundle creation",
                user_id=current_user.id,
                required_permission="order_bundles.create"
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to create order bundles"
            )
        
        order_bundle = await order_bundle_use_cases.create_order_bundle(bundle_data, current_factory_id)
        
        logger.info(
            "Successfully created order bundle",
            order_bundle_id=order_bundle.id,
            order_bundle_no=order_bundle.order_bundle_no,
            created_by=current_user.id
        )
        return order_bundle
        
    except ValueError as e:
        logger.error(
            "ValueError in create_order_bundle",
            error=str(e),
            user_id=current_user.id,
            order_no=bundle_data.order_no
        )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in create_order_bundle",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            order_no=bundle_data.order_no,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create order bundle: {str(e)}"
        )


@router.get("/", response_model=OrderBundleListDTO)
@inject
async def search_order_bundles(
    search_term: Optional[str] = Query(None, description="Search by order_bundle_no, order_part_no, etc."),
    order_no: Optional[str] = Query(None, description="Filter by order number"),
    order_part_no: Optional[str] = Query(None, description="Filter by order part number"),
    size: Optional[str] = Query(None, description="Filter by size"),
    status_filter: Optional[str] = Query(None, description="Filter by bundle status"),
    color: Optional[str] = Query(None, description="Filter by color"),
    cutter_user_id: Optional[int] = Query(None, description="Filter by cutter user ID"),
    sewer_user_id: Optional[int] = Query(None, description="Filter by sewer user ID"),
    qc_user_id: Optional[int] = Query(None, description="Filter by QC user ID"),
    skip: int = Query(0, ge=0, description="Skip items"),
    limit: int = Query(100, ge=1, le=1000, description="Limit items"),
    current_user: User = Depends(get_current_active_user),
    current_factory_id: int = Depends(get_current_factory_from_session),
    order_bundle_use_cases: OrderBundleUseCases = Depends(Provide[Container.order_bundle_use_cases])
):
    """Search order bundles with optional filtering."""
    logger.info(
        "Searching order bundles",
        user_id=current_user.id,
        factory_id=current_factory_id,
        search_term=search_term,
        order_no=order_no,
        order_part_no=order_part_no,
        size=size,
        status_filter=status_filter
    )
    
    try:
        # Check if user has permission to view order bundles
        if not current_user.has_any_permission(["order_bundles.view", "order_bundles.manage"]):
            logger.warning(
                "Permission denied for viewing order bundles",
                user_id=current_user.id,
                required_permissions=["order_bundles.view", "order_bundles.manage"]
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to view order bundles"
            )
        
        search_criteria = None
        if any([search_term, order_no, order_part_no, size, status_filter, color, cutter_user_id, sewer_user_id, qc_user_id]):
            search_criteria = OrderBundleSearchDTO(
                search_term=search_term,
                order_no=order_no,
                order_part_no=order_part_no,
                size=size,
                status=status_filter,
                color=color,
                cutter_user_id=cutter_user_id,
                sewer_user_id=sewer_user_id,
                qc_user_id=qc_user_id
            )
        
        order_bundles = await order_bundle_use_cases.get_all_order_bundles(current_factory_id, search_criteria, skip, limit)
        
        logger.info(
            "Successfully retrieved order bundles",
            user_id=current_user.id,
            factory_id=current_factory_id,
            total_order_bundles=order_bundles.total
        )
        return order_bundles
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in search_order_bundles",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            factory_id=current_factory_id,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to search order bundles: {str(e)}"
        )


@router.get("/order-part/{order_part_no}/order/{order_no}", response_model=List[OrderBundleResponseDTO])
@inject
async def get_order_bundles_by_order_part(
    order_part_no: str,
    order_no: str,
    current_user: User = Depends(get_current_active_user),
    current_factory_id: int = Depends(get_current_factory_from_session),
    order_bundle_use_cases: OrderBundleUseCases = Depends(Provide[Container.order_bundle_use_cases])
):
    """Get all order bundles for a specific order part."""
    logger.info("Getting order bundles by order part", user_id=current_user.id, factory_id=current_factory_id, order_part_no=order_part_no, order_no=order_no)
    
    try:
        # Check if user has permission to view order bundles
        if not current_user.has_any_permission(["order_bundles.view", "order_bundles.manage"]):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to view order bundles"
            )
        
        order_bundles = await order_bundle_use_cases.get_order_bundles_by_order_part(order_part_no, current_factory_id, order_no)
        
        logger.info(
            "Successfully retrieved order bundles for order part",
            user_id=current_user.id,
            factory_id=current_factory_id,
            order_part_no=order_part_no,
            order_no=order_no,
            total_order_bundles=len(order_bundles)
        )
        return order_bundles
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in get_order_bundles_by_order_part",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            factory_id=current_factory_id,
            order_part_no=order_part_no,
            order_no=order_no,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve order bundles: {str(e)}"
        )


@router.get("/order/{order_no}", response_model=List[OrderBundleResponseDTO])
@inject
async def get_order_bundles_by_order(
    order_no: str,
    current_user: User = Depends(get_current_active_user),
    current_factory_id: int = Depends(get_current_factory_from_session),
    order_bundle_use_cases: OrderBundleUseCases = Depends(Provide[Container.order_bundle_use_cases])
):
    """Get all order bundles for a specific order."""
    logger.info("Getting order bundles by order", user_id=current_user.id, factory_id=current_factory_id, order_no=order_no)
    
    try:
        # Check if user has permission to view order bundles
        if not current_user.has_any_permission(["order_bundles.view", "order_bundles.manage"]):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to view order bundles"
            )
        
        order_bundles = await order_bundle_use_cases.get_order_bundles_by_order(order_no, current_factory_id)
        
        logger.info(
            "Successfully retrieved order bundles for order",
            user_id=current_user.id,
            factory_id=current_factory_id,
            order_no=order_no,
            total_order_bundles=len(order_bundles)
        )
        return order_bundles
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in get_order_bundles_by_order",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            factory_id=current_factory_id,
            order_no=order_no,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve order bundles: {str(e)}"
        )


@router.get("/{order_bundle_id}", response_model=OrderBundleResponseDTO)
@inject
async def get_order_bundle_by_id(
    order_bundle_id: int,
    current_user: User = Depends(get_current_active_user),
    order_bundle_use_cases: OrderBundleUseCases = Depends(Provide[Container.order_bundle_use_cases])
):
    """Get order bundle by ID."""
    logger.info("Getting order bundle by ID", user_id=current_user.id, order_bundle_id=order_bundle_id)
    
    try:
        # Check if user has permission to view order bundles
        if not current_user.has_any_permission(["order_bundles.view", "order_bundles.manage"]):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to view order bundles"
            )
        
        order_bundle = await order_bundle_use_cases.get_order_bundle_by_id(order_bundle_id)
        if not order_bundle:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Order bundle not found"
            )
        
        logger.info(
            "Successfully retrieved order bundle",
            user_id=current_user.id,
            order_bundle_id=order_bundle_id,
            order_bundle_no=order_bundle.order_bundle_no
        )
        return order_bundle
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in get_order_bundle_by_id",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            order_bundle_id=order_bundle_id,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve order bundle: {str(e)}"
        )


@router.put("/{order_bundle_id}", response_model=OrderBundleResponseDTO)
@inject
async def update_order_bundle(
    order_bundle_id: int,
    bundle_data: OrderBundleUpdateDTO,
    current_user: User = Depends(get_current_active_user),
    order_bundle_use_cases: OrderBundleUseCases = Depends(Provide[Container.order_bundle_use_cases])
):
    """Update order bundle."""
    logger.info(
        "Updating order bundle",
        user_id=current_user.id,
        order_bundle_id=order_bundle_id,
        update_data=bundle_data.model_dump(exclude_unset=True)
    )
    
    try:
        # Check if user has permission to update order bundles
        if not current_user.has_permission("order_bundles.update"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to update order bundles"
            )
        
        order_bundle = await order_bundle_use_cases.update_order_bundle(order_bundle_id, bundle_data)
        if not order_bundle:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Order bundle not found"
            )
        
        logger.info(
            "Successfully updated order bundle",
            user_id=current_user.id,
            order_bundle_id=order_bundle_id,
            order_bundle_no=order_bundle.order_bundle_no
        )
        return order_bundle
        
    except ValueError as e:
        logger.error(
            "ValueError in update_order_bundle",
            error=str(e),
            user_id=current_user.id,
            order_bundle_id=order_bundle_id
        )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in update_order_bundle",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            order_bundle_id=order_bundle_id,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update order bundle: {str(e)}"
        )


@router.put("/{order_bundle_id}/status", response_model=OrderBundleOperationResultDTO)
@inject
async def update_order_bundle_status(
    order_bundle_id: int,
    status_data: OrderBundleStatusUpdateDTO,
    current_user: User = Depends(get_current_active_user),
    order_bundle_use_cases: OrderBundleUseCases = Depends(Provide[Container.order_bundle_use_cases])
):
    """Update order bundle status."""
    logger.info(
        "Updating order bundle status",
        user_id=current_user.id,
        order_bundle_id=order_bundle_id,
        new_status=status_data.status
    )
    
    try:
        # Check if user has permission to manage order bundles
        if not current_user.has_permission("order_bundles.manage"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to manage order bundle status"
            )
        
        result = await order_bundle_use_cases.update_order_bundle_status(order_bundle_id, status_data)
        
        logger.info(
            "Order bundle status update completed",
            user_id=current_user.id,
            order_bundle_id=order_bundle_id,
            success=result.success,
            message=result.message
        )
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in update_order_bundle_status",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            order_bundle_id=order_bundle_id,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update order bundle status: {str(e)}"
        )


@router.put("/{order_bundle_id}/production", response_model=OrderBundleOperationResultDTO)
@inject
async def update_order_bundle_production(
    order_bundle_id: int,
    production_data: OrderBundleProductionUpdateDTO,
    current_user: User = Depends(get_current_active_user),
    order_bundle_use_cases: OrderBundleUseCases = Depends(Provide[Container.order_bundle_use_cases])
):
    """Update order bundle production progress."""
    logger.info(
        "Updating order bundle production",
        user_id=current_user.id,
        order_bundle_id=order_bundle_id,
        completed_quantity=production_data.completed_quantity
    )
    
    try:
        # Check if user has permission to manage production
        if not current_user.has_permission("production.manage"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to update production"
            )
        
        result = await order_bundle_use_cases.update_order_bundle_production(order_bundle_id, production_data)
        
        logger.info(
            "Order bundle production update completed",
            user_id=current_user.id,
            order_bundle_id=order_bundle_id,
            success=result.success,
            message=result.message
        )
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in update_order_bundle_production",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            order_bundle_id=order_bundle_id,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update order bundle production: {str(e)}"
        )


@router.delete("/{order_bundle_id}")
@inject
async def delete_order_bundle(
    order_bundle_id: int,
    current_user: User = Depends(get_current_active_user),
    order_bundle_use_cases: OrderBundleUseCases = Depends(Provide[Container.order_bundle_use_cases])
):
    """Delete order bundle."""
    logger.info("Deleting order bundle", user_id=current_user.id, order_bundle_id=order_bundle_id)
    
    try:
        # Check if user has permission to delete order bundles
        if not current_user.has_permission("order_bundles.delete"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to delete order bundles"
            )
        
        result = await order_bundle_use_cases.delete_order_bundle(order_bundle_id)
        if not result:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Order bundle not found"
            )
        
        logger.info(
            "Successfully deleted order bundle",
            user_id=current_user.id,
            order_bundle_id=order_bundle_id
        )
        return {"success": True, "message": "Order bundle deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in delete_order_bundle",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            order_bundle_id=order_bundle_id,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete order bundle: {str(e)}"
        )


@router.post("/bulk", response_model=List[OrderBundleResponseDTO])
@inject
async def bulk_create_order_bundles(
    bulk_data: BulkOrderBundleCreateDTO,
    current_user: User = Depends(get_current_active_user),
    current_factory_id: int = Depends(get_current_factory_from_session),
    order_bundle_use_cases: OrderBundleUseCases = Depends(Provide[Container.order_bundle_use_cases])
):
    """Create multiple order bundles at once."""
    logger.info(
        "Bulk creating order bundles",
        user_id=current_user.id,
        factory_id=current_factory_id,
        order_no=bulk_data.order_no,
        order_part_no=bulk_data.order_part_no,
        bundles_count=len(bulk_data.order_bundles)
    )
    
    try:
        # Check if user has permission to create order bundles
        if not current_user.has_permission("order_bundles.create"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to create order bundles"
            )
        
        results = await order_bundle_use_cases.bulk_create_order_bundles(bulk_data, current_factory_id)
        
        logger.info(
            "Bulk order bundle creation completed",
            user_id=current_user.id,
            factory_id=current_factory_id,
            order_no=bulk_data.order_no,
            order_part_no=bulk_data.order_part_no,
            total_bundles=len(results)
        )
        return results
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in bulk_create_order_bundles",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            factory_id=current_factory_id,
            order_no=bulk_data.order_no,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create order bundles: {str(e)}"
        )


@router.get("/statistics/summary", response_model=OrderBundleStatisticsDTO)
@inject
async def get_order_bundle_statistics(
    order_no: Optional[str] = Query(None, description="Filter by order number"),
    order_part_no: Optional[str] = Query(None, description="Filter by order part number"),
    current_user: User = Depends(get_current_active_user),
    current_factory_id: int = Depends(get_current_factory_from_session),
    order_bundle_use_cases: OrderBundleUseCases = Depends(Provide[Container.order_bundle_use_cases])
):
    """Get order bundle statistics summary."""
    logger.info("Getting order bundle statistics", user_id=current_user.id, factory_id=current_factory_id, order_no=order_no, order_part_no=order_part_no)
    
    try:
        # Check if user has permission to view statistics
        if not current_user.has_any_permission(["order_bundles.view", "order_bundles.manage", "statistics.view"]):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to view statistics"
            )
        
        statistics = await order_bundle_use_cases.get_order_bundle_statistics(current_factory_id, order_no, order_part_no)
        
        logger.info(
            "Successfully retrieved order bundle statistics",
            user_id=current_user.id,
            factory_id=current_factory_id,
            order_no=order_no,
            order_part_no=order_part_no
        )
        return statistics
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in get_order_bundle_statistics",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            factory_id=current_factory_id,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve statistics: {str(e)}"
        )