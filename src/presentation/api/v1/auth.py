from datetime import datetime, timedelta
from typing import Optional
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException, status
from fastapi.security import OAuth2Pass<PERSON><PERSON><PERSON><PERSON>, OAuth2PasswordRequestForm
from jose import JWTError, jwt
from dependency_injector.wiring import Provide, inject

from config import settings
from src.infrastructure.containers import Container
from src.application.use_cases.user_use_cases import UserUseCases
from src.application.use_cases.session_use_cases import SessionUseCases
from src.application.dto.user_dto import TokenResponseDTO, UserResponseDTO, FactoryContextDTO
from src.domain.entities.user import User

router = APIRouter(prefix="/auth", tags=["authentication"])
oauth2_scheme = OAuth2PasswordBearer(tokenUrl=f"{settings.api.v1_prefix}/auth/token")


def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """Create JWT access token."""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=settings.jwt.access_token_expire_minutes)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.jwt.secret_key, algorithm=settings.jwt.algorithm)
    return encoded_jwt


@inject
async def get_current_user(
    token: str = Depends(oauth2_scheme),
    user_use_cases: UserUseCases = Depends(Provide[Container.user_use_cases])
) -> User:
    """Get current authenticated user."""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, settings.jwt.secret_key, algorithms=[settings.jwt.algorithm])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception
    
    user = await user_use_cases.get_user_by_username(username=username)
    if user is None:
        raise credentials_exception
    return user


async def get_current_active_user(current_user: User = Depends(get_current_user)) -> User:
    """Get current active user."""
    if not current_user.is_active:
        raise HTTPException(status_code=400, detail="Inactive user")
    return current_user


@router.post("/token", response_model=TokenResponseDTO)
@inject
async def login(
    form_data: OAuth2PasswordRequestForm = Depends(),
    user_use_cases: UserUseCases = Depends(Provide[Container.user_use_cases]),
    session_use_cases: SessionUseCases = Depends(Provide[Container.session_use_cases])
):
    """Login and get access token with session context."""
    user = await user_use_cases.authenticate_user(form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # Create session with factory context
    session_id, user_session = await session_use_cases.create_user_session(user)
    
    # Create access token with session_id
    access_token_expires = timedelta(minutes=settings.jwt.access_token_expire_minutes)
    access_token = create_access_token(
        data={"sub": user.username, "session_id": session_id}, expires_delta=access_token_expires
    )
    
    # Create factory context DTO
    factory_context = FactoryContextDTO(
        factory_id=user_session.current_factory_id,
        factory_name=user_session.current_factory_name,
        department_id=user_session.current_department_id,
        role=user_session.current_role,
        is_manager=user_session.is_factory_manager
    )
    
    return TokenResponseDTO(
        access_token=access_token,
        token_type="bearer",
        session_id=session_id,
        factory_context=factory_context
    )


@router.get("/me", response_model=UserResponseDTO)
async def read_users_me(current_user: User = Depends(get_current_active_user)):
    """Get current user information."""
    return current_user