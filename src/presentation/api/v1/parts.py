from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from dependency_injector.wiring import Provide, inject

from src.infrastructure.containers import Container
from src.infrastructure.logging.logger import get_logger
from src.application.use_cases.part_use_cases import PartUseCases
from src.application.dto.part_dto import (
    PartCreateDTO, PartUpdateDTO, PartResponseDTO, PartWithBundlesDTO,
    PartListDTO, PartSearchDTO, PartStatusUpdateDTO, PartProductionUpdateDTO,
    BulkPartCreateDTO, PartOperationResultDTO
)
from src.presentation.api.v1.auth import get_current_active_user
from src.domain.entities.user import User

router = APIRouter(prefix="/parts", tags=["parts"])
logger = get_logger(__name__)


@router.post("/", response_model=PartResponseDTO)
@inject
async def create_part(
    part_data: PartCreateDTO,
    current_user: User = Depends(get_current_active_user),
    part_use_cases: PartUseCases = Depends(Provide[Container.part_use_cases])
):
    """Create a new part."""
    logger.info(
        "Creating new part",
        user_id=current_user.id,
        order_no=part_data.order_no,
        part_name=part_data.part_name,
        part_type=part_data.part_type
    )
    
    try:
        # Check if user has permission to create parts
        if not current_user.has_permission("parts.create"):
            logger.warning(
                "Permission denied for part creation",
                user_id=current_user.id,
                required_permission="parts.create"
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to create parts"
            )
        
        part = await part_use_cases.create_part(part_data)
        
        logger.info(
            "Successfully created part",
            part_id=part.id,
            part_no=part.part_no,
            created_by=current_user.id
        )
        return part
        
    except ValueError as e:
        logger.error(
            "ValueError in create_part",
            error=str(e),
            user_id=current_user.id,
            order_no=part_data.order_no
        )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in create_part",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            order_no=part_data.order_no,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create part: {str(e)}"
        )


@router.get("/", response_model=PartListDTO)
@inject
async def search_parts(
    search_term: Optional[str] = Query(None, description="Search by part_no, part_name, etc."),
    order_no: Optional[str] = Query(None, description="Filter by order number"),
    part_type: Optional[str] = Query(None, description="Filter by part type"),
    status_filter: Optional[str] = Query(None, description="Filter by part status"),
    supervisor_user_id: Optional[int] = Query(None, description="Filter by supervisor user ID"),
    color: Optional[str] = Query(None, description="Filter by color"),
    skip: int = Query(0, ge=0, description="Skip items"),
    limit: int = Query(100, ge=1, le=1000, description="Limit items"),
    current_user: User = Depends(get_current_active_user),
    part_use_cases: PartUseCases = Depends(Provide[Container.part_use_cases])
):
    """Search parts with optional filtering."""
    logger.info(
        "Searching parts",
        user_id=current_user.id,
        search_term=search_term,
        order_no=order_no,
        part_type=part_type,
        status_filter=status_filter
    )
    
    try:
        # Check if user has permission to view parts
        if not current_user.has_any_permission(["parts.view", "parts.manage"]):
            logger.warning(
                "Permission denied for viewing parts",
                user_id=current_user.id,
                required_permissions=["parts.view", "parts.manage"]
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to view parts"
            )
        
        search_criteria = None
        if any([search_term, order_no, part_type, status_filter, supervisor_user_id, color]):
            search_criteria = PartSearchDTO(
                search_term=search_term,
                order_no=order_no,
                part_type=part_type,
                status=status_filter,
                supervisor_user_id=supervisor_user_id,
                color=color
            )
        
        parts = await part_use_cases.search_parts(search_criteria, skip, limit)
        
        logger.info(
            "Successfully retrieved parts",
            user_id=current_user.id,
            total_parts=parts.total
        )
        return parts
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in search_parts",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to search parts: {str(e)}"
        )


@router.get("/order/{order_no}", response_model=PartListDTO)
@inject
async def get_parts_by_order(
    order_no: str,
    skip: int = Query(0, ge=0, description="Skip items"),
    limit: int = Query(100, ge=1, le=1000, description="Limit items"),
    current_user: User = Depends(get_current_active_user),
    part_use_cases: PartUseCases = Depends(Provide[Container.part_use_cases])
):
    """Get all parts for a specific order."""
    logger.info("Getting parts by order", user_id=current_user.id, order_no=order_no)
    
    try:
        # Check if user has permission to view parts
        if not current_user.has_any_permission(["parts.view", "parts.manage"]):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to view parts"
            )
        
        parts = await part_use_cases.get_parts_by_order(order_no, skip, limit)
        
        logger.info(
            "Successfully retrieved parts for order",
            user_id=current_user.id,
            order_no=order_no,
            total_parts=parts.total
        )
        return parts
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in get_parts_by_order",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            order_no=order_no,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve parts: {str(e)}"
        )


@router.get("/{part_id}", response_model=PartResponseDTO)
@inject
async def get_part_by_id(
    part_id: int,
    current_user: User = Depends(get_current_active_user),
    part_use_cases: PartUseCases = Depends(Provide[Container.part_use_cases])
):
    """Get part by ID."""
    logger.info("Getting part by ID", user_id=current_user.id, part_id=part_id)
    
    try:
        # Check if user has permission to view parts
        if not current_user.has_any_permission(["parts.view", "parts.manage"]):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to view parts"
            )
        
        part = await part_use_cases.get_part_by_id(part_id)
        if not part:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Part not found"
            )
        
        logger.info(
            "Successfully retrieved part",
            user_id=current_user.id,
            part_id=part_id,
            part_no=part.part_no
        )
        return part
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in get_part_by_id",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            part_id=part_id,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve part: {str(e)}"
        )


@router.get("/part-no/{part_no}", response_model=PartWithBundlesDTO)
@inject
async def get_part_by_part_no(
    part_no: str,
    current_user: User = Depends(get_current_active_user),
    part_use_cases: PartUseCases = Depends(Provide[Container.part_use_cases])
):
    """Get part by part number with bundles."""
    logger.info("Getting part by part_no", user_id=current_user.id, part_no=part_no)
    
    try:
        # Check if user has permission to view parts
        if not current_user.has_any_permission(["parts.view", "parts.manage"]):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to view parts"
            )
        
        part = await part_use_cases.get_part_by_part_no(part_no)
        if not part:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Part not found"
            )
        
        logger.info(
            "Successfully retrieved part by part_no",
            user_id=current_user.id,
            part_no=part_no,
            part_id=part.id
        )
        return part
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in get_part_by_part_no",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            part_no=part_no,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve part: {str(e)}"
        )


@router.put("/{part_id}", response_model=PartResponseDTO)
@inject
async def update_part(
    part_id: int,
    part_data: PartUpdateDTO,
    current_user: User = Depends(get_current_active_user),
    part_use_cases: PartUseCases = Depends(Provide[Container.part_use_cases])
):
    """Update part."""
    logger.info(
        "Updating part",
        user_id=current_user.id,
        part_id=part_id,
        update_data=part_data.model_dump(exclude_unset=True)
    )
    
    try:
        # Check if user has permission to update parts
        if not current_user.has_permission("parts.update"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to update parts"
            )
        
        part = await part_use_cases.update_part(part_id, part_data)
        if not part:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Part not found"
            )
        
        logger.info(
            "Successfully updated part",
            user_id=current_user.id,
            part_id=part_id,
            part_no=part.part_no
        )
        return part
        
    except ValueError as e:
        logger.error(
            "ValueError in update_part",
            error=str(e),
            user_id=current_user.id,
            part_id=part_id
        )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in update_part",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            part_id=part_id,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update part: {str(e)}"
        )


@router.put("/{part_id}/status", response_model=PartOperationResultDTO)
@inject
async def update_part_status(
    part_id: int,
    status_data: PartStatusUpdateDTO,
    current_user: User = Depends(get_current_active_user),
    part_use_cases: PartUseCases = Depends(Provide[Container.part_use_cases])
):
    """Update part status."""
    logger.info(
        "Updating part status",
        user_id=current_user.id,
        part_id=part_id,
        new_status=status_data.status
    )
    
    try:
        # Check if user has permission to manage parts
        if not current_user.has_permission("parts.manage"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to manage part status"
            )
        
        result = await part_use_cases.update_part_status(part_id, status_data)
        
        logger.info(
            "Part status update completed",
            user_id=current_user.id,
            part_id=part_id,
            success=result.success,
            message=result.message
        )
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in update_part_status",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            part_id=part_id,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update part status: {str(e)}"
        )


@router.put("/{part_id}/production", response_model=PartOperationResultDTO)
@inject
async def update_part_production(
    part_id: int,
    production_data: PartProductionUpdateDTO,
    current_user: User = Depends(get_current_active_user),
    part_use_cases: PartUseCases = Depends(Provide[Container.part_use_cases])
):
    """Update part production progress."""
    logger.info(
        "Updating part production",
        user_id=current_user.id,
        part_id=part_id,
        completed_quantity=production_data.completed_quantity
    )
    
    try:
        # Check if user has permission to manage production
        if not current_user.has_permission("production.manage"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to update production"
            )
        
        result = await part_use_cases.update_part_production(part_id, production_data)
        
        logger.info(
            "Part production update completed",
            user_id=current_user.id,
            part_id=part_id,
            success=result.success,
            message=result.message
        )
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in update_part_production",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            part_id=part_id,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update part production: {str(e)}"
        )


@router.delete("/{part_id}")
@inject
async def delete_part(
    part_id: int,
    current_user: User = Depends(get_current_active_user),
    part_use_cases: PartUseCases = Depends(Provide[Container.part_use_cases])
):
    """Delete part."""
    logger.info("Deleting part", user_id=current_user.id, part_id=part_id)
    
    try:
        # Check if user has permission to delete parts
        if not current_user.has_permission("parts.delete"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to delete parts"
            )
        
        result = await part_use_cases.delete_part(part_id)
        if not result.success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Part not found"
            )
        
        logger.info(
            "Successfully deleted part",
            user_id=current_user.id,
            part_id=part_id
        )
        return {"success": True, "message": "Part deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in delete_part",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            part_id=part_id,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete part: {str(e)}"
        )


@router.post("/bulk", response_model=List[PartOperationResultDTO])
@inject
async def bulk_create_parts(
    bulk_data: BulkPartCreateDTO,
    current_user: User = Depends(get_current_active_user),
    part_use_cases: PartUseCases = Depends(Provide[Container.part_use_cases])
):
    """Create multiple parts at once."""
    logger.info(
        "Bulk creating parts",
        user_id=current_user.id,
        order_no=bulk_data.order_no,
        parts_count=len(bulk_data.parts)
    )
    
    try:
        # Check if user has permission to create parts
        if not current_user.has_permission("parts.create"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to create parts"
            )
        
        results = await part_use_cases.bulk_create_parts(bulk_data)
        
        successful_count = sum(1 for r in results if r.success)
        logger.info(
            "Bulk part creation completed",
            user_id=current_user.id,
            order_no=bulk_data.order_no,
            total_parts=len(results),
            successful_parts=successful_count
        )
        return results
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in bulk_create_parts",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            order_no=bulk_data.order_no,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create parts: {str(e)}"
        )


@router.get("/statistics/summary")
@inject
async def get_part_statistics(
    order_no: Optional[str] = Query(None, description="Filter by order number"),
    current_user: User = Depends(get_current_active_user),
    part_use_cases: PartUseCases = Depends(Provide[Container.part_use_cases])
):
    """Get part statistics summary."""
    logger.info("Getting part statistics", user_id=current_user.id, order_no=order_no)
    
    try:
        # Check if user has permission to view statistics
        if not current_user.has_any_permission(["parts.view", "parts.manage", "statistics.view"]):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to view statistics"
            )
        
        statistics = await part_use_cases.get_part_statistics(order_no)
        
        logger.info(
            "Successfully retrieved part statistics",
            user_id=current_user.id,
            order_no=order_no
        )
        return statistics
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in get_part_statistics",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve statistics: {str(e)}"
        )