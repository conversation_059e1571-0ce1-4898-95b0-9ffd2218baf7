from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import <PERSON>A<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>earer
from dependency_injector.wiring import Provide, inject
from jose import JWTError, jwt

from config import settings
from src.infrastructure.containers import Container
from src.infrastructure.logging.logger import get_logger
from src.application.use_cases.factory_use_cases import FactoryUseCases
from src.application.use_cases.session_use_cases import SessionUseCases
from src.application.dto.factory_dto import (
    FactoryJoinRequestDTO,
    FactoryJoinApprovalDTO,
    UserFactoryResponseDTO,
    MyFactoriesDTO,
    PendingRequestDTO,
    FactoryMemberDTO
)
from src.presentation.api.v1.auth import get_current_active_user, oauth2_scheme
from src.domain.entities.user import User
from src.domain.entities.user_factory import UserFactoryRole

router = APIRouter(prefix="/factory-management", tags=["factory-management"])
logger = get_logger(__name__)


@inject  
async def get_current_factory_from_session(
    token: str = Depends(oauth2_scheme),
    session_use_cases: SessionUseCases = Depends(Provide[Container.session_use_cases])
) -> int:
    """Get current factory ID from JWT session. Extracts session_id from token and retrieves factory context from Redis."""
    logger.info("Starting get_current_factory_from_session")
    
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        logger.debug("Decoding JWT token to extract session_id")
        
        # Decode JWT token to get session_id
        payload = jwt.decode(token, settings.jwt.secret_key, algorithms=[settings.jwt.algorithm])
        session_id: str = payload.get("session_id")
        
        logger.debug(
            "JWT token decoded",
            has_session_id=session_id is not None,
            payload_keys=list(payload.keys()),
            username=payload.get("sub")
        )
        
        if session_id is None:
            logger.warning("No session_id found in JWT token payload")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No session ID found in token. Please login again."
            )
            
    except JWTError as e:
        logger.error(
            "JWT decode error in get_current_factory_from_session",
            error=str(e),
            error_type=type(e).__name__
        )
        raise credentials_exception
    
    try:
        logger.debug("Getting user session from Redis", session_id=session_id)
        
        # Get user session from Redis
        user_session = await session_use_cases.get_user_session(session_id)
        
        if not user_session:
            logger.warning("Session not found in Redis", session_id=session_id)
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Session not found or expired. Please login again."
            )
        
        logger.debug(
            "Retrieved user session from Redis",
            session_id=session_id,
            user_id=user_session.user_id,
            username=user_session.username,
            current_factory_id=user_session.current_factory_id,
            factory_name=user_session.current_factory_name
        )
        
        # Check if user has factory context
        if not user_session.current_factory_id:
            logger.warning(
                "User session has no factory context",
                session_id=session_id,
                user_id=user_session.user_id
            )
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="User is not associated with any factory. Please join a factory first."
            )
        
        logger.info(
            "Successfully retrieved factory context",
            factory_id=user_session.current_factory_id,
            session_id=session_id
        )
        return user_session.current_factory_id
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in get_current_factory_from_session",
            error=str(e),
            error_type=type(e).__name__,
            session_id=session_id if 'session_id' in locals() else 'unknown',
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to determine factory context: {str(e)}"
        )


@router.post("/join-request", response_model=UserFactoryResponseDTO)
@inject
async def request_to_join_factory(
    request_data: FactoryJoinRequestDTO,
    current_user: User = Depends(get_current_active_user),
    factory_use_cases: FactoryUseCases = Depends(Provide[Container.factory_use_cases])
):
    """Request to join a factory."""
    try:
        user_factory = await factory_use_cases.request_to_join_factory(
            current_user.id, request_data
        )
        return UserFactoryResponseDTO.model_validate(user_factory)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post("/approve-request", response_model=UserFactoryResponseDTO)
@inject
async def approve_or_reject_request(
    approval_data: FactoryJoinApprovalDTO,
    current_user: User = Depends(get_current_active_user),
    factory_use_cases: FactoryUseCases = Depends(Provide[Container.factory_use_cases])
):
    """Approve or reject a factory join request (managers only)."""
    try:
        user_factory = await factory_use_cases.approve_or_reject_request(
            current_user.id, approval_data
        )
        return UserFactoryResponseDTO.model_validate(user_factory)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/my-factories", response_model=MyFactoriesDTO)
@inject
async def get_my_factories(
    current_user: User = Depends(get_current_active_user),
    factory_use_cases: FactoryUseCases = Depends(Provide[Container.factory_use_cases])
):
    """Get current user's factory relationships."""
    try:
        return await factory_use_cases.get_user_factories(current_user.id)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get factory information"
        )


@router.get("/pending-requests", response_model=List[UserFactoryResponseDTO])
@inject
async def get_pending_requests(
    current_user: User = Depends(get_current_active_user),
    current_factory_id: int = Depends(get_current_factory_from_session),
    factory_use_cases: FactoryUseCases = Depends(Provide[Container.factory_use_cases])
):
    """Get pending join requests for current factory (managers only)."""
    try:
        requests = await factory_use_cases.get_factory_pending_requests(
            current_user.id, current_factory_id
        )
        return [UserFactoryResponseDTO.model_validate(req) for req in requests]
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )


@router.get("/members", response_model=List[UserFactoryResponseDTO])
@inject
async def get_factory_members(
    current_user: User = Depends(get_current_active_user),
    current_factory_id: int = Depends(get_current_factory_from_session),
    factory_use_cases: FactoryUseCases = Depends(Provide[Container.factory_use_cases])
):
    """Get all members of current factory."""
    try:
        members = await factory_use_cases.get_factory_members(
            current_user.id, current_factory_id
        )
        return [UserFactoryResponseDTO.model_validate(member) for member in members]
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )


@router.put("/user/{user_id}/role", response_model=UserFactoryResponseDTO)
@inject
async def update_user_role(
    user_id: int,
    new_role: UserFactoryRole,
    current_user: User = Depends(get_current_active_user),
    current_factory_id: int = Depends(get_current_factory_from_session),
    factory_use_cases: FactoryUseCases = Depends(Provide[Container.factory_use_cases])
):
    """Update user's role in current factory (managers only)."""
    try:
        user_factory = await factory_use_cases.update_user_role_in_factory(
            current_user.id, user_id, current_factory_id, new_role
        )
        return UserFactoryResponseDTO.model_validate(user_factory)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )


@router.post("/user/{user_id}/suspend", response_model=UserFactoryResponseDTO)
@inject
async def suspend_user(
    user_id: int,
    reason: str = None,
    current_user: User = Depends(get_current_active_user),
    current_factory_id: int = Depends(get_current_factory_from_session),
    factory_use_cases: FactoryUseCases = Depends(Provide[Container.factory_use_cases])
):
    """Suspend user from current factory (managers only)."""
    try:
        user_factory = await factory_use_cases.suspend_user_from_factory(
            current_user.id, user_id, current_factory_id, reason
        )
        return UserFactoryResponseDTO.model_validate(user_factory)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )


@router.post("/resign", response_model=UserFactoryResponseDTO)
@inject
async def resign_from_factory(
    current_user: User = Depends(get_current_active_user),
    current_factory_id: int = Depends(get_current_factory_from_session),
    factory_use_cases: FactoryUseCases = Depends(Provide[Container.factory_use_cases])
):
    """Resign from current factory."""
    try:
        user_factory = await factory_use_cases.resign_from_factory(
            current_user.id, current_factory_id
        )
        return UserFactoryResponseDTO.model_validate(user_factory)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )