from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from dependency_injector.wiring import Provide, inject

from src.infrastructure.containers import Container
from src.infrastructure.logging.logger import get_logger
from src.application.use_cases.craft_use_cases import CraftUseCases
from src.application.dto.craft_dto import (
    CraftCreateDTO, CraftUpdateDTO, CraftResponseDTO, CraftListDTO,
    CraftRouteCreateDTO, CraftRouteUpdateDTO, CraftRouteResponseDTO,
    CraftRouteDetailDTO, CraftWithRoutesDTO, CraftRouteListDTO,
    CraftSearchDTO, CraftRouteSearchDTO, CraftOperationResultDTO,
    BulkCraftRouteCreateDTO, BulkCraftRouteOperationResultDTO
)
from src.presentation.api.v1.auth import get_current_active_user
from src.domain.entities.user import User

router = APIRouter(prefix="/crafts", tags=["crafts"])
logger = get_logger(__name__)


# Craft management endpoints
@router.post("/", response_model=CraftResponseDTO)
@inject
async def create_craft(
    craft_data: CraftCreateDTO,
    current_user: User = Depends(get_current_active_user),
    craft_use_cases: CraftUseCases = Depends(Provide[Container.craft_use_cases])
):
    """Create a new craft (admin only)."""
    logger.info(
        "Creating new craft",
        user_id=current_user.id,
        craft_code=craft_data.code,
        craft_name=craft_data.name
    )
    
    try:
        # Check if user has permission to create crafts
        if not current_user.has_permission("crafts.create"):
            logger.warning(
                "Permission denied for craft creation",
                user_id=current_user.id,
                required_permission="crafts.create"
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to create crafts"
            )
        
        craft = await craft_use_cases.create_craft(craft_data)
        
        logger.info(
            "Successfully created craft",
            craft_id=craft.id,
            craft_code=craft.code,
            created_by=current_user.id
        )
        return craft
        
    except ValueError as e:
        logger.error(
            "ValueError in create_craft",
            error=str(e),
            user_id=current_user.id,
            craft_code=craft_data.code
        )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in create_craft",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            craft_code=craft_data.code,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create craft: {str(e)}"
        )


@router.get("/", response_model=CraftListDTO)
@inject
async def get_all_crafts(
    search_term: Optional[str] = Query(None, description="Search by code, name, or description"),
    enabled: Optional[bool] = Query(None, description="Filter by enabled status"),
    min_priority: Optional[int] = Query(None, ge=0, description="Minimum priority"),
    max_priority: Optional[int] = Query(None, ge=0, description="Maximum priority"),
    current_user: User = Depends(get_current_active_user),
    craft_use_cases: CraftUseCases = Depends(Provide[Container.craft_use_cases])
):
    """Get all crafts with optional filtering."""
    logger.info(
        "Getting all crafts",
        user_id=current_user.id,
        search_term=search_term,
        enabled=enabled,
        min_priority=min_priority,
        max_priority=max_priority
    )
    
    try:
        # Check if user has permission to view crafts
        if not current_user.has_any_permission(["crafts.view", "crafts.manage"]):
            logger.warning(
                "Permission denied for viewing crafts",
                user_id=current_user.id,
                required_permissions=["crafts.view", "crafts.manage"]
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to view crafts"
            )
        
        search_criteria = None
        if any([search_term, enabled is not None, min_priority is not None, max_priority is not None]):
            search_criteria = CraftSearchDTO(
                search_term=search_term,
                enabled=enabled,
                min_priority=min_priority,
                max_priority=max_priority
            )
        
        crafts = await craft_use_cases.get_all_crafts(search_criteria)
        
        logger.info(
            "Successfully retrieved crafts",
            user_id=current_user.id,
            total_crafts=crafts.total
        )
        return crafts
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in get_all_crafts",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve crafts: {str(e)}"
        )


@router.get("/enabled", response_model=CraftListDTO)
@inject
async def get_enabled_crafts(
    current_user: User = Depends(get_current_active_user),
    craft_use_cases: CraftUseCases = Depends(Provide[Container.craft_use_cases])
):
    """Get all enabled crafts."""
    logger.info("Getting enabled crafts", user_id=current_user.id)
    
    try:
        # Check if user has permission to view crafts
        if not current_user.has_any_permission(["crafts.view", "crafts.manage"]):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to view crafts"
            )
        
        crafts = await craft_use_cases.get_enabled_crafts()
        
        logger.info(
            "Successfully retrieved enabled crafts",
            user_id=current_user.id,
            total_crafts=crafts.total
        )
        return crafts
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in get_enabled_crafts",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve enabled crafts: {str(e)}"
        )


@router.get("/{craft_id}", response_model=CraftResponseDTO)
@inject
async def get_craft_by_id(
    craft_id: int,
    current_user: User = Depends(get_current_active_user),
    craft_use_cases: CraftUseCases = Depends(Provide[Container.craft_use_cases])
):
    """Get craft by ID."""
    logger.info("Getting craft by ID", user_id=current_user.id, craft_id=craft_id)
    
    try:
        # Check if user has permission to view crafts
        if not current_user.has_any_permission(["crafts.view", "crafts.manage"]):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to view crafts"
            )
        
        craft = await craft_use_cases.get_craft_by_id(craft_id)
        if not craft:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Craft not found"
            )
        
        logger.info(
            "Successfully retrieved craft",
            user_id=current_user.id,
            craft_id=craft_id,
            craft_code=craft.code
        )
        return craft
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in get_craft_by_id",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            craft_id=craft_id,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve craft: {str(e)}"
        )


@router.get("/code/{craft_code}", response_model=CraftWithRoutesDTO)
@inject
async def get_craft_by_code(
    craft_code: str,
    current_user: User = Depends(get_current_active_user),
    craft_use_cases: CraftUseCases = Depends(Provide[Container.craft_use_cases])
):
    """Get craft by code with its routes."""
    logger.info("Getting craft by code", user_id=current_user.id, craft_code=craft_code)
    
    try:
        # Check if user has permission to view crafts
        if not current_user.has_any_permission(["crafts.view", "crafts.manage"]):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to view crafts"
            )
        
        craft = await craft_use_cases.get_craft_by_code(craft_code)
        if not craft:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Craft not found"
            )
        
        logger.info(
            "Successfully retrieved craft with routes",
            user_id=current_user.id,
            craft_code=craft_code,
            routes_count=len(craft.routes)
        )
        return craft
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in get_craft_by_code",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            craft_code=craft_code,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve craft: {str(e)}"
        )


@router.put("/{craft_id}", response_model=CraftResponseDTO)
@inject
async def update_craft(
    craft_id: int,
    craft_data: CraftUpdateDTO,
    current_user: User = Depends(get_current_active_user),
    craft_use_cases: CraftUseCases = Depends(Provide[Container.craft_use_cases])
):
    """Update craft (admin only)."""
    logger.info(
        "Updating craft",
        user_id=current_user.id,
        craft_id=craft_id,
        update_data=craft_data.model_dump(exclude_unset=True)
    )
    
    try:
        # Check if user has permission to update crafts
        if not current_user.has_permission("crafts.update"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to update crafts"
            )
        
        craft = await craft_use_cases.update_craft(craft_id, craft_data)
        if not craft:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Craft not found"
            )
        
        logger.info(
            "Successfully updated craft",
            user_id=current_user.id,
            craft_id=craft_id,
            craft_code=craft.code
        )
        return craft
        
    except ValueError as e:
        logger.error(
            "ValueError in update_craft",
            error=str(e),
            user_id=current_user.id,
            craft_id=craft_id
        )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in update_craft",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            craft_id=craft_id,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update craft: {str(e)}"
        )


@router.delete("/{craft_id}")
@inject
async def delete_craft(
    craft_id: int,
    current_user: User = Depends(get_current_active_user),
    craft_use_cases: CraftUseCases = Depends(Provide[Container.craft_use_cases])
):
    """Delete craft (admin only)."""
    logger.info("Deleting craft", user_id=current_user.id, craft_id=craft_id)
    
    try:
        # Check if user has permission to delete crafts
        if not current_user.has_permission("crafts.delete"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to delete crafts"
            )
        
        deleted = await craft_use_cases.delete_craft(craft_id)
        if not deleted:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Craft not found"
            )
        
        logger.info(
            "Successfully deleted craft",
            user_id=current_user.id,
            craft_id=craft_id
        )
        return {"success": True, "message": "Craft deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in delete_craft",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            craft_id=craft_id,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete craft: {str(e)}"
        )


# Craft Route management endpoints
@router.post("/routes", response_model=CraftRouteResponseDTO)
@inject
async def create_craft_route(
    route_data: CraftRouteCreateDTO,
    current_user: User = Depends(get_current_active_user),
    craft_use_cases: CraftUseCases = Depends(Provide[Container.craft_use_cases])
):
    """Create a new craft route."""
    logger.info(
        "Creating new craft route",
        user_id=current_user.id,
        craft_code=route_data.craft_code,
        skill_code=route_data.skill_code
    )
    
    try:
        # Check if user has permission to manage crafts
        if not current_user.has_permission("crafts.manage"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to manage craft routes"
            )
        
        route = await craft_use_cases.create_craft_route(route_data)
        
        logger.info(
            "Successfully created craft route",
            route_id=route.id,
            craft_code=route.craft_code,
            skill_code=route.skill_code,
            created_by=current_user.id
        )
        return route
        
    except ValueError as e:
        logger.error(
            "ValueError in create_craft_route",
            error=str(e),
            user_id=current_user.id,
            craft_code=route_data.craft_code,
            skill_code=route_data.skill_code
        )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in create_craft_route",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            craft_code=route_data.craft_code,
            skill_code=route_data.skill_code,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create craft route: {str(e)}"
        )


@router.post("/routes/bulk", response_model=BulkCraftRouteOperationResultDTO)
@inject
async def create_bulk_craft_routes(
    bulk_data: BulkCraftRouteCreateDTO,
    current_user: User = Depends(get_current_active_user),
    craft_use_cases: CraftUseCases = Depends(Provide[Container.craft_use_cases])
):
    """Create multiple craft routes at once."""
    logger.info(
        "Creating bulk craft routes",
        user_id=current_user.id,
        craft_code=bulk_data.craft_code,
        routes_count=len(bulk_data.routes)
    )
    
    try:
        # Check if user has permission to manage crafts
        if not current_user.has_permission("crafts.manage"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to manage craft routes"
            )
        
        result = await craft_use_cases.create_bulk_craft_routes(bulk_data)
        
        logger.info(
            "Completed bulk craft routes creation",
            user_id=current_user.id,
            craft_code=bulk_data.craft_code,
            successful_count=result.successful_count,
            failed_count=result.failed_count
        )
        return result
        
    except ValueError as e:
        logger.error(
            "ValueError in create_bulk_craft_routes",
            error=str(e),
            user_id=current_user.id,
            craft_code=bulk_data.craft_code
        )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in create_bulk_craft_routes",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            craft_code=bulk_data.craft_code,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create bulk craft routes: {str(e)}"
        )


@router.get("/routes/craft/{craft_code}", response_model=CraftRouteListDTO)
@inject
async def get_craft_routes(
    craft_code: str,
    current_user: User = Depends(get_current_active_user),
    craft_use_cases: CraftUseCases = Depends(Provide[Container.craft_use_cases])
):
    """Get all routes for a specific craft."""
    logger.info("Getting craft routes", user_id=current_user.id, craft_code=craft_code)
    
    try:
        # Check if user has permission to view crafts
        if not current_user.has_any_permission(["crafts.view", "crafts.manage"]):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to view craft routes"
            )
        
        routes = await craft_use_cases.get_craft_routes(craft_code)
        
        logger.info(
            "Successfully retrieved craft routes",
            user_id=current_user.id,
            craft_code=craft_code,
            routes_count=routes.total
        )
        return routes
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in get_craft_routes",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            craft_code=craft_code,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve craft routes: {str(e)}"
        )


@router.get("/routes/skill/{skill_code}", response_model=CraftRouteListDTO)
@inject
async def get_skill_routes(
    skill_code: str,
    current_user: User = Depends(get_current_active_user),
    craft_use_cases: CraftUseCases = Depends(Provide[Container.craft_use_cases])
):
    """Get all routes that use a specific skill."""
    logger.info("Getting skill routes", user_id=current_user.id, skill_code=skill_code)
    
    try:
        # Check if user has permission to view crafts
        if not current_user.has_any_permission(["crafts.view", "crafts.manage"]):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to view craft routes"
            )
        
        routes = await craft_use_cases.get_skill_routes(skill_code)
        
        logger.info(
            "Successfully retrieved skill routes",
            user_id=current_user.id,
            skill_code=skill_code,
            routes_count=routes.total
        )
        return routes
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in get_skill_routes",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            skill_code=skill_code,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve skill routes: {str(e)}"
        )


@router.put("/routes/{route_id}", response_model=CraftRouteResponseDTO)
@inject
async def update_craft_route(
    route_id: int,
    route_data: CraftRouteUpdateDTO,
    current_user: User = Depends(get_current_active_user),
    craft_use_cases: CraftUseCases = Depends(Provide[Container.craft_use_cases])
):
    """Update craft route."""
    logger.info(
        "Updating craft route",
        user_id=current_user.id,
        route_id=route_id,
        update_data=route_data.model_dump(exclude_unset=True)
    )
    
    try:
        # Check if user has permission to manage crafts
        if not current_user.has_permission("crafts.manage"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to manage craft routes"
            )
        
        route = await craft_use_cases.update_craft_route(route_id, route_data)
        if not route:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Craft route not found"
            )
        
        logger.info(
            "Successfully updated craft route",
            user_id=current_user.id,
            route_id=route_id,
            craft_code=route.craft_code,
            skill_code=route.skill_code
        )
        return route
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in update_craft_route",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            route_id=route_id,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update craft route: {str(e)}"
        )


@router.delete("/routes/{route_id}")
@inject
async def delete_craft_route(
    route_id: int,
    current_user: User = Depends(get_current_active_user),
    craft_use_cases: CraftUseCases = Depends(Provide[Container.craft_use_cases])
):
    """Delete craft route."""
    logger.info("Deleting craft route", user_id=current_user.id, route_id=route_id)
    
    try:
        # Check if user has permission to manage crafts
        if not current_user.has_permission("crafts.manage"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to manage craft routes"
            )
        
        deleted = await craft_use_cases.delete_craft_route(route_id)
        if not deleted:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Craft route not found"
            )
        
        logger.info(
            "Successfully deleted craft route",
            user_id=current_user.id,
            route_id=route_id
        )
        return {"success": True, "message": "Craft route deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in delete_craft_route",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            route_id=route_id,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete craft route: {str(e)}"
        )


@router.post("/routes/reorder/{craft_code}", response_model=CraftOperationResultDTO)
@inject
async def reorder_craft_routes(
    craft_code: str,
    route_orders: List[dict],  # List of {"route_id": int, "order": int}
    current_user: User = Depends(get_current_active_user),
    craft_use_cases: CraftUseCases = Depends(Provide[Container.craft_use_cases])
):
    """Reorder routes for a craft."""
    logger.info(
        "Reordering craft routes",
        user_id=current_user.id,
        craft_code=craft_code,
        routes_count=len(route_orders)
    )
    
    try:
        # Check if user has permission to manage crafts
        if not current_user.has_permission("crafts.manage"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to manage craft routes"
            )
        
        # Convert to list of tuples
        order_tuples = [(item["route_id"], item["order"]) for item in route_orders]
        
        result = await craft_use_cases.reorder_craft_routes(craft_code, order_tuples)
        
        logger.info(
            "Craft routes reorder completed",
            user_id=current_user.id,
            craft_code=craft_code,
            success=result.success
        )
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in reorder_craft_routes",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            craft_code=craft_code,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to reorder craft routes: {str(e)}"
        )