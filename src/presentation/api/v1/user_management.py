from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Header, Query
from dependency_injector.wiring import Provide, inject

from src.infrastructure.containers import Container
from src.infrastructure.logging.logger import get_logger
from src.application.use_cases.user_management_use_cases import UserManagementUseCases
from src.application.dto.user_management_dto import (
    FactoryUserListDTO, AddUsersToFactoryDTO, BindUserRoleDTO, 
    SuspendUserDTO, RemoveUserFromFactoryDTO, UserSearchDTO, 
    AvailableUsersDTO, UserOperationResultDTO, CreateUserWithFactoryDTO,
    AddUserWithFactoryResponseDTO
)
from src.presentation.api.v1.auth import get_current_active_user
from src.presentation.api.v1.factory_management import get_current_factory_from_session
from src.domain.entities.user import User

router = APIRouter(prefix="/user-management", tags=["user-management"])
logger = get_logger(__name__)


@router.get("/user-list", response_model=FactoryUserListDTO)
@inject
async def get_factory_user_list(
    current_user: User = Depends(get_current_active_user),
    current_factory_id: int = Depends(get_current_factory_from_session),
    user_management_use_cases: UserManagementUseCases = Depends(Provide[Container.user_management_use_cases])
):
    """Get list of all users in the current factory."""
    logger.info(
        "Starting get_factory_user_list request",
        user_id=current_user.id,
        username=current_user.username,
        factory_id=current_factory_id
    )
    
    try:
        logger.debug("Checking user permissions for factory user view")
        
        # Check if user has permission to view factory users
        has_manage_permission = current_user.has_permission("factory.manage_users") if hasattr(current_user, 'has_permission') else False
        has_view_permission = current_user.has_permission("factory.view_users") if hasattr(current_user, 'has_permission') else False
        has_any_permission = current_user.has_any_permission(["factory.manage_users", "factory.view_users"]) if hasattr(current_user, 'has_any_permission') else False
        
        logger.debug(
            "Permission check results",
            has_manage_permission=has_manage_permission,
            has_view_permission=has_view_permission, 
            has_any_permission=has_any_permission,
            user_permissions=getattr(current_user, 'permissions', 'N/A'),
            user_roles=getattr(current_user, 'roles', 'N/A')
        )
        
        if not has_any_permission:
            logger.warning(
                "Permission denied for user",
                user_id=current_user.id,
                required_permissions=["factory.manage_users", "factory.view_users"]
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to view factory users"
            )
        
        logger.info("Calling user_management_use_cases.get_factory_users")
        user_list = await user_management_use_cases.get_factory_users(
            current_factory_id, current_user.id
        )
        
        logger.info(
            "Successfully retrieved factory users",
            factory_id=current_factory_id,
            user_count=len(user_list.users) if hasattr(user_list, 'users') else 'unknown'
        )
        return user_list
        
    except ValueError as e:
        logger.error(
            "ValueError in get_factory_user_list",
            error=str(e),
            user_id=current_user.id,
            factory_id=current_factory_id
        )
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )
    except HTTPException as e:
        logger.error(
            "HTTPException in get_factory_user_list", 
            status_code=e.status_code,
            detail=e.detail,
            user_id=current_user.id,
            factory_id=current_factory_id
        )
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in get_factory_user_list",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            factory_id=current_factory_id,
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve factory users: {str(e)}"
        )


@router.get("/available-users", response_model=AvailableUsersDTO)
@inject
async def get_available_users(
    search_term: Optional[str] = Query(None, description="Search by username, email, or full name"),
    role_id: Optional[int] = Query(None, description="Filter by system role"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    current_user: User = Depends(get_current_active_user),
    current_factory_id: int = Depends(get_current_factory_from_session),
    user_management_use_cases: UserManagementUseCases = Depends(Provide[Container.user_management_use_cases])
):
    """Get list of users available to add to the factory."""
    try:
        # Check if user has permission to manage factory users
        if not current_user.has_permission("factory.manage_users"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to view available users"
            )
        
        search_criteria = UserSearchDTO(
            search_term=search_term,
            role_id=role_id,
            is_active=is_active,
            not_in_factory=True
        ) if any([search_term, role_id is not None, is_active is not None]) else None
        
        available_users = await user_management_use_cases.get_available_users(
            current_factory_id, current_user.id, search_criteria
        )
        return available_users
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve available users"
        )


@router.post("/add-users", response_model=List[UserOperationResultDTO])
@inject
async def add_users_to_factory(
    add_data: AddUsersToFactoryDTO,
    current_user: User = Depends(get_current_active_user),
    current_factory_id: int = Depends(get_current_factory_from_session),
    user_management_use_cases: UserManagementUseCases = Depends(Provide[Container.user_management_use_cases])
):
    """Add multiple users to the current factory."""
    try:
        # Check if user has permission to manage factory users
        if not current_user.has_permission("factory.manage_users"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to add users to factory"
            )
        
        results = await user_management_use_cases.add_users_to_factory(
            current_factory_id, current_user.id, add_data
        )
        return results
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to add users to factory"
        )


@router.post("/bind-roles", response_model=UserOperationResultDTO)
@inject
async def bind_user_role(
    bind_data: BindUserRoleDTO,
    current_user: User = Depends(get_current_active_user),
    user_management_use_cases: UserManagementUseCases = Depends(Provide[Container.user_management_use_cases])
):
    """Bind system role to a user."""
    try:
        # Check if user has permission to manage user roles
        if not current_user.has_permission("users.manage_roles"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to manage user roles"
            )
        
        result = await user_management_use_cases.bind_user_role(bind_data, current_user.id)
        if not result.success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result.message
            )
        return result
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to bind user role"
        )


@router.post("/suspend", response_model=UserOperationResultDTO)
@inject
async def suspend_user_in_factory(
    suspend_data: SuspendUserDTO,
    current_user: User = Depends(get_current_active_user),
    current_factory_id: int = Depends(get_current_factory_from_session),
    user_management_use_cases: UserManagementUseCases = Depends(Provide[Container.user_management_use_cases])
):
    """Suspend a user in the current factory."""
    try:
        # Check if user has permission to manage factory users
        if not current_user.has_permission("factory.manage_users"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to suspend users"
            )
        
        result = await user_management_use_cases.suspend_user_in_factory(
            current_factory_id, suspend_data, current_user.id
        )
        if not result.success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result.message
            )
        return result
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to suspend user"
        )


@router.delete("/remove-user", response_model=UserOperationResultDTO)
@inject
async def remove_user_from_factory(
    remove_data: RemoveUserFromFactoryDTO,
    current_user: User = Depends(get_current_active_user),
    current_factory_id: int = Depends(get_current_factory_from_session),
    user_management_use_cases: UserManagementUseCases = Depends(Provide[Container.user_management_use_cases])
):
    """Remove a user from the current factory."""
    try:
        # Check if user has permission to manage factory users
        if not current_user.has_permission("factory.manage_users"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to remove users"
            )
        
        result = await user_management_use_cases.remove_user_from_factory(
            current_factory_id, remove_data, current_user.id
        )
        if not result.success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result.message
            )
        return result
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to remove user from factory"
        )


@router.post("/create-user", response_model=AddUserWithFactoryResponseDTO)
@inject
async def create_user_with_factory(
    user_data: CreateUserWithFactoryDTO,
    current_user: User = Depends(get_current_active_user),
    current_factory_id: int = Depends(get_current_factory_from_session),
    user_management_use_cases: UserManagementUseCases = Depends(Provide[Container.user_management_use_cases])
):
    """Create new user or add existing user to factory with skills and complete information."""
    try:
        # Check if user has permission to manage factory users
        if not current_user.has_permission("factory.manage_users"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to create/add users"
            )
        
        result = await user_management_use_cases.create_or_add_user_to_factory(
            current_factory_id, current_user.id, user_data
        )
        
        if not result.success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result.message
            )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create/add user to factory"
        )