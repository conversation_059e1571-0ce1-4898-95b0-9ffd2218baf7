from abc import ABC, abstractmethod
from typing import Dict, Any
import httpx
from src.infrastructure.logging import get_logger

logger = get_logger(__name__)


class SMSServiceInterface(ABC):
    """Abstract interface for SMS service."""
    
    @abstractmethod
    async def send_sms(self, phone: str, message: str) -> bool:
        """Send SMS message."""
        pass
    
    @abstractmethod
    async def send_verification_code(self, phone: str, code: str) -> bool:
        """Send verification code via SMS."""
        pass


class MockSMSService(SMSServiceInterface):
    """Mock SMS service for development and testing."""
    
    async def send_sms(self, phone: str, message: str) -> bool:
        """Mock SMS sending."""
        logger.info(f"Mock SMS sent to {phone}: {message}")
        return True
    
    async def send_verification_code(self, phone: str, code: str) -> bool:
        """Mock verification code sending."""
        message = f"Your verification code is: {code}. Valid for 5 minutes."
        logger.info(f"Mock SMS verification code sent to {phone}: {code}")
        return True


class AlibabaCloudSMSService(SMSServiceInterface):
    """Alibaba Cloud SMS service implementation."""
    
    def __init__(self, access_key_id: str, access_key_secret: str, sign_name: str, template_code: str):
        self.access_key_id = access_key_id
        self.access_key_secret = access_key_secret
        self.sign_name = sign_name
        self.template_code = template_code
        self.endpoint = "https://dysmsapi.aliyuncs.com"
    
    async def send_sms(self, phone: str, message: str) -> bool:
        """Send SMS via Alibaba Cloud."""
        # Implementation would use Alibaba Cloud SDK
        logger.warning("Alibaba Cloud SMS not implemented - using mock")
        return await MockSMSService().send_sms(phone, message)
    
    async def send_verification_code(self, phone: str, code: str) -> bool:
        """Send verification code via Alibaba Cloud SMS."""
        try:
            # This is a placeholder implementation
            # In production, you would use the actual Alibaba Cloud SDK
            params = {
                "PhoneNumbers": phone,
                "SignName": self.sign_name,
                "TemplateCode": self.template_code,
                "TemplateParam": f'{{"code":"{code}"}}'
            }
            
            logger.info(f"Would send SMS to {phone} with code {code} using Alibaba Cloud")
            # For now, fall back to mock
            return await MockSMSService().send_verification_code(phone, code)
            
        except Exception as e:
            logger.error(f"Failed to send SMS via Alibaba Cloud: {e}")
            return False


class TencentCloudSMSService(SMSServiceInterface):
    """Tencent Cloud SMS service implementation."""
    
    def __init__(self, secret_id: str, secret_key: str, sdk_app_id: str, template_id: str, sign: str):
        self.secret_id = secret_id
        self.secret_key = secret_key
        self.sdk_app_id = sdk_app_id
        self.template_id = template_id
        self.sign = sign
    
    async def send_sms(self, phone: str, message: str) -> bool:
        """Send SMS via Tencent Cloud."""
        logger.warning("Tencent Cloud SMS not implemented - using mock")
        return await MockSMSService().send_sms(phone, message)
    
    async def send_verification_code(self, phone: str, code: str) -> bool:
        """Send verification code via Tencent Cloud SMS."""
        try:
            # This is a placeholder implementation
            # In production, you would use the actual Tencent Cloud SDK
            logger.info(f"Would send SMS to {phone} with code {code} using Tencent Cloud")
            # For now, fall back to mock
            return await MockSMSService().send_verification_code(phone, code)
            
        except Exception as e:
            logger.error(f"Failed to send SMS via Tencent Cloud: {e}")
            return False


def create_sms_service() -> SMSServiceInterface:
    """Factory function to create SMS service based on configuration."""
    # For development, use mock service
    # In production, configure with actual SMS provider
    return MockSMSService()