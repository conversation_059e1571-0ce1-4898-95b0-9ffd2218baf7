from abc import ABC, abstractmethod
from typing import Optional
import json
import uuid
from src.domain.value_objects.user_session import UserSession
from src.infrastructure.external_services.redis_service import RedisService
from src.infrastructure.logging import get_logger

logger = get_logger(__name__)


class SessionServiceInterface(ABC):
    """Abstract interface for session service."""
    
    @abstractmethod
    async def create_session(self, user_session: UserSession) -> str:
        """Create a new session and return session ID."""
        pass
    
    @abstractmethod
    async def get_session(self, session_id: str) -> Optional[UserSession]:
        """Get session by session ID."""
        pass
    
    @abstractmethod
    async def update_session(self, session_id: str, user_session: UserSession) -> bool:
        """Update existing session."""
        pass
    
    @abstractmethod
    async def delete_session(self, session_id: str) -> bool:
        """Delete session."""
        pass
    
    @abstractmethod
    async def extend_session(self, session_id: str, expire_seconds: int = 86400) -> bool:
        """Extend session expiration."""
        pass


class RedisSessionService(SessionServiceInterface):
    """Redis-based session service implementation."""
    
    def __init__(self, redis_service: RedisService, session_prefix: str = "session:"):
        self.redis_service = redis_service
        self.session_prefix = session_prefix
        self.default_expire_seconds = 86400  # 24 hours
    
    def _get_session_key(self, session_id: str) -> str:
        """Get Redis key for session."""
        return f"{self.session_prefix}{session_id}"
    
    async def create_session(self, user_session: UserSession) -> str:
        """Create a new session and return session ID."""
        session_id = str(uuid.uuid4())
        session_key = self._get_session_key(session_id)
        
        session_data = {
            "user_id": user_session.user_id,
            "username": user_session.username,
            "current_factory_id": user_session.current_factory_id,
            "current_factory_name": user_session.current_factory_name,
            "current_department_id": user_session.current_department_id,
            "current_role": user_session.current_role,
            "is_factory_manager": user_session.is_factory_manager,
            "session_created_at": user_session.session_created_at.isoformat()
        }
        
        success = await self.redis_service.set(
            session_key, 
            session_data, 
            expire=self.default_expire_seconds
        )
        
        if success:
            logger.info(f"Created session {session_id} for user {user_session.user_id}")
            return session_id
        else:
            raise RuntimeError("Failed to create session")
    
    async def get_session(self, session_id: str) -> Optional[UserSession]:
        """Get session by session ID."""
        session_key = self._get_session_key(session_id)
        session_data = await self.redis_service.get(session_key)
        
        if not session_data:
            return None
        
        try:
            from datetime import datetime
            return UserSession(
                user_id=session_data["user_id"],
                username=session_data["username"],
                current_factory_id=session_data.get("current_factory_id"),
                current_factory_name=session_data.get("current_factory_name"),
                current_department_id=session_data.get("current_department_id"),
                current_role=session_data.get("current_role"),
                is_factory_manager=session_data.get("is_factory_manager", False),
                session_created_at=datetime.fromisoformat(session_data["session_created_at"])
            )
        except (KeyError, ValueError) as e:
            logger.error(f"Invalid session data for {session_id}: {e}")
            return None
    
    async def update_session(self, session_id: str, user_session: UserSession) -> bool:
        """Update existing session."""
        session_key = self._get_session_key(session_id)
        
        # Check if session exists
        exists = await self.redis_service.exists(session_key)
        if not exists:
            return False
        
        session_data = {
            "user_id": user_session.user_id,
            "username": user_session.username,
            "current_factory_id": user_session.current_factory_id,
            "current_factory_name": user_session.current_factory_name,
            "current_department_id": user_session.current_department_id,
            "current_role": user_session.current_role,
            "is_factory_manager": user_session.is_factory_manager,
            "session_created_at": user_session.session_created_at.isoformat()
        }
        
        success = await self.redis_service.set(
            session_key, 
            session_data, 
            expire=self.default_expire_seconds
        )
        
        if success:
            logger.info(f"Updated session {session_id} for user {user_session.user_id}")
        
        return success
    
    async def delete_session(self, session_id: str) -> bool:
        """Delete session."""
        session_key = self._get_session_key(session_id)
        success = await self.redis_service.delete(session_key)
        
        if success:
            logger.info(f"Deleted session {session_id}")
        
        return success
    
    async def extend_session(self, session_id: str, expire_seconds: int = 86400) -> bool:
        """Extend session expiration."""
        session_key = self._get_session_key(session_id)
        return await self.redis_service.expire(session_key, expire_seconds)


def create_session_service(redis_service: RedisService) -> SessionServiceInterface:
    """Factory function to create session service."""
    return RedisSessionService(redis_service)