from celery import Celery
from config import settings

# Create Celery instance
celery_app = Celery(
    "production_ticket",
    broker=settings.celery.broker_url,
    backend=settings.celery.result_backend,
    include=["src.infrastructure.external_services.tasks"]
)

# Celery configuration
celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
    result_expires=60 * 60 * 24,  # 24 hours
    worker_prefetch_multiplier=1,
    task_acks_late=True,
    worker_disable_rate_limits=True,
)

# Auto-discover tasks
celery_app.autodiscover_tasks()