from abc import ABC, abstractmethod
from typing import Tuple
import base64
import io
import random
import string
from src.infrastructure.logging import get_logger

logger = get_logger(__name__)


class CaptchaServiceInterface(ABC):
    """Abstract interface for captcha service."""
    
    @abstractmethod
    async def generate_image_captcha(self, text: str, width: int = 200, height: int = 80) -> str:
        """Generate image captcha and return base64 encoded string."""
        pass


class SimpleCaptchaService(CaptchaServiceInterface):
    """Simple captcha service implementation."""
    
    def __init__(self):
        self.colors = [
            (255, 0, 0),    # Red
            (0, 255, 0),    # Green
            (0, 0, 255),    # Blue
            (255, 165, 0),  # Orange
            (128, 0, 128),  # Purple
        ]
        self.background_color = (255, 255, 255)  # White
    
    async def generate_image_captcha(self, text: str, width: int = 200, height: int = 80) -> str:
        """Generate simple image captcha."""
        try:
            from PIL import Image, ImageDraw, ImageFont
            # Create image
            image = Image.new('RGB', (width, height), self.background_color)
            draw = ImageDraw.Draw(image)
            
            # Add noise lines
            for _ in range(8):
                x1 = random.randint(0, width)
                y1 = random.randint(0, height)
                x2 = random.randint(0, width)
                y2 = random.randint(0, height)
                draw.line([(x1, y1), (x2, y2)], fill=random.choice(self.colors), width=2)
            
            # Add noise dots
            for _ in range(50):
                x = random.randint(0, width)
                y = random.randint(0, height)
                draw.point((x, y), fill=random.choice(self.colors))
            
            # Draw text
            try:
                # Try to use a better font if available
                font = ImageFont.truetype("arial.ttf", 36)
            except:
                try:
                    # Try alternative system fonts
                    font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 36)
                except:
                    # Fall back to default font with larger size
                    font = ImageFont.load_default()
            
            # Calculate text position
            text_width = draw.textlength(text, font=font)
            text_height = 36  # Font size
            x = (width - text_width) // 2
            y = (height - text_height) // 2
            
            # Draw each character with random color and slight rotation
            char_width = text_width // len(text)
            for i, char in enumerate(text):
                char_x = x + i * char_width
                char_y = y + random.randint(-8, 8)  # Random vertical offset
                color = random.choice(self.colors)
                draw.text((char_x, char_y), char, fill=color, font=font)
            
            # Convert to base64
            buffered = io.BytesIO()
            image.save(buffered, format="PNG")
            img_base64 = base64.b64encode(buffered.getvalue()).decode()
            
            return f"data:image/png;base64,{img_base64}"
            
        except Exception as e:
            logger.error(f"Failed to generate captcha image: {e}")
            # Return a simple placeholder
            return "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="


class MockCaptchaService(CaptchaServiceInterface):
    """Mock captcha service for testing."""
    
    async def generate_image_captcha(self, text: str, width: int = 200, height: int = 80) -> str:
        """Generate mock captcha image."""
        logger.info(f"Mock captcha generated for text: {text}")
        # Return a simple base64 image placeholder
        return "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="


def create_captcha_service() -> CaptchaServiceInterface:
    """Factory function to create captcha service."""
    try:
        # Try to import PIL to check if it's available
        from PIL import Image
        # If successful, use the simple captcha service
        return SimpleCaptchaService()
    except ImportError:
        # Fall back to mock service if PIL is not available
        logger.warning("PIL not available, using mock captcha service")
        return MockCaptchaService()