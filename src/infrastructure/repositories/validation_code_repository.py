from typing import Optional, Callable
from datetime import datetime, timed<PERSON>ta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, delete, func
from src.domain.entities.validation_code_record import ValidationCodeRecord
from src.domain.value_objects.validation_code import ValidationCodeType
from src.application.interfaces.validation_code_repository_interface import ValidationCodeRepositoryInterface


class ValidationCodeRepository(ValidationCodeRepositoryInterface):
    """SQLAlchemy implementation of validation code repository."""
    
    def __init__(self, session_factory: Callable[[], AsyncSession]):
        self.session_factory = session_factory
    
    async def create(self, validation_code: ValidationCodeRecord) -> ValidationCodeRecord:
        async with self.session_factory() as session:
            session.add(validation_code)
            await session.commit()
            await session.refresh(validation_code)
            return validation_code
    
    async def get_latest_by_identifier_and_type(
        self, 
        identifier: str, 
        code_type: ValidationCodeType
    ) -> Optional[ValidationCodeRecord]:
        async with self.session_factory() as session:
            result = await session.execute(
                select(ValidationCodeRecord)
                .where(
                    and_(
                        ValidationCodeRecord.identifier == identifier,
                        ValidationCodeRecord.code_type == code_type
                    )
                )
                .order_by(ValidationCodeRecord.created_at.desc())
                .limit(1)
            )
            return result.scalar_one_or_none()
    
    async def mark_as_used(self, validation_code_id: int) -> bool:
        async with self.session_factory() as session:
            validation_code = await session.get(ValidationCodeRecord, validation_code_id)
            if validation_code:
                validation_code.mark_as_used()
                await session.commit()
                return True
            return False
    
    async def delete_expired_codes(self) -> int:
        async with self.session_factory() as session:
            now = datetime.utcnow()
            result = await session.execute(
                delete(ValidationCodeRecord)
                .where(ValidationCodeRecord.expires_at < now)
            )
            await session.commit()
            return result.rowcount or 0
    
    async def get_by_id(self, validation_code_id: int) -> Optional[ValidationCodeRecord]:
        async with self.session_factory() as session:
            result = await session.execute(
                select(ValidationCodeRecord)
                .where(ValidationCodeRecord.id == validation_code_id)
            )
            return result.scalar_one_or_none()
    
    async def count_recent_codes(
        self, 
        identifier: str, 
        code_type: ValidationCodeType, 
        minutes: int = 60
    ) -> int:
        async with self.session_factory() as session:
            cutoff_time = datetime.utcnow() - timedelta(minutes=minutes)
            result = await session.execute(
                select(func.count(ValidationCodeRecord.id))
                .where(
                    and_(
                        ValidationCodeRecord.identifier == identifier,
                        ValidationCodeRecord.code_type == code_type,
                        ValidationCodeRecord.created_at >= cutoff_time
                    )
                )
            )
            return result.scalar() or 0