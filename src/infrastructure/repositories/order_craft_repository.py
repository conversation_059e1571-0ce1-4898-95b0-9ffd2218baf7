from typing import List, Optional, Callable
from datetime import datetime, timezone
from sqlalchemy import select, delete, and_, or_, func, update
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from src.application.interfaces.order_craft_repository_interface import OrderCraftRepositoryInterface
from src.domain.entities.order_craft import OrderCraft


class OrderCraftRepository(OrderCraftRepositoryInterface):
    """Repository implementation for OrderCraft entity."""
    
    def __init__(self, session_factory: Callable[[], AsyncSession]):
        self.session_factory = session_factory
    
    async def create(self, order_craft: OrderCraft) -> OrderCraft:
        """Create a new order craft."""
        async with self.session_factory() as session:
            session.add(order_craft)
            await session.commit()
            await session.refresh(order_craft)
            return order_craft
    
    async def get_by_id(self, order_craft_id: int) -> Optional[OrderCraft]:
        """Get order craft by ID."""
        async with self.session_factory() as session:
            result = await session.execute(
                select(OrderCraft)
                .options(
                    selectinload(OrderCraft.order_entity),
                    selectinload(OrderCraft.craft),
                    selectinload(OrderCraft.order_craft_routes)
                )
                .where(OrderCraft.id == order_craft_id)
            )
            return result.scalar_one_or_none()
    
    async def get_by_order_no(self, order_no: str) -> List[OrderCraft]:
        """Get all order crafts for a specific order."""
        async with self.session_factory() as session:
            result = await session.execute(
                select(OrderCraft)
                .options(
                    selectinload(OrderCraft.order_entity),
                    selectinload(OrderCraft.craft),
                    selectinload(OrderCraft.order_craft_routes)
                )
                .where(OrderCraft.order_no == order_no)
                .order_by(OrderCraft.order.asc())
            )
            return list(result.scalars().all())
    
    async def get_by_order_and_craft(self, order_no: str, craft_code: str) -> Optional[OrderCraft]:
        """Get order craft by order number and craft code."""
        async with self.session_factory() as session:
            result = await session.execute(
                select(OrderCraft)
                .options(
                    selectinload(OrderCraft.order_entity),
                    selectinload(OrderCraft.craft),
                    selectinload(OrderCraft.order_craft_routes)
                )
                .where(
                    and_(
                        OrderCraft.order_no == order_no,
                        OrderCraft.craft_code == craft_code
                    )
                )
            )
            return result.scalar_one_or_none()
    
    async def get_by_craft_code(self, craft_code: str) -> List[OrderCraft]:
        """Get all order crafts using a specific craft."""
        async with self.session_factory() as session:
            result = await session.execute(
                select(OrderCraft)
                .options(
                    selectinload(OrderCraft.order_entity),
                    selectinload(OrderCraft.craft),
                    selectinload(OrderCraft.order_craft_routes)
                )
                .where(OrderCraft.craft_code == craft_code)
                .order_by(OrderCraft.created_at.desc())
            )
            return list(result.scalars().all())
    
    async def get_active_by_order(self, order_no: str) -> List[OrderCraft]:
        """Get active order crafts for an order."""
        async with self.session_factory() as session:
            result = await session.execute(
                select(OrderCraft)
                .options(
                    selectinload(OrderCraft.order_entity),
                    selectinload(OrderCraft.craft),
                    selectinload(OrderCraft.order_craft_routes)
                )
                .where(
                    and_(
                        OrderCraft.order_no == order_no,
                        OrderCraft.is_active == True
                    )
                )
                .order_by(OrderCraft.order.asc())
            )
            return list(result.scalars().all())
    
    async def get_by_status(self, status: str) -> List[OrderCraft]:
        """Get order crafts by status."""
        async with self.session_factory() as session:
            result = await session.execute(
                select(OrderCraft)
                .options(
                    selectinload(OrderCraft.order_entity),
                    selectinload(OrderCraft.craft),
                    selectinload(OrderCraft.order_craft_routes)
                )
                .where(OrderCraft.status == status)
                .order_by(OrderCraft.created_at.desc())
            )
            return list(result.scalars().all())
    
    async def get_next_pending_craft(self, order_no: str) -> Optional[OrderCraft]:
        """Get the next pending craft in the workflow for an order."""
        async with self.session_factory() as session:
            result = await session.execute(
                select(OrderCraft)
                .options(
                    selectinload(OrderCraft.order_entity),
                    selectinload(OrderCraft.craft),
                    selectinload(OrderCraft.order_craft_routes)
                )
                .where(
                    and_(
                        OrderCraft.order_no == order_no,
                        OrderCraft.status == "pending",
                        OrderCraft.is_active == True
                    )
                )
                .order_by(OrderCraft.order.asc())
                .limit(1)
            )
            return result.scalar_one_or_none()
    
    async def get_current_in_progress_craft(self, order_no: str) -> Optional[OrderCraft]:
        """Get the currently in-progress craft for an order."""
        async with self.session_factory() as session:
            result = await session.execute(
                select(OrderCraft)
                .options(
                    selectinload(OrderCraft.order_entity),
                    selectinload(OrderCraft.craft),
                    selectinload(OrderCraft.order_craft_routes)
                )
                .where(
                    and_(
                        OrderCraft.order_no == order_no,
                        OrderCraft.status == "in_progress"
                    )
                )
                .order_by(OrderCraft.order.asc())
                .limit(1)
            )
            return result.scalar_one_or_none()
    
    async def update(self, order_craft: OrderCraft) -> OrderCraft:
        """Update order craft."""
        async with self.session_factory() as session:
            await session.merge(order_craft)
            await session.commit()
            await session.refresh(order_craft)
            return order_craft
    
    async def delete(self, order_craft_id: int) -> bool:
        """Delete order craft."""
        async with self.session_factory() as session:
            result = await session.execute(
                delete(OrderCraft).where(OrderCraft.id == order_craft_id)
            )
            await session.commit()
            return result.rowcount > 0
    
    async def delete_by_order_no(self, order_no: str) -> int:
        """Delete all order crafts for an order. Returns number of deleted records."""
        async with self.session_factory() as session:
            result = await session.execute(
                delete(OrderCraft).where(OrderCraft.order_no == order_no)
            )
            await session.commit()
            return result.rowcount
    
    async def bulk_create(self, order_crafts: List[OrderCraft]) -> List[OrderCraft]:
        """Create multiple order crafts at once."""
        async with self.session_factory() as session:
            session.add_all(order_crafts)
            await session.commit()
            
            # Refresh all objects
            for order_craft in order_crafts:
                await session.refresh(order_craft)
            
            return order_crafts
    
    async def reorder_crafts(self, order_no: str, craft_orders: List[tuple]) -> bool:
        """Reorder crafts for an order. craft_orders is list of (craft_code, new_order) tuples."""
        async with self.session_factory() as session:
            try:
                for craft_code, new_order in craft_orders:
                    await session.execute(
                        update(OrderCraft)
                        .where(
                            and_(
                                OrderCraft.order_no == order_no,
                                OrderCraft.craft_code == craft_code
                            )
                        )
                        .values(order=new_order)
                    )
                await session.commit()
                return True
            except Exception:
                await session.rollback()
                return False
    
    async def get_order_craft_statistics(self, order_no: Optional[str] = None) -> dict:
        """Get order craft statistics, optionally filtered by order."""
        async with self.session_factory() as session:
            base_query = select(OrderCraft)
            
            if order_no:
                base_query = base_query.where(OrderCraft.order_no == order_no)
            
            # Total count
            total_result = await session.execute(
                select(func.count(OrderCraft.id)).select_from(base_query.subquery())
            )
            total_order_crafts = total_result.scalar()
            
            # Status breakdown
            status_result = await session.execute(
                select(OrderCraft.status, func.count(OrderCraft.id))
                .select_from(base_query.subquery())
                .group_by(OrderCraft.status)
            )
            status_breakdown = dict(status_result.all())
            
            # Active/inactive breakdown
            active_result = await session.execute(
                select(OrderCraft.is_active, func.count(OrderCraft.id))
                .select_from(base_query.subquery())
                .group_by(OrderCraft.is_active)
            )
            active_breakdown = dict(active_result.all())
            
            return {
                "total_order_crafts": total_order_crafts,
                "status_breakdown": status_breakdown,
                "active_breakdown": active_breakdown,
                "pending_crafts": status_breakdown.get("pending", 0),
                "in_progress_crafts": status_breakdown.get("in_progress", 0),
                "completed_crafts": status_breakdown.get("completed", 0),
                "skipped_crafts": status_breakdown.get("skipped", 0),
                "active_crafts": active_breakdown.get(True, 0),
                "inactive_crafts": active_breakdown.get(False, 0)
            }
    
    async def bulk_upsert(self, order_crafts: List[OrderCraft], session=None) -> List[OrderCraft]:
        """Create or update multiple order crafts. Uses order_no + craft_code as unique key."""
        
        async def _do_upsert(sess: AsyncSession) -> List[OrderCraft]:
            # Disable autoflush to prevent premature flushing during queries
            with sess.no_autoflush:
                result_crafts = []
                
                # Get all craft codes for this order
                craft_codes = [craft.craft_code for craft in order_crafts]
                order_no = order_crafts[0].order_no if order_crafts else None
                
                if not order_no:
                    return []
                
                # Get existing order crafts
                existing_crafts = await self.get_by_order_and_codes(order_no, craft_codes, sess)
                existing_by_code = {craft.craft_code: craft for craft in existing_crafts}
                
                for order_craft in order_crafts:
                    if order_craft.craft_code in existing_by_code:
                        # Update existing
                        existing_craft = existing_by_code[order_craft.craft_code]
                        existing_craft.craft_name = order_craft.craft_name
                        existing_craft.order = order_craft.order
                        existing_craft.is_required = order_craft.is_required
                        existing_craft.is_active = order_craft.is_active
                        existing_craft.estimated_duration_hours = order_craft.estimated_duration_hours
                        existing_craft.notes = order_craft.notes
                        existing_craft.updated_at = order_craft.updated_at
                        result_crafts.append(existing_craft)
                    else:
                        # Create new - ensure defaults are set
                        if order_craft.is_active is None:
                            order_craft.is_active = True
                        if order_craft.status is None:
                            order_craft.status = "pending"
                        if order_craft.created_at is None:
                            order_craft.created_at = datetime.now(timezone.utc)
                        if order_craft.updated_at is None:
                            order_craft.updated_at = datetime.now(timezone.utc)
                        sess.add(order_craft)
                        result_crafts.append(order_craft)
            
            # Always flush to get IDs for new entities
            await sess.flush()
            
            # Refresh objects that need IDs
            for craft in result_crafts:
                if craft.id is None:
                    await sess.refresh(craft)
            
            return result_crafts
        
        if session:
            # Use provided session
            return await _do_upsert(session)
        else:
            # Create new session
            async with self.session_factory() as sess:
                result = await _do_upsert(sess)
                await sess.commit()
                return result
    
    async def get_by_order_and_codes(self, order_no: str, craft_codes: List[str], session=None) -> List[OrderCraft]:
        """Get order crafts by order number and craft codes."""
        if session:
            # Use provided session directly
            result = await session.execute(
                select(OrderCraft)
                .options(
                    selectinload(OrderCraft.order_entity),
                    selectinload(OrderCraft.craft),
                    selectinload(OrderCraft.order_craft_routes)
                )
                .where(
                    and_(
                        OrderCraft.order_no == order_no,
                        OrderCraft.craft_code.in_(craft_codes)
                    )
                )
            )
            return list(result.scalars().all())
        else:
            # Create new session
            async with self.session_factory() as sess:
                result = await sess.execute(
                    select(OrderCraft)
                    .options(
                        selectinload(OrderCraft.order_entity),
                        selectinload(OrderCraft.craft),
                        selectinload(OrderCraft.order_craft_routes)
                    )
                    .where(
                        and_(
                            OrderCraft.order_no == order_no,
                            OrderCraft.craft_code.in_(craft_codes)
                        )
                    )
                )
                return list(result.scalars().all())