from typing import List, Optional, Callable
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, or_, and_, func
from sqlalchemy.orm import selectinload
from src.domain.entities.order import Order, OrderStatus
from src.domain.entities.order_line import OrderLine
from src.domain.entities.user import User
from src.domain.entities.craft import Craft
from src.domain.entities.craft_route import CraftRoute
from src.application.interfaces.order_repository_interface import OrderRepositoryInterface
from typing import Optional as TypingOptional  # Add this line


class OrderRepository(OrderRepositoryInterface):
    """SQLAlchemy implementation of order repository."""
    
    def __init__(self, session_factory: Callable[[], AsyncSession]):
        self.session_factory = session_factory
    
    async def create(self, order: Order, session: TypingOptional[AsyncSession] = None) -> Order:
        if session is not None:
            session.add(order)
            await session.flush()
            await session.refresh(order)
            return order
        else:
            async with self.session_factory() as session:
                session.add(order)
                await session.commit()
                await session.refresh(order)
                return order
    
    async def get_by_id(self, order_id: int) -> Optional[Order]:
        async with self.session_factory() as session:
            result = await session.execute(
                select(Order)
                .options(
                    selectinload(Order.owner),
                    selectinload(Order.craft),
                    selectinload(Order.craft_route),
                    selectinload(Order.order_lines)
                )
                .where(Order.id == order_id)
            )
            return result.scalar_one_or_none()
    
    async def get_by_order_no(self, order_no: str, session: TypingOptional[AsyncSession] = None) -> Optional[Order]:
        if session is not None:
            result = await session.execute(
                select(Order)
                .options(
                    selectinload(Order.owner),
                    selectinload(Order.craft),
                    selectinload(Order.craft_route),
                    selectinload(Order.order_lines)
                )
                .where(Order.order_no == order_no)
            )
            return result.scalar_one_or_none()
        else:
            async with self.session_factory() as session:
                result = await session.execute(
                    select(Order)
                    .options(
                        selectinload(Order.owner),
                        selectinload(Order.craft),
                        selectinload(Order.craft_route),
                        selectinload(Order.order_lines)
                    )
                    .where(Order.order_no == order_no)
                )
                return result.scalar_one_or_none()
    
    async def get_by_order_no_and_factory(self, order_no: str, factory_id: int, session: TypingOptional[AsyncSession] = None) -> Optional[Order]:
        if session is not None:
            result = await session.execute(
                select(Order)
                .options(
                    selectinload(Order.owner),
                    selectinload(Order.craft),
                    selectinload(Order.craft_route),
                    selectinload(Order.order_lines)
                )
                .where(and_(Order.order_no == order_no, Order.factory_id == factory_id))
            )
            return result.scalar_one_or_none()
        else:
            async with self.session_factory() as session:
                result = await session.execute(
                    select(Order)
                    .options(
                        selectinload(Order.owner),
                        selectinload(Order.craft),
                        selectinload(Order.craft_route),
                        selectinload(Order.order_lines)
                    )
                    .where(and_(Order.order_no == order_no, Order.factory_id == factory_id))
                )
                return result.scalar_one_or_none()
    
    async def get_by_external_order_no(self, external_order_no: str) -> List[Order]:
        async with self.session_factory() as session:
            result = await session.execute(
                select(Order)
                .options(
                    selectinload(Order.owner),
                    selectinload(Order.craft),
                    selectinload(Order.craft_route),
                    selectinload(Order.order_lines)
                )
                .where(
                    or_(
                        Order.external_order_no == external_order_no,
                        Order.external_order_no2 == external_order_no
                    )
                )
                .order_by(Order.created_at.desc())
            )
            return list(result.scalars().all())
    
    async def get_by_skc_no(self, skc_no: str) -> List[Order]:
        async with self.session_factory() as session:
            result = await session.execute(
                select(Order)
                .options(
                    selectinload(Order.owner),
                    selectinload(Order.craft),
                    selectinload(Order.craft_route),
                    selectinload(Order.order_lines)
                )
                .where(
                    or_(
                        Order.skc_no == skc_no,
                        Order.external_skc_no == skc_no
                    )
                )
                .order_by(Order.created_at.desc())
            )
            return list(result.scalars().all())
    
    async def get_all(self, skip: int = 0, limit: int = 100) -> List[Order]:
        async with self.session_factory() as session:
            result = await session.execute(
                select(Order)
                .options(
                    selectinload(Order.owner),
                    selectinload(Order.craft),
                    selectinload(Order.craft_route),
                    selectinload(Order.order_lines)
                )
                .order_by(Order.created_at.desc())
                .offset(skip)
                .limit(limit)
            )
            return list(result.scalars().all())
    
    async def get_by_status(self, status: OrderStatus) -> List[Order]:
        async with self.session_factory() as session:
            result = await session.execute(
                select(Order)
                .options(
                    selectinload(Order.owner),
                    selectinload(Order.craft),
                    selectinload(Order.craft_route),
                    selectinload(Order.order_lines)
                )
                .where(Order.status == status)
                .order_by(Order.created_at.desc())
            )
            return list(result.scalars().all())
    
    async def get_by_owner(self, owner_user_id: int) -> List[Order]:
        async with self.session_factory() as session:
            result = await session.execute(
                select(Order)
                .options(
                    selectinload(Order.owner),
                    selectinload(Order.craft),
                    selectinload(Order.craft_route),
                    selectinload(Order.order_lines)
                )
                .where(Order.owner_user_id == owner_user_id)
                .order_by(Order.created_at.desc())
            )
            return list(result.scalars().all())
    
    async def get_by_date_range(
        self, 
        start_date: datetime, 
        end_date: datetime,
        date_field: str = "created_at"
    ) -> List[Order]:
        async with self.session_factory() as session:
            date_column = getattr(Order, date_field)
            result = await session.execute(
                select(Order)
                .options(
                    selectinload(Order.owner),
                    selectinload(Order.craft),
                    selectinload(Order.craft_route),
                    selectinload(Order.order_lines)
                )
                .where(
                    and_(
                        date_column >= start_date,
                        date_column <= end_date
                    )
                )
                .order_by(Order.created_at.desc())
            )
            return list(result.scalars().all())
    
    async def get_active_orders(self) -> List[Order]:
        async with self.session_factory() as session:
            active_statuses = [
                OrderStatus.PENDING,
                OrderStatus.IN_PROGRESS,
                OrderStatus.ON_HOLD,
                OrderStatus.DELAYED
            ]
            result = await session.execute(
                select(Order)
                .options(
                    selectinload(Order.owner),
                    selectinload(Order.craft),
                    selectinload(Order.craft_route),
                    selectinload(Order.order_lines)
                )
                .where(Order.status.in_(active_statuses))
                .order_by(Order.created_at.desc())
            )
            return list(result.scalars().all())
    
    async def get_overdue_orders(self, current_date: Optional[datetime] = None) -> List[Order]:
        if current_date is None:
            current_date = datetime.utcnow()
        
        async with self.session_factory() as session:
            result = await session.execute(
                select(Order)
                .options(
                    selectinload(Order.owner),
                    selectinload(Order.craft),
                    selectinload(Order.craft_route),
                    selectinload(Order.order_lines)
                )
                .where(
                    and_(
                        Order.expect_finished_at.is_not(None),
                        Order.expect_finished_at < current_date,
                        Order.status.in_([
                            OrderStatus.PENDING,
                            OrderStatus.IN_PROGRESS,
                            OrderStatus.ON_HOLD,
                            OrderStatus.DELAYED
                        ])
                    )
                )
                .order_by(Order.expect_finished_at.asc())
            )
            return list(result.scalars().all())
    
    async def search_orders(self, search_term: str) -> List[Order]:
        async with self.session_factory() as session:
            search_pattern = f"%{search_term}%"
            result = await session.execute(
                select(Order)
                .options(
                    selectinload(Order.owner),
                    selectinload(Order.craft),
                    selectinload(Order.craft_route),
                    selectinload(Order.order_lines)
                )
                .where(
                    or_(
                        Order.order_no.ilike(search_pattern),
                        Order.skc_no.ilike(search_pattern),
                        Order.external_skc_no.ilike(search_pattern),
                        Order.external_order_no.ilike(search_pattern),
                        Order.external_order_no2.ilike(search_pattern),
                        Order.description.ilike(search_pattern)
                    )
                )
                .order_by(Order.created_at.desc())
            )
            return list(result.scalars().all())
    
    async def get_by_current_craft(self, craft_code: str) -> List[Order]:
        async with self.session_factory() as session:
            result = await session.execute(
                select(Order)
                .options(
                    selectinload(Order.owner),
                    selectinload(Order.craft),
                    selectinload(Order.craft_route),
                    selectinload(Order.order_lines)
                )
                .where(Order.current_craft == craft_code)
                .order_by(Order.created_at.desc())
            )
            return list(result.scalars().all())
    
    async def update(self, order: Order, session: TypingOptional[AsyncSession] = None) -> Order:
        if session is not None:
            session.add(order)
            await session.flush()
            await session.refresh(order)
            return order
        else:
            async with self.session_factory() as session:
                session.add(order)
                await session.commit()
                await session.refresh(order)
                return order
    
    async def delete(self, order_id: int) -> bool:
        async with self.session_factory() as session:
            order = await session.get(Order, order_id)
            if order:
                await session.delete(order)
                await session.commit()
                return True
            return False
    
    async def get_orders_by_craft_route(self, craft_route_id: int) -> List[Order]:
        async with self.session_factory() as session:
            result = await session.execute(
                select(Order)
                .options(
                    selectinload(Order.owner),
                    selectinload(Order.craft),
                    selectinload(Order.craft_route),
                    selectinload(Order.order_lines)
                )
                .where(Order.current_craft_route == craft_route_id)
                .order_by(Order.created_at.desc())
            )
            return list(result.scalars().all())
    
    async def get_order_statistics(self, user_id: Optional[int] = None) -> dict:
        async with self.session_factory() as session:
            # Base query
            base_query = select(func.count(Order.id)).select_from(Order)
            if user_id:
                base_query = base_query.where(Order.owner_user_id == user_id)
            
            # Total orders
            total_result = await session.execute(base_query)
            total_orders = total_result.scalar() or 0
            
            # Orders by status
            status_stats = {}
            for status in OrderStatus:
                status_query = base_query.where(Order.status == status)
                status_result = await session.execute(status_query)
                status_stats[status.value] = status_result.scalar() or 0
            
            # Overdue orders
            current_date = datetime.utcnow()
            overdue_query = base_query.where(
                and_(
                    Order.expect_finished_at.is_not(None),
                    Order.expect_finished_at < current_date,
                    Order.status.in_([
                        OrderStatus.PENDING,
                        OrderStatus.IN_PROGRESS,
                        OrderStatus.ON_HOLD,
                        OrderStatus.DELAYED
                    ])
                )
            )
            if user_id:
                overdue_query = overdue_query.where(Order.owner_user_id == user_id)
            
            overdue_result = await session.execute(overdue_query)
            overdue_orders = overdue_result.scalar() or 0
            
            return {
                "total_orders": total_orders,
                "status_breakdown": status_stats,
                "overdue_orders": overdue_orders,
                "active_orders": (
                    status_stats.get("pending", 0) +
                    status_stats.get("in_progress", 0) +
                    status_stats.get("on_hold", 0) +
                    status_stats.get("delayed", 0)
                )
            }