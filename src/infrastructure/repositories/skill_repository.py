from typing import List, Optional, Callable
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload
from src.application.interfaces.skill_repository_interface import SkillRepositoryInterface
from src.domain.entities.skill import Skill


class SkillRepository(SkillRepositoryInterface):
    """SQLAlchemy implementation of Skill repository."""

    def __init__(self, session_factory: Callable[[], AsyncSession]):
        self.session_factory = session_factory

    async def create(self, skill: Skill) -> Skill:
        """Create a new skill."""
        async with self.session_factory() as session:
            session.add(skill)
            await session.commit()
            await session.refresh(skill)
            return skill

    async def get_by_id(self, skill_id: int) -> Optional[Skill]:
        """Get skill by ID."""
        async with self.session_factory() as session:
            stmt = select(Skill).options(
                selectinload(Skill.user_factory_skills)
            ).where(Skill.id == skill_id)
            result = await session.execute(stmt)
            return result.scalar_one_or_none()

    async def get_by_code(self, code: str) -> Optional[Skill]:
        """Get skill by code."""
        async with self.session_factory() as session:
            stmt = select(Skill).options(
                selectinload(Skill.user_factory_skills)
            ).where(Skill.code == code)
            result = await session.execute(stmt)
            return result.scalar_one_or_none()

    async def get_all(self) -> List[Skill]:
        """Get all skills."""
        async with self.session_factory() as session:
            stmt = select(Skill).options(
                selectinload(Skill.user_factory_skills)
            )
            result = await session.execute(stmt)
            return list(result.scalars().all())

    async def get_active_skills(self) -> List[Skill]:
        """Get all active skills."""
        async with self.session_factory() as session:
            stmt = select(Skill).options(
                selectinload(Skill.user_factory_skills)
            ).where(Skill.is_active == True)
            result = await session.execute(stmt)
            return list(result.scalars().all())

    async def get_by_category(self, category: str) -> List[Skill]:
        """Get skills by category."""
        async with self.session_factory() as session:
            stmt = select(Skill).options(
                selectinload(Skill.user_factory_skills)
            ).where(Skill.category == category)
            result = await session.execute(stmt)
            return list(result.scalars().all())

    async def update(self, skill: Skill) -> Skill:
        """Update skill."""
        async with self.session_factory() as session:
            session.add(skill)
            await session.commit()
            await session.refresh(skill)
            return skill

    async def delete(self, skill_id: int) -> bool:
        """Delete skill by ID."""
        async with self.session_factory() as session:
            skill = await session.get(Skill, skill_id)
            if skill:
                await session.delete(skill)
                await session.commit()
                return True
            return False

    async def search_skills(self, search_term: str) -> List[Skill]:
        """Search skills by name or description."""
        async with self.session_factory() as session:
            search_pattern = f"%{search_term}%"
            stmt = select(Skill).options(
                selectinload(Skill.user_factory_skills)
            ).where(
                (Skill.name.ilike(search_pattern)) |
                (Skill.description.ilike(search_pattern)) |
                (Skill.code.ilike(search_pattern))
            )
            result = await session.execute(stmt)
            return list(result.scalars().all())