from typing import List, Optional, Callable
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from sqlalchemy.orm import selectinload
from src.domain.entities.factory import Factory
from src.application.interfaces.factory_repository_interface import FactoryRepositoryInterface


class FactoryRepository(FactoryRepositoryInterface):
    """SQLAlchemy implementation of factory repository."""
    
    def __init__(self, session_factory: Callable[[], AsyncSession]):
        self.session_factory = session_factory
    
    async def create(self, factory: Factory) -> Factory:
        async with self.session_factory() as session:
            session.add(factory)
            await session.commit()
            await session.refresh(factory)
            return factory
    
    async def get_by_id(self, factory_id: int) -> Optional[Factory]:
        async with self.session_factory() as session:
            result = await session.execute(select(Factory).where(Factory.id == factory_id))
            return result.scalar_one_or_none()
    
    async def get_by_code(self, code: str) -> Optional[Factory]:
        async with self.session_factory() as session:
            result = await session.execute(select(Factory).where(Factory.code == code))
            return result.scalar_one_or_none()
    
    async def get_by_name(self, name: str) -> Optional[Factory]:
        async with self.session_factory() as session:
            result = await session.execute(select(Factory).where(Factory.name == name))
            return result.scalar_one_or_none()
    
    async def update(self, factory: Factory) -> Factory:
        async with self.session_factory() as session:
            session.add(factory)
            await session.commit()
            await session.refresh(factory)
            return factory
    
    async def delete(self, factory_id: int) -> bool:
        async with self.session_factory() as session:
            factory = await session.get(Factory, factory_id)
            if factory:
                await session.delete(factory)
                await session.commit()
                return True
            return False
    
    async def get_all(self, skip: int = 0, limit: int = 100, is_active: Optional[bool] = None) -> List[Factory]:
        async with self.session_factory() as session:
            query = select(Factory).offset(skip).limit(limit)
            if is_active is not None:
                query = query.where(Factory.is_active == is_active)
            result = await session.execute(query)
            return list(result.scalars().all())
    
    async def get_with_departments(self, factory_id: int) -> Optional[Factory]:
        async with self.session_factory() as session:
            result = await session.execute(
                select(Factory)
                .where(Factory.id == factory_id)
                .options(selectinload(Factory.departments))
            )
            return result.scalar_one_or_none()