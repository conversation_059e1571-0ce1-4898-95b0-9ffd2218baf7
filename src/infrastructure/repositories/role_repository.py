from typing import List, Optional, Callable
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload
from src.application.interfaces.role_repository_interface import RoleRepositoryInterface
from src.domain.entities.role import Role
from src.domain.entities.permission import Permission
from src.domain.entities.role_permission import role_permissions


class RoleRepository(RoleRepositoryInterface):
    """SQLAlchemy implementation of Role repository."""

    def __init__(self, session_factory: Callable[[], AsyncSession]):
        self.session_factory = session_factory

    async def create(self, role: Role) -> Role:
        """Create a new role."""
        async with self.session_factory() as session:
            session.add(role)
            await session.commit()
            await session.refresh(role)
            return role

    async def get_by_id(self, role_id: int) -> Optional[Role]:
        """Get role by ID."""
        async with self.session_factory() as session:
            stmt = select(Role).options(
                selectinload(Role.permissions),
                selectinload(Role.users)
            ).where(Role.id == role_id)
            result = await session.execute(stmt)
            return result.scalar_one_or_none()

    async def get_by_name(self, name: str) -> Optional[Role]:
        """Get role by name."""
        async with self.session_factory() as session:
            stmt = select(Role).options(
                selectinload(Role.permissions),
                selectinload(Role.users)
            ).where(Role.name == name)
            result = await session.execute(stmt)
            return result.scalar_one_or_none()

    async def get_all(self) -> List[Role]:
        """Get all roles."""
        async with self.session_factory() as session:
            stmt = select(Role).options(
                selectinload(Role.permissions),
                selectinload(Role.users)
            )
            result = await session.execute(stmt)
            return list(result.scalars().all())

    async def get_active_roles(self) -> List[Role]:
        """Get all active roles."""
        async with self.session_factory() as session:
            stmt = select(Role).options(
                selectinload(Role.permissions),
                selectinload(Role.users)
            ).where(Role.is_active == True)
            result = await session.execute(stmt)
            return list(result.scalars().all())

    async def update(self, role: Role) -> Role:
        """Update role."""
        async with self.session_factory() as session:
            session.add(role)
            await session.commit()
            await session.refresh(role)
            return role

    async def delete(self, role_id: int) -> bool:
        """Delete role by ID."""
        async with self.session_factory() as session:
            role = await session.get(Role, role_id)
            if role:
                await session.delete(role)
                await session.commit()
                return True
            return False

    async def add_permission_to_role(self, role_id: int, permission_id: int) -> bool:
        """Add permission to role."""
        async with self.session_factory() as session:
            # Check if relationship already exists
            stmt = select(role_permissions).where(
                role_permissions.c.role_id == role_id,
                role_permissions.c.permission_id == permission_id
            )
            result = await session.execute(stmt)
            if result.first():
                return False  # Relationship already exists
            
            # Insert new relationship
            stmt = role_permissions.insert().values(
                role_id=role_id,
                permission_id=permission_id
            )
            await session.execute(stmt)
            await session.commit()
            return True

    async def remove_permission_from_role(self, role_id: int, permission_id: int) -> bool:
        """Remove permission from role."""
        async with self.session_factory() as session:
            stmt = role_permissions.delete().where(
                role_permissions.c.role_id == role_id,
                role_permissions.c.permission_id == permission_id
            )
            result = await session.execute(stmt)
            await session.commit()
            return result.rowcount > 0

    async def get_role_permissions(self, role_id: int) -> List[Permission]:
        """Get all permissions for a role."""
        async with self.session_factory() as session:
            stmt = select(Permission).join(role_permissions).where(
                role_permissions.c.role_id == role_id
            )
            result = await session.execute(stmt)
            return list(result.scalars().all())

    async def get_roles_with_permission(self, permission_id: int) -> List[Role]:
        """Get all roles that have a specific permission."""
        async with self.session_factory() as session:
            stmt = select(Role).join(role_permissions).where(
                role_permissions.c.permission_id == permission_id
            )
            result = await session.execute(stmt)
            return list(result.scalars().all())