from typing import List, Optional, Callable
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete, and_, func
from sqlalchemy.orm import selectinload
from src.domain.entities.order_line import OrderLine
from src.domain.entities.order import Order
from src.application.interfaces.order_line_repository_interface import OrderLineRepositoryInterface
from typing import Optional as TypingOptional  # Add this line


class OrderLineRepository(OrderLineRepositoryInterface):
    """SQLAlchemy implementation of order line repository."""
    
    def __init__(self, session_factory: Callable[[], AsyncSession]):
        self.session_factory = session_factory
    
    async def create(self, order_line: OrderLine) -> OrderLine:
        async with self.session_factory() as session:
            session.add(order_line)
            await session.commit()
            await session.refresh(order_line)
            return order_line
    
    async def get_by_id(self, order_line_id: int) -> Optional[OrderLine]:
        async with self.session_factory() as session:
            result = await session.execute(
                select(OrderLine)
                .options(selectinload(OrderLine.order))
                .where(OrderLine.id == order_line_id)
            )
            return result.scalar_one_or_none()
    
    async def get_by_order_line_no(self, order_line_no: str, session: TypingOptional[AsyncSession] = None) -> TypingOptional[OrderLine]:
        if session is not None:
            result = await session.execute(
                select(OrderLine)
                .options(selectinload(OrderLine.order))
                .where(OrderLine.order_line_no == order_line_no)
            )
            return result.scalar_one_or_none()
        else:
            async with self.session_factory() as session:
                result = await session.execute(
                    select(OrderLine)
                    .options(selectinload(OrderLine.order))
                    .where(OrderLine.order_line_no == order_line_no)
                )
                return result.scalar_one_or_none()
    
    async def get_by_order_line_no_and_factory(self, order_line_no: str, factory_id: int, session: TypingOptional[AsyncSession] = None) -> TypingOptional[OrderLine]:
        if session is not None:
            result = await session.execute(
                select(OrderLine)
                .options(selectinload(OrderLine.order))
                .where(and_(OrderLine.order_line_no == order_line_no, OrderLine.factory_id == factory_id))
            )
            return result.scalar_one_or_none()
        else:
            async with self.session_factory() as session:
                result = await session.execute(
                    select(OrderLine)
                    .options(selectinload(OrderLine.order))
                    .where(and_(OrderLine.order_line_no == order_line_no, OrderLine.factory_id == factory_id))
                )
                return result.scalar_one_or_none()
    
    async def get_by_order_no(self, order_no: str, session: TypingOptional[AsyncSession] = None) -> List[OrderLine]:
        if session is not None:
            result = await session.execute(
                select(OrderLine)
                .options(selectinload(OrderLine.order))
                .where(OrderLine.order_no == order_no)
                .order_by(OrderLine.size, OrderLine.order_line_no)
            )
            return list(result.scalars().all())
        else:
            async with self.session_factory() as session:
                result = await session.execute(
                    select(OrderLine)
                    .options(selectinload(OrderLine.order))
                    .where(OrderLine.order_no == order_no)
                    .order_by(OrderLine.size, OrderLine.order_line_no)
                )
                return list(result.scalars().all())
    
    async def get_by_order_no_and_factory(self, order_no: str, factory_id: int, session: TypingOptional[AsyncSession] = None) -> List[OrderLine]:
        if session is not None:
            result = await session.execute(
                select(OrderLine)
                .options(selectinload(OrderLine.order))
                .where(and_(OrderLine.order_no == order_no, OrderLine.factory_id == factory_id))
                .order_by(OrderLine.size, OrderLine.order_line_no)
            )
            return list(result.scalars().all())
        else:
            async with self.session_factory() as session:
                result = await session.execute(
                    select(OrderLine)
                    .options(selectinload(OrderLine.order))
                    .where(and_(OrderLine.order_no == order_no, OrderLine.factory_id == factory_id))
                    .order_by(OrderLine.size, OrderLine.order_line_no)
                )
                return list(result.scalars().all())
    
    async def get_by_size(self, size: str) -> List[OrderLine]:
        async with self.session_factory() as session:
            result = await session.execute(
                select(OrderLine)
                .options(selectinload(OrderLine.order))
                .where(OrderLine.size == size)
                .order_by(OrderLine.created_at.desc())
            )
            return list(result.scalars().all())
    
    async def get_all(self, skip: int = 0, limit: int = 100) -> List[OrderLine]:
        async with self.session_factory() as session:
            result = await session.execute(
                select(OrderLine)
                .options(selectinload(OrderLine.order))
                .order_by(OrderLine.created_at.desc())
                .offset(skip)
                .limit(limit)
            )
            return list(result.scalars().all())
    
    async def get_incomplete_lines(self) -> List[OrderLine]:
        async with self.session_factory() as session:
            result = await session.execute(
                select(OrderLine)
                .options(selectinload(OrderLine.order))
                .where(OrderLine.completed_amount < OrderLine.amount)
                .order_by(OrderLine.created_at.desc())
            )
            return list(result.scalars().all())
    
    async def get_in_progress_lines(self) -> List[OrderLine]:
        async with self.session_factory() as session:
            result = await session.execute(
                select(OrderLine)
                .options(selectinload(OrderLine.order))
                .where(
                    and_(
                        OrderLine.produced_amount > 0,
                        OrderLine.completed_amount < OrderLine.amount
                    )
                )
                .order_by(OrderLine.created_at.desc())
            )
            return list(result.scalars().all())
    
    async def get_lines_by_order_and_size(self, order_no: str, size: str) -> Optional[OrderLine]:
        async with self.session_factory() as session:
            result = await session.execute(
                select(OrderLine)
                .options(selectinload(OrderLine.order))
                .where(
                    and_(
                        OrderLine.order_no == order_no,
                        OrderLine.size == size
                    )
                )
            )
            return result.scalar_one_or_none()
    
    async def update(self, order_line: OrderLine) -> OrderLine:
        async with self.session_factory() as session:
            session.add(order_line)
            await session.commit()
            await session.refresh(order_line)
            return order_line
    
    async def delete(self, order_line_id: int) -> bool:
        async with self.session_factory() as session:
            order_line = await session.get(OrderLine, order_line_id)
            if order_line:
                await session.delete(order_line)
                await session.commit()
                return True
            return False
    
    async def delete_by_order_no(self, order_no: str) -> int:
        async with self.session_factory() as session:
            result = await session.execute(
                delete(OrderLine).where(OrderLine.order_no == order_no)
            )
            await session.commit()
            return result.rowcount
    
    async def bulk_create(self, order_lines: List[OrderLine], session: TypingOptional[AsyncSession] = None) -> List[OrderLine]:
        if session is not None:
            session.add_all(order_lines)
            await session.flush()
            for line in order_lines:
                await session.refresh(line)
            return order_lines
        else:
            async with self.session_factory() as session:
                session.add_all(order_lines)
                await session.commit()
                for line in order_lines:
                    await session.refresh(line)
                return order_lines
    
    async def bulk_update_production(self, updates: List[tuple]) -> bool:
        async with self.session_factory() as session:
            try:
                # updates is list of (order_line_id, produced_qty) tuples
                for order_line_id, produced_qty in updates:
                    order_line = await session.get(OrderLine, order_line_id)
                    if order_line:
                        order_line.update_production(produced_qty)
                        session.add(order_line)
                
                await session.commit()
                return True
            except Exception:
                await session.rollback()
                return False
    
    async def bulk_update_completion(self, updates: List[tuple]) -> bool:
        async with self.session_factory() as session:
            try:
                # updates is list of (order_line_id, completed_qty) tuples
                for order_line_id, completed_qty in updates:
                    order_line = await session.get(OrderLine, order_line_id)
                    if order_line:
                        order_line.update_completion(completed_qty)
                        session.add(order_line)
                
                await session.commit()
                return True
            except Exception:
                await session.rollback()
                return False
    
    async def get_order_line_statistics(self, order_no: Optional[str] = None) -> dict:
        async with self.session_factory() as session:
            # Base query
            base_query = select(OrderLine)
            if order_no:
                base_query = base_query.where(OrderLine.order_no == order_no)
            
            # Total lines count
            total_result = await session.execute(
                select(func.count(OrderLine.id)).select_from(base_query.subquery())
            )
            total_lines = total_result.scalar() or 0
            
            # Total amounts
            amount_result = await session.execute(
                select(
                    func.sum(OrderLine.amount),
                    func.sum(OrderLine.produced_amount),
                    func.sum(OrderLine.completed_amount)
                ).select_from(base_query.subquery())
            )
            amount_stats = amount_result.one()
            total_amount = amount_stats[0] or 0
            total_produced = amount_stats[1] or 0
            total_completed = amount_stats[2] or 0
            
            # Completion statistics
            completed_lines_result = await session.execute(
                select(func.count(OrderLine.id))
                .select_from(base_query.subquery())
                .where(OrderLine.completed_amount >= OrderLine.amount)
            )
            completed_lines = completed_lines_result.scalar() or 0
            
            # In progress lines
            in_progress_result = await session.execute(
                select(func.count(OrderLine.id))
                .select_from(base_query.subquery())
                .where(
                    and_(
                        OrderLine.produced_amount > 0,
                        OrderLine.completed_amount < OrderLine.amount
                    )
                )
            )
            in_progress_lines = in_progress_result.scalar() or 0
            
            # Pending lines (not started)
            pending_lines = total_lines - completed_lines - in_progress_lines
            
            return {
                "total_lines": total_lines,
                "completed_lines": completed_lines,
                "in_progress_lines": in_progress_lines,
                "pending_lines": pending_lines,
                "total_amount": total_amount,
                "total_produced": total_produced,
                "total_completed": total_completed,
                "completion_percentage": (total_completed / total_amount * 100) if total_amount > 0 else 0,
                "production_percentage": (total_produced / total_amount * 100) if total_amount > 0 else 0
            }