from typing import List, Optional, Callable
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete, and_
from sqlalchemy.orm import selectinload
from src.domain.entities.craft_route import CraftRoute
from src.domain.entities.craft import Craft
from src.domain.entities.skill import Skill
from src.application.interfaces.craft_route_repository_interface import CraftRouteRepositoryInterface


class CraftRouteRepository(CraftRouteRepositoryInterface):
    """SQLAlchemy implementation of craft route repository."""
    
    def __init__(self, session_factory: Callable[[], AsyncSession]):
        self.session_factory = session_factory
    
    async def create(self, craft_route: CraftRoute) -> CraftRoute:
        async with self.session_factory() as session:
            session.add(craft_route)
            await session.commit()
            await session.refresh(craft_route)
            return craft_route
    
    async def get_by_id(self, craft_route_id: int) -> Optional[CraftRoute]:
        async with self.session_factory() as session:
            result = await session.execute(
                select(CraftRoute)
                .options(
                    selectinload(CraftRoute.craft),
                    selectinload(CraftRoute.skill)
                )
                .where(CraftRoute.id == craft_route_id)
            )
            return result.scalar_one_or_none()
    
    async def get_by_craft_and_skill(self, craft_code: str, skill_code: str) -> Optional[CraftRoute]:
        async with self.session_factory() as session:
            result = await session.execute(
                select(CraftRoute)
                .options(
                    selectinload(CraftRoute.craft),
                    selectinload(CraftRoute.skill)
                )
                .where(
                    and_(
                        CraftRoute.craft_code == craft_code,
                        CraftRoute.skill_code == skill_code
                    )
                )
            )
            return result.scalar_one_or_none()
    
    async def get_by_craft_code(self, craft_code: str) -> List[CraftRoute]:
        async with self.session_factory() as session:
            result = await session.execute(
                select(CraftRoute)
                .options(
                    selectinload(CraftRoute.craft),
                    selectinload(CraftRoute.skill)
                )
                .where(CraftRoute.craft_code == craft_code)
                .order_by(CraftRoute.order, CraftRoute.id)
            )
            return list(result.scalars().all())
    
    async def get_by_skill_code(self, skill_code: str) -> List[CraftRoute]:
        async with self.session_factory() as session:
            result = await session.execute(
                select(CraftRoute)
                .options(
                    selectinload(CraftRoute.craft),
                    selectinload(CraftRoute.skill)
                )
                .where(CraftRoute.skill_code == skill_code)
                .order_by(CraftRoute.craft_code, CraftRoute.order)
            )
            return list(result.scalars().all())
    
    async def get_all(self, skip: int = 0, limit: int = 100) -> List[CraftRoute]:
        async with self.session_factory() as session:
            result = await session.execute(
                select(CraftRoute)
                .options(
                    selectinload(CraftRoute.craft),
                    selectinload(CraftRoute.skill)
                )
                .order_by(CraftRoute.craft_code, CraftRoute.order)
                .offset(skip)
                .limit(limit)
            )
            return list(result.scalars().all())
    
    async def update(self, craft_route: CraftRoute) -> CraftRoute:
        async with self.session_factory() as session:
            session.add(craft_route)
            await session.commit()
            await session.refresh(craft_route)
            return craft_route
    
    async def delete(self, craft_route_id: int) -> bool:
        async with self.session_factory() as session:
            craft_route = await session.get(CraftRoute, craft_route_id)
            if craft_route:
                await session.delete(craft_route)
                await session.commit()
                return True
            return False
    
    async def delete_by_craft_code(self, craft_code: str) -> int:
        async with self.session_factory() as session:
            result = await session.execute(
                delete(CraftRoute).where(CraftRoute.craft_code == craft_code)
            )
            await session.commit()
            return result.rowcount
    
    async def delete_by_skill_code(self, skill_code: str) -> int:
        async with self.session_factory() as session:
            result = await session.execute(
                delete(CraftRoute).where(CraftRoute.skill_code == skill_code)
            )
            await session.commit()
            return result.rowcount
    
    async def reorder_craft_routes(self, craft_code: str, route_orders: List[tuple]) -> bool:
        async with self.session_factory() as session:
            try:
                # route_orders is list of (route_id, new_order) tuples
                for route_id, new_order in route_orders:
                    craft_route = await session.get(CraftRoute, route_id)
                    if craft_route and craft_route.craft_code == craft_code:
                        craft_route.order = new_order
                        session.add(craft_route)
                
                await session.commit()
                return True
            except Exception:
                await session.rollback()
                return False