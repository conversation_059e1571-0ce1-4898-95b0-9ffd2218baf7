from typing import List, Optional, Callable
from datetime import date
from sqlalchemy import and_, select, func
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload
from src.domain.entities.daily_worker_bill import DailyWorkerBill, BillStatus
from src.application.interfaces.daily_worker_bill_repository_interface import DailyWorkerBillRepositoryInterface


class DailyWorkerBillRepository(DailyWorkerBillRepositoryInterface):
    """Daily worker bill repository implementation."""
    
    def __init__(self, session_factory: Callable[[], AsyncSession]):
        self.session_factory = session_factory
    
    async def create(self, bill: DailyWorkerBill, session: AsyncSession) -> DailyWorkerBill:
        """Create a new daily worker bill."""
        session.add(bill)
        await session.flush()
        await session.refresh(bill)
        return bill
    
    async def get_by_id(self, bill_id: int, session: AsyncSession) -> Optional[DailyWorkerBill]:
        """Get daily worker bill by ID."""
        result = await session.execute(
            select(DailyWorkerBill)
            .where(DailyWorkerBill.id == bill_id)
            .options(
                selectinload(DailyWorkerBill.worker),
                selectinload(DailyWorkerBill.reviewer),
                selectinload(DailyWorkerBill.payer)
            )
        )
        return result.scalar_one_or_none()
    
    async def get_by_bill_no(self, bill_no: str, session: AsyncSession) -> Optional[DailyWorkerBill]:
        """Get daily worker bill by bill number."""
        result = await session.execute(
            select(DailyWorkerBill)
            .where(DailyWorkerBill.bill_no == bill_no)
            .options(
                selectinload(DailyWorkerBill.worker),
                selectinload(DailyWorkerBill.reviewer),
                selectinload(DailyWorkerBill.payer)
            )
        )
        return result.scalar_one_or_none()
    
    async def get_by_worker_and_date(
        self, 
        worker_user_id: int, 
        factory_id: int, 
        bill_date: date,
        session: AsyncSession
    ) -> Optional[DailyWorkerBill]:
        """Get daily worker bill by worker and date."""
        result = await session.execute(
            select(DailyWorkerBill)
            .where(
                and_(
                    DailyWorkerBill.worker_user_id == worker_user_id,
                    DailyWorkerBill.factory_id == factory_id,
                    DailyWorkerBill.bill_date == bill_date
                )
            )
            .options(
                selectinload(DailyWorkerBill.worker),
                selectinload(DailyWorkerBill.reviewer),
                selectinload(DailyWorkerBill.payer)
            )
        )
        return result.scalar_one_or_none()
    
    async def get_by_factory_and_date(
        self, 
        factory_id: int, 
        bill_date: date,
        session: AsyncSession
    ) -> List[DailyWorkerBill]:
        """Get all daily worker bills for a factory on a specific date."""
        result = await session.execute(
            select(DailyWorkerBill)
            .where(
                and_(
                    DailyWorkerBill.factory_id == factory_id,
                    DailyWorkerBill.bill_date == bill_date
                )
            )
            .options(
                selectinload(DailyWorkerBill.worker),
                selectinload(DailyWorkerBill.reviewer),
                selectinload(DailyWorkerBill.payer)
            )
            .order_by(DailyWorkerBill.created_at.desc())
        )
        return list(result.scalars().all())
    
    async def get_by_worker_and_date_range(
        self,
        worker_user_id: int,
        factory_id: int,
        start_date: date,
        end_date: date,
        session: AsyncSession
    ) -> List[DailyWorkerBill]:
        """Get daily worker bills for a worker within a date range."""
        result = await session.execute(
            select(DailyWorkerBill)
            .where(
                and_(
                    DailyWorkerBill.worker_user_id == worker_user_id,
                    DailyWorkerBill.factory_id == factory_id,
                    DailyWorkerBill.bill_date >= start_date,
                    DailyWorkerBill.bill_date <= end_date
                )
            )
            .options(
                selectinload(DailyWorkerBill.worker),
                selectinload(DailyWorkerBill.reviewer),
                selectinload(DailyWorkerBill.payer)
            )
            .order_by(DailyWorkerBill.bill_date.desc())
        )
        return list(result.scalars().all())
    
    async def get_by_status(
        self,
        factory_id: int,
        status: BillStatus,
        session: AsyncSession
    ) -> List[DailyWorkerBill]:
        """Get daily worker bills by status."""
        result = await session.execute(
            select(DailyWorkerBill)
            .where(
                and_(
                    DailyWorkerBill.factory_id == factory_id,
                    DailyWorkerBill.status == status
                )
            )
            .options(
                selectinload(DailyWorkerBill.worker),
                selectinload(DailyWorkerBill.reviewer),
                selectinload(DailyWorkerBill.payer)
            )
            .order_by(DailyWorkerBill.created_at.desc())
        )
        return list(result.scalars().all())
    
    async def get_pending_bills(self, factory_id: int, session: AsyncSession) -> List[DailyWorkerBill]:
        """Get all pending bills for a factory."""
        return await self.get_by_status(factory_id, BillStatus.PENDING, session)
    
    async def update(self, bill: DailyWorkerBill, session: AsyncSession) -> DailyWorkerBill:
        """Update daily worker bill."""
        session.add(bill)
        await session.flush()
        await session.refresh(bill)
        return bill
    
    async def delete(self, bill_id: int, session: AsyncSession) -> bool:
        """Delete daily worker bill."""
        bill = await self.get_by_id(bill_id, session)
        if bill:
            await session.delete(bill)
            await session.flush()
            return True
        return False
    
    async def get_all(
        self, 
        factory_id: int, 
        session: AsyncSession,
        skip: int = 0, 
        limit: int = 100
    ) -> List[DailyWorkerBill]:
        """Get all daily worker bills with pagination."""
        result = await session.execute(
            select(DailyWorkerBill)
            .where(DailyWorkerBill.factory_id == factory_id)
            .options(
                selectinload(DailyWorkerBill.worker),
                selectinload(DailyWorkerBill.reviewer),
                selectinload(DailyWorkerBill.payer)
            )
            .order_by(DailyWorkerBill.created_at.desc())
            .offset(skip)
            .limit(limit)
        )
        return list(result.scalars().all())