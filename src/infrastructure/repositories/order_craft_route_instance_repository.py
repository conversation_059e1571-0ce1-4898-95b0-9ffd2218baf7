from typing import List, Optional, Dict, Any, Callable
from datetime import datetime
from sqlalchemy import select, func, and_, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from src.application.interfaces.order_craft_route_instance_repository_interface import OrderCraftRouteInstanceRepositoryInterface
from src.domain.entities.order_craft_route_instance import OrderCraftRouteInstance, SettlementStatus, CompletionGranularity
from src.domain.entities.order_craft_route import OrderCraftRoute


class OrderCraftRouteInstanceRepository(OrderCraftRouteInstanceRepositoryInterface):
    """Order craft route instance repository implementation."""
    
    def __init__(self, session_factory: Callable[[], AsyncSession]):
        self.session_factory = session_factory

    async def create(self, instance: OrderCraftRouteInstance, session: AsyncSession = None) -> OrderCraftRouteInstance:
        """Create a new order craft route instance."""
        async def _do_create(sess: AsyncSession) -> OrderCraftRouteInstance:
            sess.add(instance)
            await sess.flush()
            await sess.refresh(instance)
            return instance

        if session:
            return await _do_create(session)
        else:
            async with self.session_factory() as session:
                result = await _do_create(session)
                await session.commit()
                return result

    async def get_by_id(self, instance_id: int, session: AsyncSession = None) -> Optional[OrderCraftRouteInstance]:
        """Get order craft route instance by ID."""
        async def _do_get(sess: AsyncSession) -> Optional[OrderCraftRouteInstance]:
            stmt = (
                select(OrderCraftRouteInstance)
                .options(
                    selectinload(OrderCraftRouteInstance.order_craft_route).selectinload(OrderCraftRoute.skill),
                    selectinload(OrderCraftRouteInstance.worker)
                )
                .where(OrderCraftRouteInstance.id == instance_id)
            )
            result = await sess.execute(stmt)
            return result.scalar_one_or_none()

        if session:
            return await _do_get(session)
        else:
            async with self.session_factory() as session:
                return await _do_get(session)

    async def get_by_craft_route(
        self, 
        order_craft_route_id: int,
        session: AsyncSession = None
    ) -> List[OrderCraftRouteInstance]:
        """Get all instances for a specific craft route."""
        async def _do_get(sess: AsyncSession) -> List[OrderCraftRouteInstance]:
            stmt = (
                select(OrderCraftRouteInstance)
                .options(
                    selectinload(OrderCraftRouteInstance.order_craft_route),
                    selectinload(OrderCraftRouteInstance.worker)
                )
                .where(OrderCraftRouteInstance.order_craft_route_id == order_craft_route_id)
                .order_by(OrderCraftRouteInstance.completed_at.desc())
            )
            result = await sess.execute(stmt)
            return list(result.scalars().all())

        if session:
            return await _do_get(session)
        else:
            async with self.session_factory() as session:
                return await _do_get(session)

    async def get_by_worker(
        self,
        worker_user_id: int,
        factory_id: int,
        session: AsyncSession = None
    ) -> List[OrderCraftRouteInstance]:
        """Get all instances for a specific worker."""
        async def _do_get(sess: AsyncSession) -> List[OrderCraftRouteInstance]:
            stmt = (
                select(OrderCraftRouteInstance)
                .options(
                    selectinload(OrderCraftRouteInstance.order_craft_route),
                    selectinload(OrderCraftRouteInstance.worker)
                )
                .where(
                    and_(
                        OrderCraftRouteInstance.worker_user_id == worker_user_id,
                        OrderCraftRouteInstance.factory_id == factory_id
                    )
                )
                .order_by(OrderCraftRouteInstance.completed_at.desc())
            )
            result = await sess.execute(stmt)
            return list(result.scalars().all())

        if session:
            return await _do_get(session)
        else:
            async with self.session_factory() as session:
                return await _do_get(session)

    async def get_completed_in_date_range(
        self,
        factory_id: int,
        start_datetime: datetime,
        end_datetime: datetime,
        session: AsyncSession = None
    ) -> List[OrderCraftRouteInstance]:
        """Get completed instances within a date range."""
        async def _do_get(sess: AsyncSession) -> List[OrderCraftRouteInstance]:
            stmt = (
                select(OrderCraftRouteInstance)
                .options(
                    selectinload(OrderCraftRouteInstance.order_craft_route),
                    selectinload(OrderCraftRouteInstance.worker)
                )
                .where(
                    and_(
                        OrderCraftRouteInstance.factory_id == factory_id,
                        OrderCraftRouteInstance.completed_at >= start_datetime,
                        OrderCraftRouteInstance.completed_at <= end_datetime
                    )
                )
                .order_by(OrderCraftRouteInstance.completed_at.desc())
            )
            result = await sess.execute(stmt)
            return list(result.scalars().all())

        if session:
            return await _do_get(session)
        else:
            async with self.session_factory() as session:
                return await _do_get(session)

    async def get_worker_completed_in_date_range(
        self,
        factory_id: int,
        worker_user_id: int,
        start_datetime: datetime,
        end_datetime: datetime,
        session: AsyncSession = None
    ) -> List[OrderCraftRouteInstance]:
        """Get completed instances for a specific worker within a date range."""
        async def _do_get(sess: AsyncSession) -> List[OrderCraftRouteInstance]:
            stmt = (
                select(OrderCraftRouteInstance)
                .options(
                    selectinload(OrderCraftRouteInstance.order_craft_route),
                    selectinload(OrderCraftRouteInstance.worker)
                )
                .where(
                    and_(
                        OrderCraftRouteInstance.factory_id == factory_id,
                        OrderCraftRouteInstance.worker_user_id == worker_user_id,
                        OrderCraftRouteInstance.completed_at >= start_datetime,
                        OrderCraftRouteInstance.completed_at <= end_datetime
                    )
                )
                .order_by(OrderCraftRouteInstance.completed_at.desc())
            )
            result = await sess.execute(stmt)
            return list(result.scalars().all())

        if session:
            return await _do_get(session)
        else:
            async with self.session_factory() as session:
                return await _do_get(session)

    async def get_by_settlement_status(
        self,
        factory_id: int,
        settlement_status: SettlementStatus,
        session: AsyncSession = None
    ) -> List[OrderCraftRouteInstance]:
        """Get instances by settlement status."""
        async def _do_get(sess: AsyncSession) -> List[OrderCraftRouteInstance]:
            stmt = (
                select(OrderCraftRouteInstance)
                .options(
                    selectinload(OrderCraftRouteInstance.order_craft_route),
                    selectinload(OrderCraftRouteInstance.worker)
                )
                .where(
                    and_(
                        OrderCraftRouteInstance.factory_id == factory_id,
                        OrderCraftRouteInstance.settlement_status == settlement_status
                    )
                )
                .order_by(OrderCraftRouteInstance.completed_at.desc())
            )
            result = await sess.execute(stmt)
            return list(result.scalars().all())

        if session:
            return await _do_get(session)
        else:
            async with self.session_factory() as session:
                return await _do_get(session)

    async def get_by_granularity(
        self,
        factory_id: int,
        granularity: CompletionGranularity,
        session: AsyncSession = None
    ) -> List[OrderCraftRouteInstance]:
        """Get instances by completion granularity."""
        async def _do_get(sess: AsyncSession) -> List[OrderCraftRouteInstance]:
            stmt = (
                select(OrderCraftRouteInstance)
                .options(
                    selectinload(OrderCraftRouteInstance.order_craft_route),
                    selectinload(OrderCraftRouteInstance.worker)
                )
                .where(
                    and_(
                        OrderCraftRouteInstance.factory_id == factory_id,
                        OrderCraftRouteInstance.completion_granularity == granularity
                    )
                )
                .order_by(OrderCraftRouteInstance.completed_at.desc())
            )
            result = await sess.execute(stmt)
            return list(result.scalars().all())

        if session:
            return await _do_get(session)
        else:
            async with self.session_factory() as session:
                return await _do_get(session)

    async def get_by_order_no(
        self,
        factory_id: int,
        order_no: str,
        session: AsyncSession = None
    ) -> List[OrderCraftRouteInstance]:
        """Get instances by order number."""
        async def _do_get(sess: AsyncSession) -> List[OrderCraftRouteInstance]:
            stmt = (
                select(OrderCraftRouteInstance)
                .options(
                    selectinload(OrderCraftRouteInstance.order_craft_route).selectinload(OrderCraftRoute.skill),
                    selectinload(OrderCraftRouteInstance.worker)
                )
                .where(
                    and_(
                        OrderCraftRouteInstance.factory_id == factory_id,
                        OrderCraftRouteInstance.order_no == order_no
                    )
                )
                .order_by(OrderCraftRouteInstance.completed_at.desc())
            )
            result = await sess.execute(stmt)
            return list(result.scalars().all())

        if session:
            return await _do_get(session)
        else:
            async with self.session_factory() as session:
                return await _do_get(session)

    async def get_by_order_bundle_no(
        self,
        factory_id: int,
        order_bundle_no: str,
        session: AsyncSession = None
    ) -> List[OrderCraftRouteInstance]:
        """Get instances by order bundle number."""
        async def _do_get(sess: AsyncSession) -> List[OrderCraftRouteInstance]:
            stmt = (
                select(OrderCraftRouteInstance)
                .options(
                    selectinload(OrderCraftRouteInstance.order_craft_route),
                    selectinload(OrderCraftRouteInstance.worker)
                )
                .where(
                    and_(
                        OrderCraftRouteInstance.factory_id == factory_id,
                        OrderCraftRouteInstance.order_bundle_no == order_bundle_no
                    )
                )
                .order_by(OrderCraftRouteInstance.completed_at.desc())
            )
            result = await sess.execute(stmt)
            return list(result.scalars().all())

        if session:
            return await _do_get(session)
        else:
            async with self.session_factory() as session:
                return await _do_get(session)

    async def update(self, instance: OrderCraftRouteInstance, session: AsyncSession = None) -> OrderCraftRouteInstance:
        """Update order craft route instance."""
        async def _do_update(sess: AsyncSession) -> OrderCraftRouteInstance:
            await sess.merge(instance)
            await sess.flush()
            await sess.refresh(instance)
            return instance

        if session:
            return await _do_update(session)
        else:
            async with self.session_factory() as session:
                result = await _do_update(session)
                await session.commit()
                return result

    async def bulk_update(self, instances: List[OrderCraftRouteInstance], session: AsyncSession = None) -> List[OrderCraftRouteInstance]:
        """Bulk update order craft route instances."""
        async def _do_bulk_update(sess: AsyncSession) -> List[OrderCraftRouteInstance]:
            for instance in instances:
                await sess.merge(instance)
            await sess.flush()
            for instance in instances:
                await sess.refresh(instance)
            return instances

        if session:
            return await _do_bulk_update(session)
        else:
            async with self.session_factory() as session:
                result = await _do_bulk_update(session)
                await session.commit()
                return result

    async def delete(self, instance_id: int, session: AsyncSession = None) -> bool:
        """Delete order craft route instance."""
        async def _do_delete(sess: AsyncSession) -> bool:
            instance = await self.get_by_id(instance_id, sess)
            if instance:
                await sess.delete(instance)
                await sess.flush()
                return True
            return False

        if session:
            return await _do_delete(session)
        else:
            async with self.session_factory() as session:
                result = await _do_delete(session)
                await session.commit()
                return result

    async def get_all(
        self, 
        factory_id: int, 
        session: AsyncSession = None,
        skip: int = 0, 
        limit: int = 100
    ) -> List[OrderCraftRouteInstance]:
        """Get all order craft route instances with pagination."""
        async def _do_get_all(sess: AsyncSession) -> List[OrderCraftRouteInstance]:
            stmt = (
                select(OrderCraftRouteInstance)
                .options(
                    selectinload(OrderCraftRouteInstance.order_craft_route).selectinload(OrderCraftRoute.skill),
                    selectinload(OrderCraftRouteInstance.worker)
                )
                .where(OrderCraftRouteInstance.factory_id == factory_id)
                .order_by(OrderCraftRouteInstance.completed_at.desc())
                .offset(skip)
                .limit(limit)
            )
            result = await sess.execute(stmt)
            return list(result.scalars().all())

        if session:
            return await _do_get_all(session)
        else:
            async with self.session_factory() as session:
                return await _do_get_all(session)

    async def get_registered_summary_by_order(
        self,
        factory_id: int,
        order_no: str,
        session: AsyncSession = None
    ) -> Dict[str, Any]:
        """Get registered quantity summary by order number."""
        async def _do_get_summary(sess: AsyncSession) -> Dict[str, Any]:
            # Total registered quantity for the order
            total_stmt = (
                select(func.sum(OrderCraftRouteInstance.completed_quantity))
                .where(
                    and_(
                        OrderCraftRouteInstance.factory_id == factory_id,
                        OrderCraftRouteInstance.order_no == order_no,
                        OrderCraftRouteInstance.status.in_(["completed", "verified"])
                    )
                )
            )
            total_result = await sess.execute(total_stmt)
            total_registered = total_result.scalar() or 0

            # Registered by granularity
            granularity_stmt = (
                select(
                    OrderCraftRouteInstance.completion_granularity,
                    func.sum(OrderCraftRouteInstance.completed_quantity).label("quantity"),
                    func.count(OrderCraftRouteInstance.id).label("count")
                )
                .where(
                    and_(
                        OrderCraftRouteInstance.factory_id == factory_id,
                        OrderCraftRouteInstance.order_no == order_no,
                        OrderCraftRouteInstance.status.in_(["completed", "verified"])
                    )
                )
                .group_by(OrderCraftRouteInstance.completion_granularity)
            )
            granularity_result = await sess.execute(granularity_stmt)
            granularity_breakdown = {
                row.completion_granularity.value: {"quantity": row.quantity, "count": row.count}
                for row in granularity_result
            }

            return {
                "order_no": order_no,
                "total_registered_quantity": total_registered,
                "granularity_breakdown": granularity_breakdown
            }

        if session:
            return await _do_get_summary(session)
        else:
            async with self.session_factory() as session:
                return await _do_get_summary(session)

    async def get_registered_summary_by_craft_route(
        self,
        factory_id: int,
        order_craft_route_id: int,
        session: AsyncSession = None
    ) -> Dict[str, Any]:
        """Get registered quantity summary by craft route."""
        async def _do_get_summary(sess: AsyncSession) -> Dict[str, Any]:
            # Total registered quantity for the craft route
            total_stmt = (
                select(func.sum(OrderCraftRouteInstance.completed_quantity))
                .where(
                    and_(
                        OrderCraftRouteInstance.factory_id == factory_id,
                        OrderCraftRouteInstance.order_craft_route_id == order_craft_route_id,
                        OrderCraftRouteInstance.status.in_(["completed", "verified"])
                    )
                )
            )
            total_result = await sess.execute(total_stmt)
            total_registered = total_result.scalar() or 0

            # Registered by granularity
            granularity_stmt = (
                select(
                    OrderCraftRouteInstance.completion_granularity,
                    func.sum(OrderCraftRouteInstance.completed_quantity).label("quantity"),
                    func.count(OrderCraftRouteInstance.id).label("count")
                )
                .where(
                    and_(
                        OrderCraftRouteInstance.factory_id == factory_id,
                        OrderCraftRouteInstance.order_craft_route_id == order_craft_route_id,
                        OrderCraftRouteInstance.status.in_(["completed", "verified"])
                    )
                )
                .group_by(OrderCraftRouteInstance.completion_granularity)
            )
            granularity_result = await sess.execute(granularity_stmt)
            granularity_breakdown = {
                row.completion_granularity.value: {"quantity": row.quantity, "count": row.count}
                for row in granularity_result
            }

            return {
                "order_craft_route_id": order_craft_route_id,
                "total_registered_quantity": total_registered,
                "granularity_breakdown": granularity_breakdown
            }

        if session:
            return await _do_get_summary(session)
        else:
            async with self.session_factory() as session:
                return await _do_get_summary(session)

    async def get_registered_parts_and_bundles(
        self,
        factory_id: int,
        order_no: str,
        session: AsyncSession = None,
        order_craft_route_id: Optional[int] = None
    ) -> Dict[str, Dict[str, int]]:
        """Get registered parts and bundles with quantities."""
        async def _do_get_registered(sess: AsyncSession) -> Dict[str, Dict[str, int]]:
            conditions = [
                OrderCraftRouteInstance.factory_id == factory_id,
                OrderCraftRouteInstance.order_no == order_no,
                OrderCraftRouteInstance.status.in_(["completed", "verified"])
            ]
            
            if order_craft_route_id:
                conditions.append(OrderCraftRouteInstance.order_craft_route_id == order_craft_route_id)

            # Get registered parts
            parts_stmt = (
                select(
                    OrderCraftRouteInstance.order_part_no,
                    func.sum(OrderCraftRouteInstance.completed_quantity).label("quantity")
                )
                .where(
                    and_(
                        *conditions,
                        OrderCraftRouteInstance.order_part_no.isnot(None),
                        OrderCraftRouteInstance.completion_granularity == CompletionGranularity.BED
                    )
                )
                .group_by(OrderCraftRouteInstance.order_part_no)
            )
            parts_result = await sess.execute(parts_stmt)
            registered_parts = {row.order_part_no: row.quantity for row in parts_result}

            # Get registered bundles
            bundles_stmt = (
                select(
                    OrderCraftRouteInstance.order_part_no,
                    OrderCraftRouteInstance.order_bundle_no,
                    func.sum(OrderCraftRouteInstance.completed_quantity).label("quantity")
                )
                .where(
                    and_(
                        *conditions,
                        OrderCraftRouteInstance.order_bundle_no.isnot(None),
                        OrderCraftRouteInstance.completion_granularity == CompletionGranularity.BUNDLE
                    )
                )
                .group_by(OrderCraftRouteInstance.order_part_no, OrderCraftRouteInstance.order_bundle_no)
            )
            bundles_result = await sess.execute(bundles_stmt)
            
            registered_bundles = {}
            for row in bundles_result:
                part_no = row.order_part_no or "unknown"
                if part_no not in registered_bundles:
                    registered_bundles[part_no] = {}
                registered_bundles[part_no][row.order_bundle_no] = row.quantity

            return {
                "parts": registered_parts,
                "bundles": registered_bundles
            }

        if session:
            return await _do_get_registered(session)
        else:
            async with self.session_factory() as session:
                return await _do_get_registered(session)

    # Additional search methods for the craft instances API
    async def search_instances(
        self,
        order_no: Optional[str] = None,
        worker_user_id: Optional[int] = None,
        completion_granularity: Optional[str] = None,
        status: Optional[str] = None,
        settlement_status: Optional[str] = None,
        quality_level: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        date_field: Optional[str] = "completed_at",
        limit: int = 50,
        offset: int = 0,
        session: AsyncSession = None
    ) -> List[OrderCraftRouteInstance]:
        """Search craft instances with filters."""
        async def _do_search(sess: AsyncSession) -> List[OrderCraftRouteInstance]:
            conditions = []
            
            if order_no:
                conditions.append(OrderCraftRouteInstance.order_no == order_no)
            if worker_user_id:
                conditions.append(OrderCraftRouteInstance.worker_user_id == worker_user_id)
            if completion_granularity:
                conditions.append(OrderCraftRouteInstance.completion_granularity == CompletionGranularity(completion_granularity))
            if status:
                conditions.append(OrderCraftRouteInstance.status == status)
            if settlement_status:
                conditions.append(OrderCraftRouteInstance.settlement_status == SettlementStatus(settlement_status))
            if quality_level:
                conditions.append(OrderCraftRouteInstance.quality_level == quality_level)
            
            # Date filtering
            if start_date and date_field:
                date_column = getattr(OrderCraftRouteInstance, date_field, OrderCraftRouteInstance.completed_at)
                conditions.append(date_column >= start_date)
            if end_date and date_field:
                date_column = getattr(OrderCraftRouteInstance, date_field, OrderCraftRouteInstance.completed_at)
                conditions.append(date_column <= end_date)

            stmt = (
                select(OrderCraftRouteInstance)
                .options(
                    selectinload(OrderCraftRouteInstance.order_craft_route).selectinload(OrderCraftRoute.skill),
                    selectinload(OrderCraftRouteInstance.worker)
                )
                .where(and_(*conditions))
                .order_by(OrderCraftRouteInstance.completed_at.desc())
                .offset(offset)
                .limit(limit)
            )
            result = await sess.execute(stmt)
            return list(result.scalars().all())

        if session:
            return await _do_search(session)
        else:
            async with self.session_factory() as session:
                return await _do_search(session)

    async def count_instances(
        self,
        order_no: Optional[str] = None,
        worker_user_id: Optional[int] = None,
        completion_granularity: Optional[str] = None,
        status: Optional[str] = None,
        settlement_status: Optional[str] = None,
        quality_level: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        date_field: Optional[str] = "completed_at",
        session: AsyncSession = None
    ) -> int:
        """Count craft instances with filters."""
        async def _do_count(sess: AsyncSession) -> int:
            conditions = []
            
            if order_no:
                conditions.append(OrderCraftRouteInstance.order_no == order_no)
            if worker_user_id:
                conditions.append(OrderCraftRouteInstance.worker_user_id == worker_user_id)
            if completion_granularity:
                conditions.append(OrderCraftRouteInstance.completion_granularity == CompletionGranularity(completion_granularity))
            if status:
                conditions.append(OrderCraftRouteInstance.status == status)
            if settlement_status:
                conditions.append(OrderCraftRouteInstance.settlement_status == SettlementStatus(settlement_status))
            if quality_level:
                conditions.append(OrderCraftRouteInstance.quality_level == quality_level)
            
            # Date filtering
            if start_date and date_field:
                date_column = getattr(OrderCraftRouteInstance, date_field, OrderCraftRouteInstance.completed_at)
                conditions.append(date_column >= start_date)
            if end_date and date_field:
                date_column = getattr(OrderCraftRouteInstance, date_field, OrderCraftRouteInstance.completed_at)
                conditions.append(date_column <= end_date)

            stmt = select(func.count(OrderCraftRouteInstance.id)).where(and_(*conditions))
            result = await sess.execute(stmt)
            return result.scalar() or 0

        if session:
            return await _do_count(session)
        else:
            async with self.session_factory() as session:
                return await _do_count(session)

    async def get_statistics(
        self,
        order_no: Optional[str] = None,
        worker_user_id: Optional[int] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        session: AsyncSession = None
    ) -> Dict[str, Any]:
        """Get craft instance statistics."""
        async def _do_get_stats(sess: AsyncSession) -> Dict[str, Any]:
            conditions = []
            
            if order_no:
                conditions.append(OrderCraftRouteInstance.order_no == order_no)
            if worker_user_id:
                conditions.append(OrderCraftRouteInstance.worker_user_id == worker_user_id)
            if start_date:
                conditions.append(OrderCraftRouteInstance.completed_at >= start_date)
            if end_date:
                conditions.append(OrderCraftRouteInstance.completed_at <= end_date)

            base_condition = and_(*conditions) if conditions else True

            # Basic counts
            total_stmt = select(func.count(OrderCraftRouteInstance.id)).where(base_condition)
            total_result = await sess.execute(total_stmt)
            total_instances = total_result.scalar() or 0

            quantity_stmt = select(func.sum(OrderCraftRouteInstance.completed_quantity)).where(base_condition)
            quantity_result = await sess.execute(quantity_stmt)
            total_quantity = quantity_result.scalar() or 0

            workers_stmt = select(func.count(func.distinct(OrderCraftRouteInstance.worker_user_id))).where(base_condition)
            workers_result = await sess.execute(workers_stmt)
            total_workers = workers_result.scalar() or 0

            # Status breakdown
            status_stmt = (
                select(
                    OrderCraftRouteInstance.status,
                    func.count(OrderCraftRouteInstance.id).label("count")
                )
                .where(base_condition)
                .group_by(OrderCraftRouteInstance.status)
            )
            status_result = await sess.execute(status_stmt)
            status_breakdown = {row.status: row.count for row in status_result}

            # Settlement breakdown
            settlement_stmt = (
                select(
                    OrderCraftRouteInstance.settlement_status,
                    func.count(OrderCraftRouteInstance.id).label("count")
                )
                .where(base_condition)
                .group_by(OrderCraftRouteInstance.settlement_status)
            )
            settlement_result = await sess.execute(settlement_stmt)
            settlement_breakdown = {row.settlement_status.value: row.count for row in settlement_result}

            # Granularity breakdown
            granularity_stmt = (
                select(
                    OrderCraftRouteInstance.completion_granularity,
                    func.count(OrderCraftRouteInstance.id).label("count")
                )
                .where(base_condition)
                .group_by(OrderCraftRouteInstance.completion_granularity)
            )
            granularity_result = await sess.execute(granularity_stmt)
            granularity_breakdown = {row.completion_granularity.value: row.count for row in granularity_result}

            # Quality breakdown
            quality_stmt = (
                select(
                    OrderCraftRouteInstance.quality_level,
                    func.count(OrderCraftRouteInstance.id).label("count")
                )
                .where(base_condition)
                .group_by(OrderCraftRouteInstance.quality_level)
            )
            quality_result = await sess.execute(quality_stmt)
            quality_breakdown = {(row.quality_level or "N/A"): row.count for row in quality_result}

            return {
                "total_instances": total_instances,
                "total_quantity": total_quantity,
                "total_workers": total_workers,
                "average_quality_score": None,  # Would need quality scoring logic
                "status_breakdown": status_breakdown,
                "settlement_breakdown": settlement_breakdown,
                "granularity_breakdown": granularity_breakdown,
                "quality_breakdown": quality_breakdown,
                "daily_completion_trend": []  # Would need date grouping logic
            }

        if session:
            return await _do_get_stats(session)
        else:
            async with self.session_factory() as session:
                return await _do_get_stats(session)