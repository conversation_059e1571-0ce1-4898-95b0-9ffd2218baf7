from typing import List, Optional, Callable
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, func
from sqlalchemy.orm import selectinload
from src.domain.entities.user_factory import UserFactory, UserFactoryStatus, UserFactoryRole
from src.domain.entities.user_factory_skill import UserFactorySkill
from src.application.interfaces.user_factory_repository_interface import UserFactoryRepositoryInterface


class UserFactoryRepository(UserFactoryRepositoryInterface):
    """SQLAlchemy implementation of user factory repository."""
    
    def __init__(self, session_factory: Callable[[], AsyncSession]):
        self.session_factory = session_factory
    
    async def create(self, user_factory: UserFactory) -> UserFactory:
        async with self.session_factory() as session:
            session.add(user_factory)
            await session.commit()
            await session.refresh(user_factory)
            return user_factory
    
    async def get_by_id(self, user_factory_id: int) -> Optional[UserFactory]:
        async with self.session_factory() as session:
            result = await session.execute(
                select(UserFactory)
                .where(UserFactory.id == user_factory_id)
                .options(
                    selectinload(UserFactory.user),
                    selectinload(UserFactory.factory),
                    selectinload(UserFactory.department),
                    selectinload(UserFactory.approver),
                    selectinload(UserFactory.skills).selectinload(UserFactorySkill.skill)
                )
            )
            return result.scalar_one_or_none()
    
    async def get_by_user_and_factory(self, user_id: int, factory_id: int) -> Optional[UserFactory]:
        async with self.session_factory() as session:
            result = await session.execute(
                select(UserFactory)
                .where(
                    and_(
                        UserFactory.user_id == user_id,
                        UserFactory.factory_id == factory_id
                    )
                )
                .options(
                    selectinload(UserFactory.user),
                    selectinload(UserFactory.factory),
                    selectinload(UserFactory.department),
                    selectinload(UserFactory.skills).selectinload(UserFactorySkill.skill)
                )
            )
            return result.scalar_one_or_none()
    
    async def get_by_user_id(self, user_id: int) -> List[UserFactory]:
        async with self.session_factory() as session:
            result = await session.execute(
                select(UserFactory)
                .where(UserFactory.user_id == user_id)
                .options(
                    selectinload(UserFactory.factory),
                    selectinload(UserFactory.department)
                )
                .order_by(UserFactory.created_at.desc())
            )
            return list(result.scalars().all())
    
    async def get_by_factory_id(self, factory_id: int) -> List[UserFactory]:
        async with self.session_factory() as session:
            result = await session.execute(
                select(UserFactory)
                .where(UserFactory.factory_id == factory_id)
                .options(
                    selectinload(UserFactory.user),
                    selectinload(UserFactory.department),
                    selectinload(UserFactory.skills).selectinload(UserFactorySkill.skill)
                )
                .order_by(UserFactory.created_at.desc())
            )
            return list(result.scalars().all())
    
    async def get_active_members_by_factory(self, factory_id: int) -> List[UserFactory]:
        async with self.session_factory() as session:
            result = await session.execute(
                select(UserFactory)
                .where(
                    and_(
                        UserFactory.factory_id == factory_id,
                        UserFactory.status == UserFactoryStatus.APPROVED
                    )
                )
                .options(
                    selectinload(UserFactory.user),
                    selectinload(UserFactory.department)
                )
                .order_by(UserFactory.start_date.desc())
            )
            return list(result.scalars().all())
    
    async def get_pending_requests_by_factory(self, factory_id: int) -> List[UserFactory]:
        async with self.session_factory() as session:
            result = await session.execute(
                select(UserFactory)
                .where(
                    and_(
                        UserFactory.factory_id == factory_id,
                        UserFactory.status == UserFactoryStatus.PENDING
                    )
                )
                .options(
                    selectinload(UserFactory.user),
                    selectinload(UserFactory.department)
                )
                .order_by(UserFactory.requested_at.asc())
            )
            return list(result.scalars().all())
    
    async def get_by_status(self, status: UserFactoryStatus, skip: int = 0, limit: int = 100) -> List[UserFactory]:
        async with self.session_factory() as session:
            result = await session.execute(
                select(UserFactory)
                .where(UserFactory.status == status)
                .options(
                    selectinload(UserFactory.user),
                    selectinload(UserFactory.factory),
                    selectinload(UserFactory.department)
                )
                .offset(skip)
                .limit(limit)
                .order_by(UserFactory.created_at.desc())
            )
            return list(result.scalars().all())
    
    async def get_managers_by_factory(self, factory_id: int) -> List[UserFactory]:
        async with self.session_factory() as session:
            result = await session.execute(
                select(UserFactory)
                .where(
                    and_(
                        UserFactory.factory_id == factory_id,
                        UserFactory.status == UserFactoryStatus.APPROVED,
                        UserFactory.role.in_([UserFactoryRole.MANAGER, UserFactoryRole.ADMIN])
                    )
                )
                .options(selectinload(UserFactory.user))
                .order_by(UserFactory.role.desc(), UserFactory.start_date.asc())
            )
            return list(result.scalars().all())
    
    async def get_by_user_and_role(self, user_id: int, role: UserFactoryRole) -> List[UserFactory]:
        async with self.session_factory() as session:
            result = await session.execute(
                select(UserFactory)
                .where(
                    and_(
                        UserFactory.user_id == user_id,
                        UserFactory.status == UserFactoryStatus.APPROVED,
                        UserFactory.role == role
                    )
                )
                .options(
                    selectinload(UserFactory.factory),
                    selectinload(UserFactory.department)
                )
                .order_by(UserFactory.start_date.desc())
            )
            return list(result.scalars().all())
    
    async def update(self, user_factory: UserFactory) -> UserFactory:
        async with self.session_factory() as session:
            session.add(user_factory)
            await session.commit()
            await session.refresh(user_factory)
            return user_factory
    
    async def delete(self, user_factory_id: int) -> bool:
        async with self.session_factory() as session:
            user_factory = await session.get(UserFactory, user_factory_id)
            if user_factory:
                await session.delete(user_factory)
                await session.commit()
                return True
            return False
    
    async def get_factory_statistics(self, factory_id: int) -> dict:
        async with self.session_factory() as session:
            # Get total members count
            total_result = await session.execute(
                select(func.count(UserFactory.id))
                .where(UserFactory.factory_id == factory_id)
            )
            total_members = total_result.scalar() or 0
            
            # Get active members count
            active_result = await session.execute(
                select(func.count(UserFactory.id))
                .where(
                    and_(
                        UserFactory.factory_id == factory_id,
                        UserFactory.status == UserFactoryStatus.APPROVED
                    )
                )
            )
            active_members = active_result.scalar() or 0
            
            # Get pending requests count
            pending_result = await session.execute(
                select(func.count(UserFactory.id))
                .where(
                    and_(
                        UserFactory.factory_id == factory_id,
                        UserFactory.status == UserFactoryStatus.PENDING
                    )
                )
            )
            pending_requests = pending_result.scalar() or 0
            
            return {
                "total_members": total_members,
                "active_members": active_members,
                "pending_requests": pending_requests
            }
    
    async def get_user_by_employee_id(self, factory_id: int, employee_id: str) -> Optional[UserFactory]:
        async with self.session_factory() as session:
            result = await session.execute(
                select(UserFactory)
                .where(
                    and_(
                        UserFactory.factory_id == factory_id,
                        UserFactory.employee_id == employee_id,
                        UserFactory.status == UserFactoryStatus.APPROVED
                    )
                )
                .options(selectinload(UserFactory.user))
            )
            return result.scalar_one_or_none()