from typing import List, Optional, Callable
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_
from sqlalchemy.orm import selectinload
from src.domain.entities.department import Department
from src.application.interfaces.department_repository_interface import DepartmentRepositoryInterface


class DepartmentRepository(DepartmentRepositoryInterface):
    """SQLAlchemy implementation of department repository."""
    
    def __init__(self, session_factory: Callable[[], AsyncSession]):
        self.session_factory = session_factory
    
    async def create(self, department: Department) -> Department:
        async with self.session_factory() as session:
            session.add(department)
            await session.commit()
            await session.refresh(department)
            return department
    
    async def get_by_id(self, department_id: int) -> Optional[Department]:
        async with self.session_factory() as session:
            result = await session.execute(select(Department).where(Department.id == department_id))
            return result.scalar_one_or_none()
    
    async def get_by_code_and_factory(self, code: str, factory_id: int) -> Optional[Department]:
        async with self.session_factory() as session:
            result = await session.execute(
                select(Department).where(
                    and_(Department.code == code, Department.factory_id == factory_id)
                )
            )
            return result.scalar_one_or_none()
    
    async def get_by_factory_id(self, factory_id: int, skip: int = 0, limit: int = 100) -> List[Department]:
        async with self.session_factory() as session:
            result = await session.execute(
                select(Department)
                .where(Department.factory_id == factory_id)
                .offset(skip)
                .limit(limit)
            )
            return list(result.scalars().all())
    
    async def update(self, department: Department) -> Department:
        async with self.session_factory() as session:
            session.add(department)
            await session.commit()
            await session.refresh(department)
            return department
    
    async def delete(self, department_id: int) -> bool:
        async with self.session_factory() as session:
            department = await session.get(Department, department_id)
            if department:
                await session.delete(department)
                await session.commit()
                return True
            return False
    
    async def get_all(self, skip: int = 0, limit: int = 100, is_active: Optional[bool] = None) -> List[Department]:
        async with self.session_factory() as session:
            query = select(Department).offset(skip).limit(limit)
            if is_active is not None:
                query = query.where(Department.is_active == is_active)
            result = await session.execute(query)
            return list(result.scalars().all())
    
    async def get_with_users(self, department_id: int) -> Optional[Department]:
        async with self.session_factory() as session:
            result = await session.execute(
                select(Department)
                .where(Department.id == department_id)
                .options(selectinload(Department.users))
            )
            return result.scalar_one_or_none()