from typing import List, Optional, Callable
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete, and_, or_, func
from sqlalchemy.orm import selectinload
from src.domain.entities.order_part import OrderPart, PartStatus
from src.application.interfaces.order_part_repository_interface import OrderPartRepositoryInterface
from typing import Optional as TypingOptional


class OrderPartRepository(OrderPartRepositoryInterface):
    """SQLAlchemy implementation of order part repository."""
    
    def __init__(self, session_factory: Callable[[], AsyncSession]):
        self.session_factory = session_factory
    
    async def create(self, order_part: OrderPart) -> OrderPart:
        async with self.session_factory() as session:
            session.add(order_part)
            await session.commit()
            await session.refresh(order_part)
            return order_part
    
    async def get_by_id(self, order_part_id: int) -> Optional[OrderPart]:
        async with self.session_factory() as session:
            result = await session.execute(
                select(OrderPart)
                .options(
                    selectinload(OrderPart.order),
                    selectinload(OrderPart.supervisor),
                    selectinload(OrderPart.order_bundles)
                )
                .where(OrderPart.id == order_part_id)
            )
            return result.scalar_one_or_none()
    
    async def get_by_order_part_no_and_factory(self, order_part_no: str, factory_id: int, order_no: str, session: TypingOptional[AsyncSession] = None) -> Optional[OrderPart]:
        if session is not None:
            result = await session.execute(
                select(OrderPart)
                .options(
                    selectinload(OrderPart.order),
                    selectinload(OrderPart.supervisor),
                    selectinload(OrderPart.order_bundles)
                )
                .where(and_(
                    OrderPart.order_part_no == order_part_no,
                    OrderPart.factory_id == factory_id,
                    OrderPart.order_no == order_no
                ))
            )
            return result.scalar_one_or_none()
        else:
            async with self.session_factory() as session:
                result = await session.execute(
                    select(OrderPart)
                    .options(
                        selectinload(OrderPart.order),
                        selectinload(OrderPart.supervisor),
                        selectinload(OrderPart.order_bundles)
                    )
                    .where(and_(
                        OrderPart.order_part_no == order_part_no,
                        OrderPart.factory_id == factory_id,
                        OrderPart.order_no == order_no
                    ))
                )
                return result.scalar_one_or_none()
    
    async def get_by_order_no_and_factory(self, order_no: str, factory_id: int, session: TypingOptional[AsyncSession] = None) -> List[OrderPart]:
        if session is not None:
            result = await session.execute(
                select(OrderPart)
                .options(
                    selectinload(OrderPart.order),
                    selectinload(OrderPart.supervisor),
                    selectinload(OrderPart.order_bundles)
                )
                .where(and_(OrderPart.order_no == order_no, OrderPart.factory_id == factory_id))
                .order_by(OrderPart.part_sequence, OrderPart.order_part_no)
            )
            return list(result.scalars().all())
        else:
            async with self.session_factory() as session:
                result = await session.execute(
                    select(OrderPart)
                    .options(
                        selectinload(OrderPart.order),
                        selectinload(OrderPart.supervisor),
                        selectinload(OrderPart.order_bundles)
                    )
                    .where(and_(OrderPart.order_no == order_no, OrderPart.factory_id == factory_id))
                    .order_by(OrderPart.part_sequence, OrderPart.order_part_no)
                )
                return list(result.scalars().all())
    
    async def get_by_status(self, status: PartStatus, factory_id: int) -> List[OrderPart]:
        async with self.session_factory() as session:
            result = await session.execute(
                select(OrderPart)
                .options(
                    selectinload(OrderPart.order),
                    selectinload(OrderPart.supervisor),
                    selectinload(OrderPart.order_bundles)
                )
                .where(and_(OrderPart.status == status, OrderPart.factory_id == factory_id))
                .order_by(OrderPart.created_at.desc())
            )
            return list(result.scalars().all())
    
    async def get_by_supervisor(self, supervisor_user_id: int, factory_id: int) -> List[OrderPart]:
        async with self.session_factory() as session:
            result = await session.execute(
                select(OrderPart)
                .options(
                    selectinload(OrderPart.order),
                    selectinload(OrderPart.supervisor),
                    selectinload(OrderPart.order_bundles)
                )
                .where(and_(OrderPart.supervisor_user_id == supervisor_user_id, OrderPart.factory_id == factory_id))
                .order_by(OrderPart.created_at.desc())
            )
            return list(result.scalars().all())
    
    async def get_all(self, factory_id: int, skip: int = 0, limit: int = 100) -> List[OrderPart]:
        async with self.session_factory() as session:
            result = await session.execute(
                select(OrderPart)
                .options(
                    selectinload(OrderPart.order),
                    selectinload(OrderPart.supervisor),
                    selectinload(OrderPart.order_bundles)
                )
                .where(OrderPart.factory_id == factory_id)
                .order_by(OrderPart.created_at.desc())
                .offset(skip)
                .limit(limit)
            )
            return list(result.scalars().all())
    
    async def update(self, order_part: OrderPart) -> OrderPart:
        async with self.session_factory() as session:
            session.add(order_part)
            await session.commit()
            await session.refresh(order_part)
            return order_part
    
    async def delete(self, order_part_id: int) -> bool:
        async with self.session_factory() as session:
            result = await session.execute(
                delete(OrderPart).where(OrderPart.id == order_part_id)
            )
            await session.commit()
            return result.rowcount > 0
    
    async def delete_by_order_no_and_factory(self, order_no: str, factory_id: int) -> int:
        async with self.session_factory() as session:
            result = await session.execute(
                delete(OrderPart).where(and_(OrderPart.order_no == order_no, OrderPart.factory_id == factory_id))
            )
            await session.commit()
            return result.rowcount
    
    async def bulk_create(self, order_parts: List[OrderPart], session: TypingOptional[AsyncSession] = None) -> List[OrderPart]:
        if session is not None:
            session.add_all(order_parts)
            await session.flush()
            for order_part in order_parts:
                await session.refresh(order_part)
            return order_parts
        else:
            async with self.session_factory() as session:
                session.add_all(order_parts)
                await session.commit()
                for order_part in order_parts:
                    await session.refresh(order_part)
                return order_parts
    
    async def get_order_part_statistics(self, factory_id: int, order_no: Optional[str] = None) -> dict:
        async with self.session_factory() as session:
            base_query = select(OrderPart).where(OrderPart.factory_id == factory_id)
            
            if order_no:
                base_query = base_query.where(OrderPart.order_no == order_no)
            
            # Total count
            total_result = await session.execute(
                select(func.count(OrderPart.id)).select_from(base_query.subquery())
            )
            total_order_parts = total_result.scalar()
            
            # Status breakdown
            status_result = await session.execute(
                select(OrderPart.status, func.count(OrderPart.id))
                .select_from(base_query.subquery())
                .group_by(OrderPart.status)
            )
            status_breakdown = dict(status_result.all())
            
            # Quantity statistics
            quantity_result = await session.execute(
                select(
                    func.sum(OrderPart.total_quantity),
                    func.sum(OrderPart.completed_quantity)
                ).select_from(base_query.subquery())
            )
            quantity_stats = quantity_result.one()
            total_quantity = quantity_stats[0] or 0
            completed_quantity = quantity_stats[1] or 0
            
            return {
                "total_order_parts": total_order_parts,
                "status_breakdown": status_breakdown,
                "total_quantity": total_quantity,
                "completed_quantity": completed_quantity,
                "completion_percentage": (completed_quantity / total_quantity * 100) if total_quantity > 0 else 0,
                "planned_parts": status_breakdown.get("planned", 0),
                "cutting_parts": status_breakdown.get("cutting", 0),
                "sewing_parts": status_breakdown.get("sewing", 0),
                "quality_check_parts": status_breakdown.get("quality_check", 0),
                "completed_parts": status_breakdown.get("completed", 0),
                "on_hold_parts": status_breakdown.get("on_hold", 0),
                "cancelled_parts": status_breakdown.get("cancelled", 0)
            }