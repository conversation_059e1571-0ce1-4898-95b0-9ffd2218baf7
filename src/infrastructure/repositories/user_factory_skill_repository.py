from typing import List, Optional, Callable
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload
from src.application.interfaces.user_factory_skill_repository_interface import UserFactorySkillRepositoryInterface
from src.domain.entities.user_factory_skill import UserFactorySkill


class UserFactorySkillRepository(UserFactorySkillRepositoryInterface):
    """SQLAlchemy implementation of UserFactorySkill repository."""

    def __init__(self, session_factory: Callable[[], AsyncSession]):
        self.session_factory = session_factory

    async def create(self, user_factory_skill: UserFactorySkill) -> UserFactorySkill:
        """Create a new user factory skill."""
        async with self.session_factory() as session:
            session.add(user_factory_skill)
            await session.commit()
            await session.refresh(user_factory_skill)
            return user_factory_skill

    async def get_by_id(self, skill_id: int) -> Optional[UserFactorySkill]:
        """Get user factory skill by ID."""
        async with self.session_factory() as session:
            stmt = select(UserFactorySkill).options(
                selectinload(UserFactorySkill.skill),
                selectinload(UserFactorySkill.user_factory),
                selectinload(UserFactorySkill.assigned_by_user)
            ).where(UserFactorySkill.id == skill_id)
            result = await session.execute(stmt)
            return result.scalar_one_or_none()

    async def get_by_user_factory_and_skill(self, user_factory_id: int, skill_id: int) -> Optional[UserFactorySkill]:
        """Get user factory skill by user factory and skill."""
        async with self.session_factory() as session:
            stmt = select(UserFactorySkill).options(
                selectinload(UserFactorySkill.skill),
                selectinload(UserFactorySkill.user_factory),
                selectinload(UserFactorySkill.assigned_by_user)
            ).where(
                UserFactorySkill.user_factory_id == user_factory_id,
                UserFactorySkill.skill_id == skill_id
            )
            result = await session.execute(stmt)
            return result.scalar_one_or_none()

    async def get_by_user_factory(self, user_factory_id: int) -> List[UserFactorySkill]:
        """Get all skills for a user factory relationship."""
        async with self.session_factory() as session:
            stmt = select(UserFactorySkill).options(
                selectinload(UserFactorySkill.skill),
                selectinload(UserFactorySkill.user_factory),
                selectinload(UserFactorySkill.assigned_by_user)
            ).where(UserFactorySkill.user_factory_id == user_factory_id)
            result = await session.execute(stmt)
            return list(result.scalars().all())

    async def get_by_skill(self, skill_id: int) -> List[UserFactorySkill]:
        """Get all user factory relationships for a skill."""
        async with self.session_factory() as session:
            stmt = select(UserFactorySkill).options(
                selectinload(UserFactorySkill.skill),
                selectinload(UserFactorySkill.user_factory),
                selectinload(UserFactorySkill.assigned_by_user)
            ).where(UserFactorySkill.skill_id == skill_id)
            result = await session.execute(stmt)
            return list(result.scalars().all())

    async def get_certified_skills(self, user_factory_id: int) -> List[UserFactorySkill]:
        """Get all certified skills for a user factory relationship."""
        async with self.session_factory() as session:
            stmt = select(UserFactorySkill).options(
                selectinload(UserFactorySkill.skill),
                selectinload(UserFactorySkill.user_factory),
                selectinload(UserFactorySkill.assigned_by_user)
            ).where(
                UserFactorySkill.user_factory_id == user_factory_id,
                UserFactorySkill.certified == True
            )
            result = await session.execute(stmt)
            return list(result.scalars().all())

    async def update(self, user_factory_skill: UserFactorySkill) -> UserFactorySkill:
        """Update user factory skill."""
        async with self.session_factory() as session:
            session.add(user_factory_skill)
            await session.commit()
            await session.refresh(user_factory_skill)
            return user_factory_skill

    async def delete(self, skill_id: int) -> bool:
        """Delete user factory skill by ID."""
        async with self.session_factory() as session:
            skill = await session.get(UserFactorySkill, skill_id)
            if skill:
                await session.delete(skill)
                await session.commit()
                return True
            return False

    async def delete_by_user_factory_and_skill(self, user_factory_id: int, skill_id: int) -> bool:
        """Delete user factory skill by user factory and skill."""
        async with self.session_factory() as session:
            stmt = select(UserFactorySkill).where(
                UserFactorySkill.user_factory_id == user_factory_id,
                UserFactorySkill.skill_id == skill_id
            )
            result = await session.execute(stmt)
            skill = result.scalar_one_or_none()
            if skill:
                await session.delete(skill)
                await session.commit()
                return True
            return False