from typing import List, Optional, Callable
from datetime import datetime, timezone
from sqlalchemy import select, delete, and_, or_, func, update
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from src.application.interfaces.order_craft_route_repository_interface import OrderCraftRouteRepositoryInterface
from src.domain.entities.order_craft_route import OrderCraftRoute


class OrderCraftRouteRepository(OrderCraftRouteRepositoryInterface):
    """SQLAlchemy implementation of order craft route repository."""
    
    def __init__(self, session_factory: Callable[[], AsyncSession]):
        self.session_factory = session_factory
    
    async def create(self, order_craft_route: OrderCraftRoute) -> OrderCraftRoute:
        async with self.session_factory() as session:
            session.add(order_craft_route)
            await session.commit()
            await session.refresh(order_craft_route)
            return order_craft_route
    
    async def get_by_id(self, order_craft_route_id: int) -> Optional[OrderCraftRoute]:
        async with self.session_factory() as session:
            result = await session.execute(
                select(OrderCraftRoute)
                .options(
                    selectinload(OrderCraftRoute.order_craft),
                    selectinload(OrderCraftRoute.skill),
                    selectinload(OrderCraftRoute.assigned_user)
                )
                .where(OrderCraftRoute.id == order_craft_route_id)
            )
            return result.scalar_one_or_none()
    
    async def get_by_order_craft_id(self, order_craft_id: int) -> List[OrderCraftRoute]:
        async with self.session_factory() as session:
            result = await session.execute(
                select(OrderCraftRoute)
                .options(
                    selectinload(OrderCraftRoute.order_craft),
                    selectinload(OrderCraftRoute.skill),
                    selectinload(OrderCraftRoute.assigned_user)
                )
                .where(OrderCraftRoute.order_craft_id == order_craft_id)
                .order_by(OrderCraftRoute.order)
            )
            return list(result.scalars().all())
    
    async def get_by_order_craft_and_skill(self, order_craft_id: int, skill_code: str) -> Optional[OrderCraftRoute]:
        async with self.session_factory() as session:
            result = await session.execute(
                select(OrderCraftRoute)
                .options(
                    selectinload(OrderCraftRoute.order_craft),
                    selectinload(OrderCraftRoute.skill),
                    selectinload(OrderCraftRoute.assigned_user)
                )
                .where(and_(
                    OrderCraftRoute.order_craft_id == order_craft_id,
                    OrderCraftRoute.skill_code == skill_code
                ))
            )
            return result.scalar_one_or_none()
    
    async def get_by_skill_code(self, skill_code: str) -> List[OrderCraftRoute]:
        async with self.session_factory() as session:
            result = await session.execute(
                select(OrderCraftRoute)
                .options(
                    selectinload(OrderCraftRoute.order_craft),
                    selectinload(OrderCraftRoute.skill),
                    selectinload(OrderCraftRoute.assigned_user)
                )
                .where(OrderCraftRoute.skill_code == skill_code)
                .order_by(OrderCraftRoute.created_at.desc())
            )
            return list(result.scalars().all())
    
    async def get_by_order_no(self, order_no: str) -> List[OrderCraftRoute]:
        async with self.session_factory() as session:
            result = await session.execute(
                select(OrderCraftRoute)
                .options(
                    selectinload(OrderCraftRoute.order_craft),
                    selectinload(OrderCraftRoute.skill),
                    selectinload(OrderCraftRoute.assigned_user)
                )
                .join(OrderCraftRoute.order_craft)
                .where(OrderCraftRoute.order_craft.has(order_no=order_no))
                .order_by(OrderCraftRoute.order_craft_id, OrderCraftRoute.order)
            )
            return list(result.scalars().all())
    
    async def get_active_by_order_craft(self, order_craft_id: int) -> List[OrderCraftRoute]:
        async with self.session_factory() as session:
            result = await session.execute(
                select(OrderCraftRoute)
                .options(
                    selectinload(OrderCraftRoute.order_craft),
                    selectinload(OrderCraftRoute.skill),
                    selectinload(OrderCraftRoute.assigned_user)
                )
                .where(and_(
                    OrderCraftRoute.order_craft_id == order_craft_id,
                    OrderCraftRoute.is_active == True
                ))
                .order_by(OrderCraftRoute.order)
            )
            return list(result.scalars().all())
    
    async def get_by_status(self, status: str) -> List[OrderCraftRoute]:
        async with self.session_factory() as session:
            result = await session.execute(
                select(OrderCraftRoute)
                .options(
                    selectinload(OrderCraftRoute.order_craft),
                    selectinload(OrderCraftRoute.skill),
                    selectinload(OrderCraftRoute.assigned_user)
                )
                .where(OrderCraftRoute.status == status)
                .order_by(OrderCraftRoute.created_at.desc())
            )
            return list(result.scalars().all())
    
    async def get_by_assigned_user(self, user_id: int) -> List[OrderCraftRoute]:
        async with self.session_factory() as session:
            result = await session.execute(
                select(OrderCraftRoute)
                .options(
                    selectinload(OrderCraftRoute.order_craft),
                    selectinload(OrderCraftRoute.skill),
                    selectinload(OrderCraftRoute.assigned_user)
                )
                .where(OrderCraftRoute.assigned_user_id == user_id)
                .order_by(OrderCraftRoute.created_at.desc())
            )
            return list(result.scalars().all())
    
    async def get_next_pending_route(self, order_craft_id: int) -> Optional[OrderCraftRoute]:
        async with self.session_factory() as session:
            result = await session.execute(
                select(OrderCraftRoute)
                .options(
                    selectinload(OrderCraftRoute.order_craft),
                    selectinload(OrderCraftRoute.skill),
                    selectinload(OrderCraftRoute.assigned_user)
                )
                .where(and_(
                    OrderCraftRoute.order_craft_id == order_craft_id,
                    OrderCraftRoute.status == "pending",
                    OrderCraftRoute.is_active == True
                ))
                .order_by(OrderCraftRoute.order)
                .limit(1)
            )
            return result.scalar_one_or_none()
    
    async def get_current_in_progress_route(self, order_craft_id: int) -> Optional[OrderCraftRoute]:
        async with self.session_factory() as session:
            result = await session.execute(
                select(OrderCraftRoute)
                .options(
                    selectinload(OrderCraftRoute.order_craft),
                    selectinload(OrderCraftRoute.skill),
                    selectinload(OrderCraftRoute.assigned_user)
                )
                .where(and_(
                    OrderCraftRoute.order_craft_id == order_craft_id,
                    OrderCraftRoute.status == "in_progress"
                ))
                .order_by(OrderCraftRoute.order)
                .limit(1)
            )
            return result.scalar_one_or_none()
    
    async def update(self, order_craft_route: OrderCraftRoute) -> OrderCraftRoute:
        async with self.session_factory() as session:
            session.add(order_craft_route)
            await session.commit()
            await session.refresh(order_craft_route)
            return order_craft_route
    
    async def delete(self, order_craft_route_id: int) -> bool:
        async with self.session_factory() as session:
            result = await session.execute(
                delete(OrderCraftRoute).where(OrderCraftRoute.id == order_craft_route_id)
            )
            await session.commit()
            return result.rowcount > 0
    
    async def delete_by_order_craft_id(self, order_craft_id: int) -> int:
        async with self.session_factory() as session:
            result = await session.execute(
                delete(OrderCraftRoute).where(OrderCraftRoute.order_craft_id == order_craft_id)
            )
            await session.commit()
            return result.rowcount
    
    async def delete_by_order_no(self, order_no: str) -> int:
        async with self.session_factory() as session:
            result = await session.execute(
                delete(OrderCraftRoute)
                .where(OrderCraftRoute.order_craft_id.in_(
                    select(OrderCraftRoute.order_craft_id)
                    .join(OrderCraftRoute.order_craft)
                    .where(OrderCraftRoute.order_craft.has(order_no=order_no))
                ))
            )
            await session.commit()
            return result.rowcount
    
    async def bulk_create(self, order_craft_routes: List[OrderCraftRoute]) -> List[OrderCraftRoute]:
        async with self.session_factory() as session:
            session.add_all(order_craft_routes)
            await session.commit()
            for route in order_craft_routes:
                await session.refresh(route)
            return order_craft_routes
    
    async def reorder_routes(self, order_craft_id: int, route_orders: List[tuple]) -> bool:
        async with self.session_factory() as session:
            try:
                for skill_code, new_order in route_orders:
                    await session.execute(
                        update(OrderCraftRoute)
                        .where(and_(
                            OrderCraftRoute.order_craft_id == order_craft_id,
                            OrderCraftRoute.skill_code == skill_code
                        ))
                        .values(order=new_order)
                    )
                await session.commit()
                return True
            except Exception:
                await session.rollback()
                return False
    
    async def assign_user_to_route(self, order_craft_route_id: int, user_id: int) -> bool:
        async with self.session_factory() as session:
            result = await session.execute(
                update(OrderCraftRoute)
                .where(OrderCraftRoute.id == order_craft_route_id)
                .values(assigned_user_id=user_id)
            )
            await session.commit()
            return result.rowcount > 0
    
    async def unassign_user_from_route(self, order_craft_route_id: int) -> bool:
        async with self.session_factory() as session:
            result = await session.execute(
                update(OrderCraftRoute)
                .where(OrderCraftRoute.id == order_craft_route_id)
                .values(assigned_user_id=None)
            )
            await session.commit()
            return result.rowcount > 0
    
    async def bulk_assign_users(self, assignments: List[tuple]) -> bool:
        async with self.session_factory() as session:
            try:
                for order_craft_route_id, user_id in assignments:
                    await session.execute(
                        update(OrderCraftRoute)
                        .where(OrderCraftRoute.id == order_craft_route_id)
                        .values(assigned_user_id=user_id)
                    )
                await session.commit()
                return True
            except Exception:
                await session.rollback()
                return False
    
    async def get_route_statistics(self, order_craft_id: Optional[int] = None, order_no: Optional[str] = None) -> dict:
        async with self.session_factory() as session:
            base_query = select(OrderCraftRoute)
            
            if order_craft_id:
                base_query = base_query.where(OrderCraftRoute.order_craft_id == order_craft_id)
            elif order_no:
                base_query = base_query.join(OrderCraftRoute.order_craft).where(
                    OrderCraftRoute.order_craft.has(order_no=order_no)
                )
            
            # Total count
            total_result = await session.execute(
                select(func.count(OrderCraftRoute.id)).select_from(base_query.subquery())
            )
            total_routes = total_result.scalar()
            
            # Status breakdown
            status_result = await session.execute(
                select(OrderCraftRoute.status, func.count(OrderCraftRoute.id))
                .select_from(base_query.subquery())
                .group_by(OrderCraftRoute.status)
            )
            status_breakdown = dict(status_result.all())
            
            # Active breakdown
            active_result = await session.execute(
                select(OrderCraftRoute.is_active, func.count(OrderCraftRoute.id))
                .select_from(base_query.subquery())
                .group_by(OrderCraftRoute.is_active)
            )
            active_breakdown = dict(active_result.all())
            
            return {
                "total_routes": total_routes,
                "status_breakdown": status_breakdown,
                "active_breakdown": active_breakdown,
                "pending_routes": status_breakdown.get("pending", 0),
                "in_progress_routes": status_breakdown.get("in_progress", 0),
                "completed_routes": status_breakdown.get("completed", 0),
                "skipped_routes": status_breakdown.get("skipped", 0),
                "active_routes": active_breakdown.get(True, 0),
                "inactive_routes": active_breakdown.get(False, 0)
            }
    
    async def get_user_workload(self, user_id: int) -> dict:
        async with self.session_factory() as session:
            # Total assigned routes
            total_result = await session.execute(
                select(func.count(OrderCraftRoute.id))
                .where(OrderCraftRoute.assigned_user_id == user_id)
            )
            total_assigned = total_result.scalar()
            
            # Status breakdown for assigned routes
            status_result = await session.execute(
                select(OrderCraftRoute.status, func.count(OrderCraftRoute.id))
                .where(OrderCraftRoute.assigned_user_id == user_id)
                .group_by(OrderCraftRoute.status)
            )
            status_breakdown = dict(status_result.all())
            
            # Active assigned routes
            active_result = await session.execute(
                select(func.count(OrderCraftRoute.id))
                .where(and_(
                    OrderCraftRoute.assigned_user_id == user_id,
                    OrderCraftRoute.is_active == True
                ))
            )
            active_assigned = active_result.scalar()
            
            return {
                "total_assigned_routes": total_assigned,
                "active_assigned_routes": active_assigned,
                "status_breakdown": status_breakdown,
                "pending_routes": status_breakdown.get("pending", 0),
                "in_progress_routes": status_breakdown.get("in_progress", 0),
                "completed_routes": status_breakdown.get("completed", 0),
                "workload_percentage": 0.0  # This could be calculated based on business rules
            }
    
    async def get_next_route_in_craft(self, order_craft_id: int, current_order: int, session: AsyncSession) -> Optional[OrderCraftRoute]:
        """Get the next route in the craft workflow after the current order."""
        result = await session.execute(
            select(OrderCraftRoute)
            .options(
                selectinload(OrderCraftRoute.order_craft),
                selectinload(OrderCraftRoute.skill),
                selectinload(OrderCraftRoute.assigned_user)
            )
            .where(and_(
                OrderCraftRoute.order_craft_id == order_craft_id,
                OrderCraftRoute.order > current_order,
                OrderCraftRoute.is_active == True
            ))
            .order_by(OrderCraftRoute.order)
            .limit(1)
        )
        return result.scalar_one_or_none()
    
    async def bulk_upsert(self, order_craft_routes: List[OrderCraftRoute], session=None) -> List[OrderCraftRoute]:
        """Create or update multiple order craft routes. Uses order_craft_id + skill_code as unique key."""
        
        async def _do_upsert(sess: AsyncSession) -> List[OrderCraftRoute]:
            # Disable autoflush to prevent premature flushing during queries
            with sess.no_autoflush:
                result_routes = []
                
                if not order_craft_routes:
                    return []
                
                # Group by order_craft_id
                routes_by_craft = {}
                for route in order_craft_routes:
                    if route.order_craft_id not in routes_by_craft:
                        routes_by_craft[route.order_craft_id] = []
                    routes_by_craft[route.order_craft_id].append(route)
                
                for order_craft_id, routes in routes_by_craft.items():
                    skill_codes = [route.skill_code for route in routes]
                    
                    # Get existing routes
                    existing_routes = await self.get_by_order_craft_and_skills(order_craft_id, skill_codes, sess)
                    existing_by_skill = {route.skill_code: route for route in existing_routes}
                    
                    for route in routes:
                        if route.skill_code in existing_by_skill:
                            # Update existing
                            existing_route = existing_by_skill[route.skill_code]
                            existing_route.name = route.name
                            existing_route.code = route.code
                            existing_route.order = route.order
                            existing_route.measurement_types = route.measurement_types
                            existing_route.registration_types = route.registration_types
                            existing_route.is_required = route.is_required
                            existing_route.is_active = route.is_active
                            existing_route.estimated_duration_minutes = route.estimated_duration_minutes
                            existing_route.price = route.price
                            existing_route.total_cost = route.total_cost
                            existing_route.notes = route.notes
                            existing_route.updated_at = route.updated_at
                            result_routes.append(existing_route)
                        else:
                            # Create new - ensure defaults are set
                            if route.is_active is None:
                                route.is_active = True
                            if route.status is None:
                                route.status = "pending"
                            if route.created_at is None:
                                route.created_at = datetime.now(timezone.utc)
                            if route.updated_at is None:
                                route.updated_at = datetime.now(timezone.utc)
                            sess.add(route)
                            result_routes.append(route)
            
            # Always flush to get IDs for new entities
            await sess.flush()
            
            # Refresh objects that need IDs
            for route in result_routes:
                if route.id is None:
                    await sess.refresh(route)
            
            return result_routes
        
        if session:
            # Use provided session
            return await _do_upsert(session)
        else:
            # Create new session
            async with self.session_factory() as sess:
                result = await _do_upsert(sess)
                await sess.commit()
                return result
    
    async def get_by_order_craft_and_skills(self, order_craft_id: int, skill_codes: List[str], session=None) -> List[OrderCraftRoute]:
        """Get order craft routes by order craft ID and skill codes."""
        if session:
            # Use provided session directly
            result = await session.execute(
                select(OrderCraftRoute)
                .options(
                    selectinload(OrderCraftRoute.order_craft),
                    selectinload(OrderCraftRoute.skill),
                    selectinload(OrderCraftRoute.assigned_user)
                )
                .where(
                    and_(
                        OrderCraftRoute.order_craft_id == order_craft_id,
                        OrderCraftRoute.skill_code.in_(skill_codes)
                    )
                )
            )
            return list(result.scalars().all())
        else:
            # Create new session
            async with self.session_factory() as sess:
                result = await sess.execute(
                    select(OrderCraftRoute)
                    .options(
                        selectinload(OrderCraftRoute.order_craft),
                        selectinload(OrderCraftRoute.skill),
                        selectinload(OrderCraftRoute.assigned_user)
                    )
                    .where(
                        and_(
                            OrderCraftRoute.order_craft_id == order_craft_id,
                            OrderCraftRoute.skill_code.in_(skill_codes)
                        )
                    )
                )
                return list(result.scalars().all())