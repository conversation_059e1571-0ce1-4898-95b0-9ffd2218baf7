from typing import List, Optional, Callable
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, or_, and_
from sqlalchemy.orm import selectinload
from src.domain.entities.craft import Craft
from src.domain.entities.craft_route import CraftRoute
from src.application.interfaces.craft_repository_interface import CraftRepositoryInterface


class CraftRepository(CraftRepositoryInterface):
    """SQLAlchemy implementation of craft repository."""
    
    def __init__(self, session_factory: Callable[[], AsyncSession]):
        self.session_factory = session_factory
    
    async def create(self, craft: Craft) -> Craft:
        async with self.session_factory() as session:
            session.add(craft)
            await session.commit()
            await session.refresh(craft)
            return craft
    
    async def get_by_id(self, craft_id: int) -> Optional[Craft]:
        async with self.session_factory() as session:
            result = await session.execute(
                select(Craft)
                .options(
                    selectinload(Craft.craft_routes).selectinload(CraftRoute.skill)
                )
                .where(Craft.id == craft_id)
            )
            return result.scalar_one_or_none()
    
    async def get_by_code(self, code: str) -> Optional[Craft]:
        async with self.session_factory() as session:
            result = await session.execute(
                select(Craft)
                .options(
                    selectinload(Craft.craft_routes).selectinload(CraftRoute.skill)
                )
                .where(Craft.code == code)
            )
            return result.scalar_one_or_none()
    
    async def get_all(self, skip: int = 0, limit: int = 100) -> List[Craft]:
        async with self.session_factory() as session:
            result = await session.execute(
                select(Craft)
                .options(
                    selectinload(Craft.craft_routes).selectinload(CraftRoute.skill)
                )
                .order_by(Craft.priority.desc(), Craft.name)
                .offset(skip)
                .limit(limit)
            )
            return list(result.scalars().all())
    
    async def get_enabled_crafts(self) -> List[Craft]:
        async with self.session_factory() as session:
            result = await session.execute(
                select(Craft)
                .options(
                    selectinload(Craft.craft_routes).selectinload(CraftRoute.skill)
                )
                .where(Craft.enabled == True)
                .order_by(Craft.priority.desc(), Craft.name)
            )
            return list(result.scalars().all())
    
    async def get_by_priority_range(self, min_priority: int, max_priority: int) -> List[Craft]:
        async with self.session_factory() as session:
            result = await session.execute(
                select(Craft)
                .options(
                    selectinload(Craft.craft_routes).selectinload(CraftRoute.skill)
                )
                .where(
                    and_(
                        Craft.priority >= min_priority,
                        Craft.priority <= max_priority
                    )
                )
                .order_by(Craft.priority.desc(), Craft.name)
            )
            return list(result.scalars().all())
    
    async def search_crafts(self, search_term: str) -> List[Craft]:
        async with self.session_factory() as session:
            search_pattern = f"%{search_term}%"
            result = await session.execute(
                select(Craft)
                .options(
                    selectinload(Craft.craft_routes).selectinload(CraftRoute.skill)
                )
                .where(
                    or_(
                        Craft.code.ilike(search_pattern),
                        Craft.name.ilike(search_pattern),
                        Craft.description.ilike(search_pattern)
                    )
                )
                .order_by(Craft.priority.desc(), Craft.name)
            )
            return list(result.scalars().all())
    
    async def update(self, craft: Craft) -> Craft:
        async with self.session_factory() as session:
            session.add(craft)
            await session.commit()
            await session.refresh(craft)
            return craft
    
    async def delete(self, craft_id: int) -> bool:
        async with self.session_factory() as session:
            craft = await session.get(Craft, craft_id)
            if craft:
                await session.delete(craft)
                await session.commit()
                return True
            return False
    
    async def get_crafts_by_codes(self, codes: List[str]) -> List[Craft]:
        async with self.session_factory() as session:
            result = await session.execute(
                select(Craft)
                .options(
                    selectinload(Craft.craft_routes).selectinload(CraftRoute.skill)
                )
                .where(Craft.code.in_(codes))
                .order_by(Craft.priority.desc(), Craft.name)
            )
            return list(result.scalars().all())