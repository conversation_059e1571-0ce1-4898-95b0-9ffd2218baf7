from typing import List, Optional, Callable
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from sqlalchemy.orm import selectinload
from src.domain.entities.user import User
from src.domain.entities.role import Role
from src.domain.entities.permission import Permission
from src.application.interfaces.user_repository_interface import UserRepositoryInterface


class UserRepository(UserRepositoryInterface):
    """SQLAlchemy implementation of user repository."""
    
    def __init__(self, session_factory: Callable[[], AsyncSession]):
        self.session_factory = session_factory
    
    async def create(self, user: User) -> User:
        async with self.session_factory() as session:
            session.add(user)
            await session.commit()
            await session.refresh(user)
            return user
    
    async def get_by_id(self, user_id: int) -> Optional[User]:
        async with self.session_factory() as session:
            result = await session.execute(
                select(User)
                .options(
                    selectinload(User.role)
                    .selectinload(Role.permissions)
                )
                .where(User.id == user_id)
            )
            user = result.scalar_one_or_none()
            
            if user:
                # Make the object detached from the session
                session.expunge(user)
                
            return user
    
    async def get_by_username(self, username: str) -> Optional[User]:
        async with self.session_factory() as session:
            result = await session.execute(
                select(User)
                .options(
                    selectinload(User.role)
                    .selectinload(Role.permissions)
                )
                .where(User.username == username)
            )
            user = result.scalar_one_or_none()
            
            if user:
                # Make the object detached from the session
                session.expunge(user)
                
            return user
    
    async def get_by_email(self, email: str) -> Optional[User]:
        async with self.session_factory() as session:
            result = await session.execute(
                select(User)
                .options(
                    selectinload(User.role).selectinload(Role.permissions).selectinload(Permission.children)
                )
                .where(User.email == email)
            )
            return result.scalar_one_or_none()
    
    async def get_by_phone(self, phone: str) -> Optional[User]:
        async with self.session_factory() as session:
            result = await session.execute(
                select(User)
                .options(
                    selectinload(User.role).selectinload(Role.permissions).selectinload(Permission.children)
                )
                .where(User.phone == phone)
            )
            return result.scalar_one_or_none()
    
    async def update(self, user: User) -> User:
        async with self.session_factory() as session:
            session.add(user)
            await session.commit()
            await session.refresh(user)
            return user
    
    async def delete(self, user_id: int) -> bool:
        async with self.session_factory() as session:
            user = await session.get(User, user_id)
            if user:
                await session.delete(user)
                await session.commit()
                return True
            return False
    
    async def get_all(self, skip: int = 0, limit: int = 100) -> List[User]:
        async with self.session_factory() as session:
            result = await session.execute(
                select(User)
                .options(
                    selectinload(User.role).selectinload(Role.permissions).selectinload(Permission.children)
                )
                .offset(skip).limit(limit)
            )
            return list(result.scalars().all())