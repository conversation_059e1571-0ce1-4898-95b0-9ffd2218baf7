from typing import List, Optional, Callable
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload
from src.application.interfaces.permission_repository_interface import PermissionRepositoryInterface
from src.domain.entities.permission import Permission


class PermissionRepository(PermissionRepositoryInterface):
    """SQLAlchemy implementation of Permission repository."""

    def __init__(self, session_factory: Callable[[], AsyncSession]):
        self.session_factory = session_factory

    async def create(self, permission: Permission) -> Permission:
        """Create a new permission."""
        async with self.session_factory() as session:
            session.add(permission)
            await session.commit()
            await session.refresh(permission)
            return permission

    async def get_by_id(self, permission_id: int) -> Optional[Permission]:
        """Get permission by ID."""
        async with self.session_factory() as session:
            stmt = select(Permission).options(
                selectinload(Permission.parent),
                selectinload(Permission.children),
                selectinload(Permission.roles)
            ).where(Permission.id == permission_id)
            result = await session.execute(stmt)
            return result.scalar_one_or_none()

    async def get_by_code(self, code: str) -> Optional[Permission]:
        """Get permission by code."""
        async with self.session_factory() as session:
            stmt = select(Permission).options(
                selectinload(Permission.parent),
                selectinload(Permission.children),
                selectinload(Permission.roles)
            ).where(Permission.code == code)
            result = await session.execute(stmt)
            return result.scalar_one_or_none()

    async def get_all(self) -> List[Permission]:
        """Get all permissions."""
        async with self.session_factory() as session:
            stmt = select(Permission)
            result = await session.execute(stmt)
            return list(result.scalars().all())

    async def get_root_permissions(self) -> List[Permission]:
        """Get all root permissions (no parent)."""
        async with self.session_factory() as session:
            stmt = select(Permission).where(Permission.parent_id.is_(None))
            result = await session.execute(stmt)
            return list(result.scalars().all())

    async def get_children(self, parent_id: int) -> List[Permission]:
        """Get all child permissions of a parent."""
        async with self.session_factory() as session:
            stmt = select(Permission).options(
                selectinload(Permission.parent),
                selectinload(Permission.children),
                selectinload(Permission.roles)
            ).where(Permission.parent_id == parent_id)
            result = await session.execute(stmt)
            return list(result.scalars().all())

    async def update(self, permission: Permission) -> Permission:
        """Update permission."""
        async with self.session_factory() as session:
            session.add(permission)
            await session.commit()
            await session.refresh(permission)
            return permission

    async def delete(self, permission_id: int) -> bool:
        """Delete permission by ID."""
        async with self.session_factory() as session:
            permission = await session.get(Permission, permission_id)
            if permission:
                await session.delete(permission)
                await session.commit()
                return True
            return False

    async def get_permission_tree(self) -> List[Permission]:
        """Get hierarchical permission tree."""
        # Get all root permissions with their children loaded recursively
        root_permissions = await self.get_root_permissions()
        return root_permissions