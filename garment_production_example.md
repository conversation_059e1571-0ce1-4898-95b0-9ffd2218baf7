# 服装生产管理系统 - 部位(Part)和扎(Bundle)设计说明

## 业务场景示例

### 订单信息
- **订单号**: `202401001`
- **款号**: `SKC_001` 
- **颜色**: `蓝色`
- **订单明细**:
  - S码: 100件
  - M码: 200件

### 生产分解过程

#### 1. 订单分解为部位(Parts)

订单 `202401001` 可以分解为两个部位：

**部位1: 上衣**
- **部位号**: `202401001_P01_FRONT_BODY` (前身)
- **部位类型**: `FRONT_BODY`
- **部位名称**: `上衣前身`
- **数量**: 300件 (S:100 + M:200)

**部位2: 短裤**  
- **部位号**: `202401001_P02_BACK_BODY` (后身)
- **部位类型**: `BACK_BODY`
- **部位名称**: `短裤后身`
- **数量**: 300件 (S:100 + M:200)

#### 2. 部位分解为扎(Bundles)

每个部位按尺码分为多个扎：

**上衣前身 - S码扎**
- `202401001_P01_FRONT_BODY_S_01`: S码第1扎, 50件
- `202401001_P01_FRONT_BODY_S_02`: S码第2扎, 50件

**上衣前身 - M码扎**
- `202401001_P01_FRONT_BODY_M_01`: M码第1扎, 40件
- `202401001_P01_FRONT_BODY_M_02`: M码第2扎, 40件
- `202401001_P01_FRONT_BODY_M_03`: M码第3扎, 40件
- `202401001_P01_FRONT_BODY_M_04`: M码第4扎, 40件
- `202401001_P01_FRONT_BODY_M_05`: M码第5扎, 40件

**短裤后身**同样按照相同规则分扎。

## 数据库表结构

### Parts表 (部位表)

| 字段 | 类型 | 说明 | 示例 |
|------|------|------|------|
| part_no | String(100) | 部位号 | `202401001_P01_FRONT_BODY` |
| order_no | String(100) | 订单号 | `202401001` |
| style_no | String(100) | 款号 | `SKC_001` |
| color | String(50) | 颜色 | `蓝色` |
| part_type | Enum | 部位类型 | `FRONT_BODY` |
| part_name | String(100) | 部位名称 | `上衣前身` |
| part_sequence | Integer | 部位序号 | `1` |
| total_quantity | Integer | 总件数 | `300` |
| completed_quantity | Integer | 已完成件数 | `0` |
| status | Enum | 状态 | `PLANNED` |
| machine_no | String(50) | 机床编号 | `MC001` |
| process_route | String(200) | 工序路线 | `裁剪->缝制->质检` |

### Bundles表 (扎表)

| 字段 | 类型 | 说明 | 示例 |
|------|------|------|------|
| bundle_no | String(100) | 扎号 | `202401001_P01_FRONT_BODY_S_01` |
| part_no | String(100) | 部位号 | `202401001_P01_FRONT_BODY` |
| order_no | String(100) | 订单号 | `202401001` |
| style_no | String(100) | 款号 | `SKC_001` |
| color | String(50) | 颜色 | `蓝色` |
| size | String(20) | 尺码 | `S` |
| bundle_sequence | Integer | 扎序号 | `1` |
| quantity | Integer | 扎件数 | `50` |
| completed_quantity | Integer | 已完成件数 | `0` |
| defective_quantity | Integer | 次品件数 | `0` |
| status | Enum | 状态 | `PLANNED` |
| cutting_machine | String(50) | 裁剪机床 | `CUT001` |
| sewing_machine | String(50) | 缝制机床 | `SEW001` |

## 生产状态流程

### 部位(Part)状态流程
```
PLANNED → CUTTING → SEWING → QUALITY_CHECK → COMPLETED
```

### 扎(Bundle)状态流程
```
PLANNED → CUTTING → CUT_COMPLETED → SEWING → SEW_COMPLETED → QUALITY_CHECK → COMPLETED
```

## 编号规则

### 部位号生成规则
```
{订单号}_P{部位序号:02d}_{部位类型}
示例: 202401001_P01_FRONT_BODY
```

### 扎号生成规则
```
{部位号}_{尺码}_{扎序号:02d}
示例: 202401001_P01_FRONT_BODY_S_01
```

## 业务功能

### 部位管理
- 按订单创建部位
- 部位进度跟踪
- 机床分配
- 工序路线管理

### 扎管理
- 按部位和尺码创建扎
- 扎的生产进度跟踪
- 质量管理(良品、次品、返工)
- 工人分配(裁剪工、缝制工、质检员)
- 生产时间跟踪

### 统计分析
- 按订单/部位/扎的生产进度
- 尺码分布统计
- 质量统计
- 效率分析

这个设计完全符合服装生产行业的实际需求，支持从订单到部位到扎的完整生产管理流程。