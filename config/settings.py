from typing import Optional
from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict


class DatabaseSettings(BaseSettings):
    url: str = Field(..., alias="DATABASE_URL")
    echo: bool = Field(False, alias="DATABASE_ECHO")
    
    model_config = SettingsConfigDict(env_file=".env", extra="ignore")


class RedisSettings(BaseSettings):
    url: str = Field("redis://localhost:6379/0", alias="REDIS_URL")
    
    model_config = SettingsConfigDict(env_file=".env", extra="ignore")


class JWTSettings(BaseSettings):
    secret_key: str = Field(..., alias="SECRET_KEY")
    algorithm: str = Field("HS256", alias="ALGORITHM")
    access_token_expire_minutes: int = Field(30, alias="ACCESS_TOKEN_EXPIRE_MINUTES")
    
    model_config = SettingsConfigDict(env_file=".env", extra="ignore")


class APISettings(BaseSettings):
    v1_prefix: str = Field("/api/v1", alias="API_V1_PREFIX")
    project_name: str = Field("Production Ticket System", alias="PROJECT_NAME")
    version: str = Field("1.0.0", alias="VERSION")
    debug: bool = Field(False, alias="DEBUG")
    
    model_config = SettingsConfigDict(env_file=".env", extra="ignore")


class CelerySettings(BaseSettings):
    broker_url: str = Field("redis://localhost:6379/1", alias="CELERY_BROKER_URL")
    result_backend: str = Field("redis://localhost:6379/2", alias="CELERY_RESULT_BACKEND")
    
    model_config = SettingsConfigDict(env_file=".env", extra="ignore")


class LoggingSettings(BaseSettings):
    level: str = Field("INFO", alias="LOG_LEVEL")
    format: str = Field("json", alias="LOG_FORMAT")
    
    model_config = SettingsConfigDict(env_file=".env", extra="ignore")


class Settings(BaseSettings):
    database: DatabaseSettings = DatabaseSettings()
    redis: RedisSettings = RedisSettings()
    jwt: JWTSettings = JWTSettings()
    api: APISettings = APISettings()
    celery: CelerySettings = CelerySettings()
    logging: LoggingSettings = LoggingSettings()
    
    model_config = SettingsConfigDict(env_file=".env", extra="ignore")


settings = Settings()