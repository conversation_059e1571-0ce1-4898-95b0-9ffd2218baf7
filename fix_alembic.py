#!/usr/bin/env python3
"""
Script to fix alembic version issues
"""

import asyncio
from sqlalchemy import text
from src.infrastructure.database.database import Database
from config import settings


async def fix_alembic_version():
    """Fix the alembic version table"""
    db = Database(db_url=settings.database.url, echo=False)
    
    async with db.session_factory() as session:
        # Update alembic version to the correct one
        await session.execute(text("DELETE FROM alembic_version"))
        await session.execute(text("INSERT INTO alembic_version (version_num) VALUES ('cad706eb7742')"))
        await session.commit()
        print("Fixed alembic version table")


if __name__ == "__main__":
    asyncio.run(fix_alembic_version())