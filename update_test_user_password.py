#!/usr/bin/env python3
"""
Scrip<PERSON> to update the test user password with a proper bcrypt hash.
"""
import asyncio
import bcrypt
from sqlalchemy import text
from src.infrastructure.database.database import get_async_session

async def update_test_user_password():
    """Update the test user password with proper bcrypt hash."""
    # Generate proper bcrypt hash
    password = "testpass123"
    salt = bcrypt.gensalt()
    hashed_password = bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')
    
    print(f"Generated hash: {hashed_password}")
    
    # Update database
    async with get_async_session() as session:
        query = text("""
            UPDATE users 
            SET hashed_password = :hashed_password 
            WHERE username = 'testuser'
        """)
        
        result = await session.execute(query, {"hashed_password": hashed_password})
        await session.commit()
        
        print(f"Updated {result.rowcount} user(s)")
        print("Test user password updated successfully!")

if __name__ == "__main__":
    asyncio.run(update_test_user_password())