components:
  schemas:
    AddUserToFactoryDTO:
      description: DTO for adding user to factory.
      properties:
        factory_role:
          default: WORKER
          description: Initial role in factory
          title: Factory Role
          type: string
        start_date:
          anyOf:
          - type: string
          - type: 'null'
          description: Start date (ISO format)
          title: Start Date
        user_id:
          description: User ID to add
          title: User Id
          type: integer
      required:
      - user_id
      title: AddUserToFactoryDTO
      type: object
    AddUsersToFactoryDTO:
      description: DTO for adding multiple users to factory.
      properties:
        users:
          description: List of users to add
          items:
            $ref: '#/components/schemas/AddUserToFactoryDTO'
          title: Users
          type: array
      required:
      - users
      title: AddUsersToFactoryDTO
      type: object
    AssignSkillDTO:
      description: DTO for assigning skill to user.
      properties:
        notes:
          anyOf:
          - type: string
          - type: 'null'
          description: Additional notes
          title: Notes
        proficiency_level:
          default: BEGINNER
          description: Initial proficiency level
          title: Proficiency Level
          type: string
        skill_id:
          description: Skill ID
          title: Skill Id
          type: integer
        user_id:
          description: User ID
          title: User Id
          type: integer
      required:
      - user_id
      - skill_id
      title: AssignSkillDTO
      type: object
    AssignSkillsDTO:
      description: DTO for assigning multiple skills to user.
      properties:
        skills:
          description: List of skills to assign
          items:
            $ref: '#/components/schemas/AssignSkillDTO'
          title: Skills
          type: array
        user_id:
          description: User ID
          title: User Id
          type: integer
      required:
      - user_id
      - skills
      title: AssignSkillsDTO
      type: object
    AvailableFactoriesDTO:
      description: DTO for list of available factories.
      properties:
        current_factory_id:
          anyOf:
          - type: integer
          - type: 'null'
          title: Current Factory Id
        factories:
          items:
            $ref: '#/components/schemas/AvailableFactoryDTO'
          title: Factories
          type: array
      required:
      - factories
      title: AvailableFactoriesDTO
      type: object
    AvailableFactoryDTO:
      description: DTO for available factory in switch list.
      properties:
        department_id:
          anyOf:
          - type: integer
          - type: 'null'
          title: Department Id
        department_name:
          anyOf:
          - type: string
          - type: 'null'
          title: Department Name
        employee_id:
          anyOf:
          - type: string
          - type: 'null'
          title: Employee Id
        factory_code:
          title: Factory Code
          type: string
        factory_id:
          title: Factory Id
          type: integer
        factory_name:
          title: Factory Name
          type: string
        is_current:
          title: Is Current
          type: boolean
        is_manager:
          title: Is Manager
          type: boolean
        position:
          anyOf:
          - type: string
          - type: 'null'
          title: Position
        role:
          title: Role
          type: string
      required:
      - factory_id
      - factory_name
      - factory_code
      - role
      - is_manager
      - is_current
      title: AvailableFactoryDTO
      type: object
    AvailableUsersDTO:
      description: DTO for available users (not in factory).
      properties:
        total:
          description: Total number of available users
          title: Total
          type: integer
        users:
          description: List of available users
          items:
            $ref: '#/components/schemas/UserSummaryDTO'
          title: Users
          type: array
      required:
      - users
      - total
      title: AvailableUsersDTO
      type: object
    BindUserRoleDTO:
      description: DTO for binding system role to user.
      properties:
        role_id:
          description: Role ID to assign
          title: Role Id
          type: integer
        user_id:
          description: User ID
          title: User Id
          type: integer
      required:
      - user_id
      - role_id
      title: BindUserRoleDTO
      type: object
    Body_login_api_v1_auth_token_post:
      properties:
        client_id:
          anyOf:
          - type: string
          - type: 'null'
          title: Client Id
        client_secret:
          anyOf:
          - type: string
          - type: 'null'
          title: Client Secret
        grant_type:
          anyOf:
          - pattern: password
            type: string
          - type: 'null'
          title: Grant Type
        password:
          title: Password
          type: string
        scope:
          default: ''
          title: Scope
          type: string
        username:
          title: Username
          type: string
      required:
      - username
      - password
      title: Body_login_api_v1_auth_token_post
      type: object
    CertifySkillDTO:
      description: DTO for certifying user in skill.
      properties:
        certification_expires:
          anyOf:
          - type: string
          - type: 'null'
          description: Certification expiry date (ISO format)
          title: Certification Expires
        notes:
          anyOf:
          - type: string
          - type: 'null'
          description: Certification notes
          title: Notes
        user_factory_skill_id:
          description: User factory skill ID
          title: User Factory Skill Id
          type: integer
      required:
      - user_factory_skill_id
      title: CertifySkillDTO
      type: object
    FactoryJoinApprovalDTO:
      description: DTO for approving/rejecting factory join requests.
      properties:
        action:
          description: approve or reject
          title: Action
          type: string
        department_id:
          anyOf:
          - type: integer
          - type: 'null'
          description: Department assignment (if approved)
          title: Department Id
        employee_id:
          anyOf:
          - maxLength: 50
            type: string
          - type: 'null'
          description: Employee ID (if approved)
          title: Employee Id
        position:
          anyOf:
          - maxLength: 100
            type: string
          - type: 'null'
          description: Position title (if approved)
          title: Position
        reason:
          anyOf:
          - maxLength: 500
            type: string
          - type: 'null'
          description: Rejection reason (if rejected)
          title: Reason
        role:
          anyOf:
          - $ref: '#/components/schemas/UserFactoryRoleEnum'
          - type: 'null'
          default: worker
          description: User role in factory
        start_date:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          description: Start date (if approved)
          title: Start Date
        user_factory_id:
          description: UserFactory record ID
          title: User Factory Id
          type: integer
      required:
      - user_factory_id
      - action
      title: FactoryJoinApprovalDTO
      type: object
    FactoryJoinRequestDTO:
      description: DTO for requesting to join a factory.
      properties:
        department_id:
          anyOf:
          - type: integer
          - type: 'null'
          description: Optional department ID
          title: Department Id
        factory_id:
          description: Factory ID to join
          title: Factory Id
          type: integer
        message:
          anyOf:
          - maxLength: 500
            type: string
          - type: 'null'
          description: Optional message to manager
          title: Message
      required:
      - factory_id
      title: FactoryJoinRequestDTO
      type: object
    FactoryUserDTO:
      description: DTO for factory user with factory-specific information.
      properties:
        certified_skills_count:
          default: 0
          description: Number of certified skills
          title: Certified Skills Count
          type: integer
        factory_role:
          description: Role in factory (WORKER, SUPERVISOR, MANAGER, ADMIN)
          title: Factory Role
          type: string
        factory_status:
          description: Status in factory (APPROVED, SUSPENDED, etc.)
          title: Factory Status
          type: string
        joined_at:
          anyOf:
          - type: string
          - type: 'null'
          description: Date joined factory
          title: Joined At
        skills:
          description: User's skills in this factory
          items:
            $ref: '#/components/schemas/UserFactorySkillDTO'
          title: Skills
          type: array
        skills_count:
          default: 0
          description: Total number of skills
          title: Skills Count
          type: integer
        user:
          $ref: '#/components/schemas/UserSummaryDTO'
          description: User information
      required:
      - user
      - factory_role
      - factory_status
      title: FactoryUserDTO
      type: object
    FactoryUserListDTO:
      description: DTO for factory user list response.
      properties:
        factory_id:
          description: Factory ID
          title: Factory Id
          type: integer
        factory_name:
          description: Factory name
          title: Factory Name
          type: string
        total:
          description: Total number of users
          title: Total
          type: integer
        users:
          description: List of factory users
          items:
            $ref: '#/components/schemas/FactoryUserDTO'
          title: Users
          type: array
      required:
      - users
      - total
      - factory_id
      - factory_name
      title: FactoryUserListDTO
      type: object
    GenerateImageCodeDTO:
      description: DTO for generating image validation code.
      properties:
        session_id:
          anyOf:
          - type: string
          - type: 'null'
          description: Optional session ID, will generate if not provided
          title: Session Id
      title: GenerateImageCodeDTO
      type: object
    HTTPValidationError:
      properties:
        detail:
          items:
            $ref: '#/components/schemas/ValidationError'
          title: Detail
          type: array
      title: HTTPValidationError
      type: object
    ImageCodeResponseDTO:
      description: Response DTO for image validation code.
      properties:
        expires_in_seconds:
          title: Expires In Seconds
          type: integer
        image_base64:
          title: Image Base64
          type: string
        session_id:
          title: Session Id
          type: string
      required:
      - session_id
      - image_base64
      - expires_in_seconds
      title: ImageCodeResponseDTO
      type: object
    MyFactoriesDTO:
      description: DTO for current user's factories.
      properties:
        active_factories:
          items:
            $ref: '#/components/schemas/UserFactoryResponseDTO'
          title: Active Factories
          type: array
        managed_factories:
          items:
            $ref: '#/components/schemas/UserFactoryResponseDTO'
          title: Managed Factories
          type: array
        pending_requests:
          items:
            $ref: '#/components/schemas/UserFactoryResponseDTO'
          title: Pending Requests
          type: array
      required:
      - active_factories
      - pending_requests
      - managed_factories
      title: MyFactoriesDTO
      type: object
    PermissionListDTO:
      description: DTO for permission list response.
      properties:
        permissions:
          description: List of permissions
          items:
            $ref: '#/components/schemas/PermissionResponseDTO'
          title: Permissions
          type: array
        total:
          description: Total number of permissions
          title: Total
          type: integer
      required:
      - permissions
      - total
      title: PermissionListDTO
      type: object
    PermissionResponseDTO:
      description: DTO for permission response.
      properties:
        code:
          description: Unique permission code
          maxLength: 100
          minLength: 1
          title: Code
          type: string
        id:
          description: Permission ID
          title: Id
          type: integer
        name:
          description: Permission name
          maxLength: 200
          minLength: 1
          title: Name
          type: string
        parent_id:
          anyOf:
          - type: integer
          - type: 'null'
          description: Parent permission ID for hierarchical structure
          title: Parent Id
      required:
      - code
      - name
      - id
      title: PermissionResponseDTO
      type: object
    PermissionTreeDTO:
      description: DTO for hierarchical permission tree.
      properties:
        children:
          description: Child permissions
          items:
            $ref: '#/components/schemas/PermissionTreeDTO'
          title: Children
          type: array
        code:
          description: Permission code
          title: Code
          type: string
        id:
          description: Permission ID
          title: Id
          type: integer
        name:
          description: Permission name
          title: Name
          type: string
        parent_id:
          anyOf:
          - type: integer
          - type: 'null'
          description: Parent permission ID
          title: Parent Id
      required:
      - id
      - code
      - name
      title: PermissionTreeDTO
      type: object
    PhonePasswordLoginDTO:
      description: DTO for phone + password + image validation code login.
      properties:
        image_code:
          maxLength: 6
          minLength: 4
          title: Image Code
          type: string
        password:
          minLength: 1
          title: Password
          type: string
        phone:
          description: Chinese mobile number
          pattern: ^1[3-9]\d{9}$
          title: Phone
          type: string
        session_id:
          description: Session ID for image validation code
          title: Session Id
          type: string
      required:
      - phone
      - password
      - image_code
      - session_id
      title: PhonePasswordLoginDTO
      type: object
    PhoneSmsLoginDTO:
      description: DTO for phone + SMS + image validation code login.
      properties:
        image_code:
          maxLength: 6
          minLength: 4
          title: Image Code
          type: string
        phone:
          description: Chinese mobile number
          pattern: ^1[3-9]\d{9}$
          title: Phone
          type: string
        session_id:
          description: Session ID for image validation code
          title: Session Id
          type: string
        sms_code:
          maxLength: 8
          minLength: 4
          title: Sms Code
          type: string
      required:
      - phone
      - sms_code
      - image_code
      - session_id
      title: PhoneSmsLoginDTO
      type: object
    RemoveSkillDTO:
      description: DTO for removing skill from user.
      properties:
        reason:
          anyOf:
          - type: string
          - type: 'null'
          description: Reason for removal
          title: Reason
        user_factory_skill_id:
          description: User factory skill ID
          title: User Factory Skill Id
          type: integer
      required:
      - user_factory_skill_id
      title: RemoveSkillDTO
      type: object
    RemoveUserFromFactoryDTO:
      description: DTO for removing user from factory.
      properties:
        reason:
          anyOf:
          - type: string
          - type: 'null'
          description: Reason for removal
          title: Reason
        user_id:
          description: User ID to remove
          title: User Id
          type: integer
      required:
      - user_id
      title: RemoveUserFromFactoryDTO
      type: object
    RoleCreateDTO:
      description: DTO for creating a new role.
      properties:
        description:
          anyOf:
          - maxLength: 1000
            type: string
          - type: 'null'
          description: Role description
          title: Description
        is_active:
          default: true
          description: Whether the role is active
          title: Is Active
          type: boolean
        name:
          description: Unique role name
          maxLength: 100
          minLength: 1
          title: Name
          type: string
        permission_ids:
          description: List of permission IDs to assign
          items:
            type: integer
          title: Permission Ids
          type: array
      required:
      - name
      title: RoleCreateDTO
      type: object
    RoleListDTO:
      description: DTO for role list response.
      properties:
        roles:
          description: List of roles
          items:
            $ref: '#/components/schemas/RoleResponseDTO'
          title: Roles
          type: array
        total:
          description: Total number of roles
          title: Total
          type: integer
      required:
      - roles
      - total
      title: RoleListDTO
      type: object
    RolePermissionAssignDTO:
      description: DTO for assigning permissions to role.
      properties:
        permission_ids:
          description: List of permission IDs to assign
          items:
            type: integer
          title: Permission Ids
          type: array
      required:
      - permission_ids
      title: RolePermissionAssignDTO
      type: object
    RolePermissionRemoveDTO:
      description: DTO for removing permissions from role.
      properties:
        permission_ids:
          description: List of permission IDs to remove
          items:
            type: integer
          title: Permission Ids
          type: array
      required:
      - permission_ids
      title: RolePermissionRemoveDTO
      type: object
    RoleResponseDTO:
      description: DTO for role response.
      properties:
        description:
          anyOf:
          - maxLength: 1000
            type: string
          - type: 'null'
          description: Role description
          title: Description
        id:
          description: Role ID
          title: Id
          type: integer
        is_active:
          default: true
          description: Whether the role is active
          title: Is Active
          type: boolean
        name:
          description: Unique role name
          maxLength: 100
          minLength: 1
          title: Name
          type: string
        permissions:
          description: Assigned permissions
          items:
            $ref: '#/components/schemas/PermissionResponseDTO'
          title: Permissions
          type: array
      required:
      - name
      - id
      title: RoleResponseDTO
      type: object
    RoleSummaryDTO:
      description: DTO for role summary without permissions.
      properties:
        description:
          anyOf:
          - type: string
          - type: 'null'
          description: Role description
          title: Description
        id:
          description: Role ID
          title: Id
          type: integer
        is_active:
          description: Whether the role is active
          title: Is Active
          type: boolean
        name:
          description: Role name
          title: Name
          type: string
        permission_count:
          description: Number of permissions assigned
          title: Permission Count
          type: integer
      required:
      - id
      - name
      - is_active
      - permission_count
      title: RoleSummaryDTO
      type: object
    RoleUpdateDTO:
      description: DTO for updating a role.
      properties:
        description:
          anyOf:
          - maxLength: 1000
            type: string
          - type: 'null'
          description: Role description
          title: Description
        is_active:
          anyOf:
          - type: boolean
          - type: 'null'
          description: Whether the role is active
          title: Is Active
        name:
          anyOf:
          - maxLength: 100
            minLength: 1
            type: string
          - type: 'null'
          description: Role name
          title: Name
      title: RoleUpdateDTO
      type: object
    SendSmsCodeDTO:
      description: DTO for requesting SMS validation code.
      properties:
        image_code:
          maxLength: 6
          minLength: 4
          title: Image Code
          type: string
        phone:
          description: Chinese mobile number
          pattern: ^1[3-9]\d{9}$
          title: Phone
          type: string
        session_id:
          description: Session ID for image validation code
          title: Session Id
          type: string
      required:
      - phone
      - image_code
      - session_id
      title: SendSmsCodeDTO
      type: object
    SendSmsCodeResponseDTO:
      description: Response DTO for SMS code sending.
      properties:
        expires_in_seconds:
          title: Expires In Seconds
          type: integer
        message:
          title: Message
          type: string
        success:
          title: Success
          type: boolean
      required:
      - success
      - message
      - expires_in_seconds
      title: SendSmsCodeResponseDTO
      type: object
    SessionFactoryContextDTO:
      description: DTO for factory context in session.
      properties:
        department_id:
          anyOf:
          - type: integer
          - type: 'null'
          title: Department Id
        department_name:
          anyOf:
          - type: string
          - type: 'null'
          title: Department Name
        employee_id:
          anyOf:
          - type: string
          - type: 'null'
          title: Employee Id
        factory_code:
          anyOf:
          - type: string
          - type: 'null'
          title: Factory Code
        factory_id:
          anyOf:
          - type: integer
          - type: 'null'
          title: Factory Id
        factory_name:
          anyOf:
          - type: string
          - type: 'null'
          title: Factory Name
        is_manager:
          default: false
          title: Is Manager
          type: boolean
        position:
          anyOf:
          - type: string
          - type: 'null'
          title: Position
        role:
          anyOf:
          - type: string
          - type: 'null'
          title: Role
      title: SessionFactoryContextDTO
      type: object
    SessionStatusDTO:
      description: DTO for session status response.
      properties:
        is_valid:
          title: Is Valid
          type: boolean
        message:
          anyOf:
          - type: string
          - type: 'null'
          title: Message
        user_session:
          anyOf:
          - $ref: '#/components/schemas/UserSessionDTO'
          - type: 'null'
      required:
      - is_valid
      title: SessionStatusDTO
      type: object
    SkillCreateDTO:
      description: DTO for creating a new skill.
      properties:
        category:
          anyOf:
          - maxLength: 50
            type: string
          - type: 'null'
          description: Skill category
          title: Category
        code:
          description: Unique skill code
          maxLength: 50
          minLength: 1
          title: Code
          type: string
        description:
          anyOf:
          - maxLength: 1000
            type: string
          - type: 'null'
          description: Skill description
          title: Description
        is_active:
          default: true
          description: Whether the skill is active
          title: Is Active
          type: boolean
        name:
          description: Skill name
          maxLength: 100
          minLength: 1
          title: Name
          type: string
      required:
      - code
      - name
      title: SkillCreateDTO
      type: object
    SkillListDTO:
      description: DTO for skill list response.
      properties:
        skills:
          description: List of skills
          items:
            $ref: '#/components/schemas/SkillResponseDTO'
          title: Skills
          type: array
        total:
          description: Total number of skills
          title: Total
          type: integer
      required:
      - skills
      - total
      title: SkillListDTO
      type: object
    SkillOperationResultDTO:
      description: DTO for skill operation results.
      properties:
        details:
          anyOf:
          - additionalProperties: true
            type: object
          - type: 'null'
          description: Additional operation details
          title: Details
        message:
          description: Result message
          title: Message
          type: string
        skill_id:
          anyOf:
          - type: integer
          - type: 'null'
          description: Skill ID affected
          title: Skill Id
        success:
          description: Whether operation succeeded
          title: Success
          type: boolean
        user_factory_skill_id:
          anyOf:
          - type: integer
          - type: 'null'
          description: User factory skill ID affected
          title: User Factory Skill Id
      required:
      - success
      - message
      title: SkillOperationResultDTO
      type: object
    SkillResponseDTO:
      description: DTO for skill response.
      properties:
        category:
          anyOf:
          - maxLength: 50
            type: string
          - type: 'null'
          description: Skill category
          title: Category
        code:
          description: Unique skill code
          maxLength: 50
          minLength: 1
          title: Code
          type: string
        description:
          anyOf:
          - maxLength: 1000
            type: string
          - type: 'null'
          description: Skill description
          title: Description
        id:
          description: Skill ID
          title: Id
          type: integer
        is_active:
          default: true
          description: Whether the skill is active
          title: Is Active
          type: boolean
        name:
          description: Skill name
          maxLength: 100
          minLength: 1
          title: Name
          type: string
      required:
      - code
      - name
      - id
      title: SkillResponseDTO
      type: object
    SkillUpdateDTO:
      description: DTO for updating a skill.
      properties:
        category:
          anyOf:
          - maxLength: 50
            type: string
          - type: 'null'
          description: Skill category
          title: Category
        description:
          anyOf:
          - maxLength: 1000
            type: string
          - type: 'null'
          description: Skill description
          title: Description
        is_active:
          anyOf:
          - type: boolean
          - type: 'null'
          description: Whether the skill is active
          title: Is Active
        name:
          anyOf:
          - maxLength: 100
            minLength: 1
            type: string
          - type: 'null'
          description: Skill name
          title: Name
      title: SkillUpdateDTO
      type: object
    SuspendUserDTO:
      description: DTO for suspending user.
      properties:
        reason:
          anyOf:
          - type: string
          - type: 'null'
          description: Reason for suspension
          title: Reason
        suspension_note:
          anyOf:
          - type: string
          - type: 'null'
          description: Additional notes
          title: Suspension Note
        user_id:
          description: User ID to suspend
          title: User Id
          type: integer
      required:
      - user_id
      title: SuspendUserDTO
      type: object
    SwitchFactoryDTO:
      description: DTO for switching factory context.
      properties:
        factory_id:
          title: Factory Id
          type: integer
      required:
      - factory_id
      title: SwitchFactoryDTO
      type: object
    UpdateSkillProficiencyDTO:
      description: DTO for updating skill proficiency.
      properties:
        notes:
          anyOf:
          - type: string
          - type: 'null'
          description: Updated notes
          title: Notes
        proficiency_level:
          description: New proficiency level
          title: Proficiency Level
          type: string
        user_factory_skill_id:
          description: User factory skill ID
          title: User Factory Skill Id
          type: integer
      required:
      - user_factory_skill_id
      - proficiency_level
      title: UpdateSkillProficiencyDTO
      type: object
    UserCreateDTO:
      properties:
        email:
          format: email
          title: Email
          type: string
        full_name:
          anyOf:
          - maxLength: 100
            type: string
          - type: 'null'
          title: Full Name
        password:
          minLength: 8
          title: Password
          type: string
        username:
          maxLength: 50
          minLength: 3
          title: Username
          type: string
      required:
      - username
      - email
      - password
      title: UserCreateDTO
      type: object
    UserFactoryResponseDTO:
      description: Response DTO for UserFactory relationships.
      properties:
        approved_at:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          title: Approved At
        approved_by:
          anyOf:
          - type: integer
          - type: 'null'
          title: Approved By
        approver_name:
          anyOf:
          - type: string
          - type: 'null'
          title: Approver Name
        department_id:
          anyOf:
          - type: integer
          - type: 'null'
          title: Department Id
        department_name:
          anyOf:
          - type: string
          - type: 'null'
          title: Department Name
        employee_id:
          anyOf:
          - type: string
          - type: 'null'
          title: Employee Id
        end_date:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          title: End Date
        factory_id:
          title: Factory Id
          type: integer
        factory_name:
          anyOf:
          - type: string
          - type: 'null'
          title: Factory Name
        id:
          title: Id
          type: integer
        position:
          anyOf:
          - type: string
          - type: 'null'
          title: Position
        rejected_reason:
          anyOf:
          - type: string
          - type: 'null'
          title: Rejected Reason
        requested_at:
          format: date-time
          title: Requested At
          type: string
        role:
          $ref: '#/components/schemas/UserFactoryRoleEnum'
        start_date:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          title: Start Date
        status:
          $ref: '#/components/schemas/UserFactoryStatusEnum'
        user_id:
          title: User Id
          type: integer
        user_name:
          anyOf:
          - type: string
          - type: 'null'
          title: User Name
      required:
      - id
      - user_id
      - factory_id
      - department_id
      - status
      - role
      - employee_id
      - position
      - start_date
      - end_date
      - requested_at
      - approved_by
      - approved_at
      - rejected_reason
      title: UserFactoryResponseDTO
      type: object
    UserFactoryRole:
      description: User's role in the factory.
      enum:
      - worker
      - supervisor
      - manager
      - admin
      title: UserFactoryRole
      type: string
    UserFactoryRoleEnum:
      description: Role enum for API responses.
      enum:
      - worker
      - supervisor
      - manager
      - admin
      title: UserFactoryRoleEnum
      type: string
    UserFactorySkillDTO:
      description: DTO for user factory skill relationship.
      properties:
        assigned_at:
          description: When skill was assigned (ISO format)
          title: Assigned At
          type: string
        assigned_by:
          anyOf:
          - type: integer
          - type: 'null'
          description: ID of user who assigned/updated this skill
          title: Assigned By
        certification_date:
          anyOf:
          - type: string
          - type: 'null'
          description: Certification date (ISO format)
          title: Certification Date
        certification_expires:
          anyOf:
          - type: string
          - type: 'null'
          description: Certification expiry date (ISO format)
          title: Certification Expires
        certified:
          description: Whether user is certified
          title: Certified
          type: boolean
        id:
          description: User factory skill ID
          title: Id
          type: integer
        notes:
          anyOf:
          - type: string
          - type: 'null'
          description: Additional notes
          title: Notes
        proficiency_level:
          description: Proficiency level (BEGINNER, INTERMEDIATE, ADVANCED, EXPERT)
          title: Proficiency Level
          type: string
        skill:
          $ref: '#/components/schemas/SkillResponseDTO'
          description: Skill information
        updated_at:
          description: Last update time (ISO format)
          title: Updated At
          type: string
      required:
      - id
      - skill
      - proficiency_level
      - certified
      - assigned_at
      - updated_at
      title: UserFactorySkillDTO
      type: object
    UserFactoryStatusEnum:
      description: Status enum for API responses.
      enum:
      - pending
      - approved
      - rejected
      - suspended
      - resigned
      title: UserFactoryStatusEnum
      type: string
    UserOperationResultDTO:
      description: DTO for user operation results.
      properties:
        details:
          anyOf:
          - additionalProperties: true
            type: object
          - type: 'null'
          description: Additional operation details
          title: Details
        message:
          description: Result message
          title: Message
          type: string
        success:
          description: Whether operation succeeded
          title: Success
          type: boolean
        user_id:
          description: User ID affected
          title: User Id
          type: integer
      required:
      - success
      - message
      - user_id
      title: UserOperationResultDTO
      type: object
    UserResponseDTO:
      properties:
        avatar_url:
          anyOf:
          - type: string
          - type: 'null'
          title: Avatar Url
        created_at:
          format: date-time
          title: Created At
          type: string
        email:
          title: Email
          type: string
        full_name:
          anyOf:
          - type: string
          - type: 'null'
          title: Full Name
        id:
          title: Id
          type: integer
        is_active:
          title: Is Active
          type: boolean
        is_superuser:
          title: Is Superuser
          type: boolean
        updated_at:
          format: date-time
          title: Updated At
          type: string
        username:
          title: Username
          type: string
      required:
      - id
      - username
      - email
      - full_name
      - is_active
      - is_superuser
      - avatar_url
      - created_at
      - updated_at
      title: UserResponseDTO
      type: object
    UserSessionDTO:
      description: DTO for user session information.
      properties:
        factory_context:
          $ref: '#/components/schemas/SessionFactoryContextDTO'
        session_created_at:
          format: date-time
          title: Session Created At
          type: string
        session_id:
          title: Session Id
          type: string
        user_id:
          title: User Id
          type: integer
        username:
          title: Username
          type: string
      required:
      - user_id
      - username
      - session_id
      - factory_context
      - session_created_at
      title: UserSessionDTO
      type: object
    UserSkillsDTO:
      description: DTO for user's skills in a factory.
      properties:
        certified_skills:
          description: Number of certified skills
          title: Certified Skills
          type: integer
        skills:
          description: List of user's skills
          items:
            $ref: '#/components/schemas/UserFactorySkillDTO'
          title: Skills
          type: array
        total_skills:
          description: Total number of skills
          title: Total Skills
          type: integer
        user_factory_id:
          description: User factory relationship ID
          title: User Factory Id
          type: integer
      required:
      - user_factory_id
      - skills
      - total_skills
      - certified_skills
      title: UserSkillsDTO
      type: object
    UserSummaryDTO:
      description: DTO for user summary information.
      properties:
        email:
          description: Email address
          title: Email
          type: string
        full_name:
          anyOf:
          - type: string
          - type: 'null'
          description: User's full name
          title: Full Name
        id:
          description: User ID
          title: Id
          type: integer
        is_active:
          description: Whether user is active
          title: Is Active
          type: boolean
        phone:
          anyOf:
          - type: string
          - type: 'null'
          description: Phone number
          title: Phone
        role:
          anyOf:
          - $ref: '#/components/schemas/RoleSummaryDTO'
          - type: 'null'
          description: User's system role
        username:
          description: Username
          title: Username
          type: string
      required:
      - id
      - username
      - email
      - is_active
      title: UserSummaryDTO
      type: object
    UserUpdateDTO:
      properties:
        avatar_url:
          anyOf:
          - type: string
          - type: 'null'
          title: Avatar Url
        full_name:
          anyOf:
          - maxLength: 100
            type: string
          - type: 'null'
          title: Full Name
        is_active:
          anyOf:
          - type: boolean
          - type: 'null'
          title: Is Active
      title: UserUpdateDTO
      type: object
    ValidationError:
      properties:
        loc:
          items:
            anyOf:
            - type: string
            - type: integer
          title: Location
          type: array
        msg:
          title: Message
          type: string
        type:
          title: Error Type
          type: string
      required:
      - loc
      - msg
      - type
      title: ValidationError
      type: object
    src__application__dto__auth_dto__TokenResponseDTO:
      description: Response DTO for authentication token.
      properties:
        access_token:
          title: Access Token
          type: string
        factory_context:
          additionalProperties: true
          title: Factory Context
          type: object
        session_id:
          title: Session Id
          type: string
        token_type:
          default: bearer
          title: Token Type
          type: string
        user_info:
          additionalProperties: true
          title: User Info
          type: object
      required:
      - access_token
      - session_id
      - user_info
      - factory_context
      title: TokenResponseDTO
      type: object
    src__application__dto__user_dto__TokenResponseDTO:
      properties:
        access_token:
          title: Access Token
          type: string
        token_type:
          default: bearer
          title: Token Type
          type: string
      required:
      - access_token
      title: TokenResponseDTO
      type: object
  securitySchemes:
    OAuth2PasswordBearer:
      flows:
        password:
          scopes: {}
          tokenUrl: /api/v1/auth/token
      type: oauth2
info:
  title: Production Ticket System
  version: 1.0.0
openapi: 3.1.0
paths:
  /:
    get:
      description: Root endpoint.
      operationId: root__get
      responses:
        '200':
          content:
            application/json:
              schema: {}
          description: Successful Response
      summary: Root
  /api/v1/auth/cleanup-expired-codes:
    post:
      description: Clean up expired validation codes (admin endpoint).
      operationId: cleanup_expired_codes_api_v1_auth_cleanup_expired_codes_post
      responses:
        '200':
          content:
            application/json:
              schema: {}
          description: Successful Response
      summary: Cleanup Expired Codes
      tags:
      - phone-authentication
  /api/v1/auth/generate-image-code:
    post:
      description: Generate image validation code.
      operationId: generate_image_code_api_v1_auth_generate_image_code_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GenerateImageCodeDTO'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ImageCodeResponseDTO'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Generate Image Code
      tags:
      - phone-authentication
  /api/v1/auth/login-phone-password:
    post:
      description: Login with phone + password + image validation code.
      operationId: login_with_phone_password_api_v1_auth_login_phone_password_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PhonePasswordLoginDTO'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/src__application__dto__auth_dto__TokenResponseDTO'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Login With Phone Password
      tags:
      - phone-authentication
  /api/v1/auth/login-phone-sms:
    post:
      description: Login with phone + SMS code + image validation code.
      operationId: login_with_phone_sms_api_v1_auth_login_phone_sms_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PhoneSmsLoginDTO'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/src__application__dto__auth_dto__TokenResponseDTO'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Login With Phone Sms
      tags:
      - phone-authentication
  /api/v1/auth/me:
    get:
      description: Get current user information.
      operationId: read_users_me_api_v1_auth_me_get
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserResponseDTO'
          description: Successful Response
      security:
      - OAuth2PasswordBearer: []
      summary: Read Users Me
      tags:
      - authentication
  /api/v1/auth/send-sms-code:
    post:
      description: Send SMS validation code.
      operationId: send_sms_code_api_v1_auth_send_sms_code_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SendSmsCodeDTO'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SendSmsCodeResponseDTO'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Send Sms Code
      tags:
      - phone-authentication
  /api/v1/auth/token:
    post:
      description: Login and get access token.
      operationId: login_api_v1_auth_token_post
      requestBody:
        content:
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Body_login_api_v1_auth_token_post'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/src__application__dto__user_dto__TokenResponseDTO'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Login
      tags:
      - authentication
  /api/v1/factory-management/approve-request:
    post:
      description: Approve or reject a factory join request (managers only).
      operationId: approve_or_reject_request_api_v1_factory_management_approve_request_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FactoryJoinApprovalDTO'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserFactoryResponseDTO'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      security:
      - OAuth2PasswordBearer: []
      summary: Approve Or Reject Request
      tags:
      - factory-management
  /api/v1/factory-management/join-request:
    post:
      description: Request to join a factory.
      operationId: request_to_join_factory_api_v1_factory_management_join_request_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FactoryJoinRequestDTO'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserFactoryResponseDTO'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      security:
      - OAuth2PasswordBearer: []
      summary: Request To Join Factory
      tags:
      - factory-management
  /api/v1/factory-management/members:
    get:
      description: Get all members of current factory.
      operationId: get_factory_members_api_v1_factory_management_members_get
      parameters:
      - in: header
        name: x-session-id
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: X-Session-Id
      responses:
        '200':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/UserFactoryResponseDTO'
                title: Response Get Factory Members Api V1 Factory Management Members
                  Get
                type: array
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      security:
      - OAuth2PasswordBearer: []
      summary: Get Factory Members
      tags:
      - factory-management
  /api/v1/factory-management/my-factories:
    get:
      description: Get current user's factory relationships.
      operationId: get_my_factories_api_v1_factory_management_my_factories_get
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MyFactoriesDTO'
          description: Successful Response
      security:
      - OAuth2PasswordBearer: []
      summary: Get My Factories
      tags:
      - factory-management
  /api/v1/factory-management/pending-requests:
    get:
      description: Get pending join requests for current factory (managers only).
      operationId: get_pending_requests_api_v1_factory_management_pending_requests_get
      parameters:
      - in: header
        name: x-session-id
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: X-Session-Id
      responses:
        '200':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/UserFactoryResponseDTO'
                title: Response Get Pending Requests Api V1 Factory Management Pending
                  Requests Get
                type: array
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      security:
      - OAuth2PasswordBearer: []
      summary: Get Pending Requests
      tags:
      - factory-management
  /api/v1/factory-management/resign:
    post:
      description: Resign from current factory.
      operationId: resign_from_factory_api_v1_factory_management_resign_post
      parameters:
      - in: header
        name: x-session-id
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: X-Session-Id
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserFactoryResponseDTO'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      security:
      - OAuth2PasswordBearer: []
      summary: Resign From Factory
      tags:
      - factory-management
  /api/v1/factory-management/user/{user_id}/role:
    put:
      description: Update user's role in current factory (managers only).
      operationId: update_user_role_api_v1_factory_management_user__user_id__role_put
      parameters:
      - in: path
        name: user_id
        required: true
        schema:
          title: User Id
          type: integer
      - in: query
        name: new_role
        required: true
        schema:
          $ref: '#/components/schemas/UserFactoryRole'
      - in: header
        name: x-session-id
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: X-Session-Id
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserFactoryResponseDTO'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      security:
      - OAuth2PasswordBearer: []
      summary: Update User Role
      tags:
      - factory-management
  /api/v1/factory-management/user/{user_id}/suspend:
    post:
      description: Suspend user from current factory (managers only).
      operationId: suspend_user_api_v1_factory_management_user__user_id__suspend_post
      parameters:
      - in: path
        name: user_id
        required: true
        schema:
          title: User Id
          type: integer
      - in: query
        name: reason
        required: false
        schema:
          title: Reason
          type: string
      - in: header
        name: x-session-id
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: X-Session-Id
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserFactoryResponseDTO'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      security:
      - OAuth2PasswordBearer: []
      summary: Suspend User
      tags:
      - factory-management
  /api/v1/permissions/list:
    get:
      description: Get all permissions as a flat list.
      operationId: get_all_permissions_api_v1_permissions_list_get
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PermissionListDTO'
          description: Successful Response
      security:
      - OAuth2PasswordBearer: []
      summary: Get All Permissions
      tags:
      - permissions
  /api/v1/permissions/tree:
    get:
      description: Get hierarchical permission tree.
      operationId: get_permission_tree_api_v1_permissions_tree_get
      responses:
        '200':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/PermissionTreeDTO'
                title: Response Get Permission Tree Api V1 Permissions Tree Get
                type: array
          description: Successful Response
      security:
      - OAuth2PasswordBearer: []
      summary: Get Permission Tree
      tags:
      - permissions
  /api/v1/roles/:
    get:
      description: Get all roles.
      operationId: get_all_roles_api_v1_roles__get
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RoleListDTO'
          description: Successful Response
      security:
      - OAuth2PasswordBearer: []
      summary: Get All Roles
      tags:
      - roles
    post:
      description: Create a new role.
      operationId: create_role_api_v1_roles__post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RoleCreateDTO'
        required: true
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RoleResponseDTO'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      security:
      - OAuth2PasswordBearer: []
      summary: Create Role
      tags:
      - roles
  /api/v1/roles/active:
    get:
      description: Get all active roles.
      operationId: get_active_roles_api_v1_roles_active_get
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RoleListDTO'
          description: Successful Response
      security:
      - OAuth2PasswordBearer: []
      summary: Get Active Roles
      tags:
      - roles
  /api/v1/roles/summary:
    get:
      description: Get summary of all roles without full permission details.
      operationId: get_roles_summary_api_v1_roles_summary_get
      responses:
        '200':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/RoleSummaryDTO'
                title: Response Get Roles Summary Api V1 Roles Summary Get
                type: array
          description: Successful Response
      security:
      - OAuth2PasswordBearer: []
      summary: Get Roles Summary
      tags:
      - roles
  /api/v1/roles/{role_id}:
    delete:
      description: Delete role.
      operationId: delete_role_api_v1_roles__role_id__delete
      parameters:
      - in: path
        name: role_id
        required: true
        schema:
          title: Role Id
          type: integer
      responses:
        '204':
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      security:
      - OAuth2PasswordBearer: []
      summary: Delete Role
      tags:
      - roles
    get:
      description: Get role by ID.
      operationId: get_role_by_id_api_v1_roles__role_id__get
      parameters:
      - in: path
        name: role_id
        required: true
        schema:
          title: Role Id
          type: integer
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RoleResponseDTO'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      security:
      - OAuth2PasswordBearer: []
      summary: Get Role By Id
      tags:
      - roles
    put:
      description: Update role.
      operationId: update_role_api_v1_roles__role_id__put
      parameters:
      - in: path
        name: role_id
        required: true
        schema:
          title: Role Id
          type: integer
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RoleUpdateDTO'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RoleResponseDTO'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      security:
      - OAuth2PasswordBearer: []
      summary: Update Role
      tags:
      - roles
  /api/v1/roles/{role_id}/permissions:
    delete:
      description: Remove permissions from role.
      operationId: remove_permissions_from_role_api_v1_roles__role_id__permissions_delete
      parameters:
      - in: path
        name: role_id
        required: true
        schema:
          title: Role Id
          type: integer
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RolePermissionRemoveDTO'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RoleResponseDTO'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      security:
      - OAuth2PasswordBearer: []
      summary: Remove Permissions From Role
      tags:
      - roles
    get:
      description: Get permissions assigned to a role.
      operationId: get_role_permissions_api_v1_roles__role_id__permissions_get
      parameters:
      - in: path
        name: role_id
        required: true
        schema:
          title: Role Id
          type: integer
      responses:
        '200':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/PermissionResponseDTO'
                title: Response Get Role Permissions Api V1 Roles  Role Id  Permissions
                  Get
                type: array
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      security:
      - OAuth2PasswordBearer: []
      summary: Get Role Permissions
      tags:
      - roles
    post:
      description: Assign permissions to role.
      operationId: assign_permissions_to_role_api_v1_roles__role_id__permissions_post
      parameters:
      - in: path
        name: role_id
        required: true
        schema:
          title: Role Id
          type: integer
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RolePermissionAssignDTO'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RoleResponseDTO'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      security:
      - OAuth2PasswordBearer: []
      summary: Assign Permissions To Role
      tags:
      - roles
  /api/v1/session/available-factories:
    get:
      description: Get list of factories user can switch to.
      operationId: get_available_factories_api_v1_session_available_factories_get
      parameters:
      - in: header
        name: x-session-id
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: X-Session-Id
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AvailableFactoriesDTO'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Get Available Factories
      tags:
      - session
  /api/v1/session/extend:
    post:
      description: Extend session expiration.
      operationId: extend_session_api_v1_session_extend_post
      parameters:
      - in: header
        name: x-session-id
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: X-Session-Id
      responses:
        '200':
          content:
            application/json:
              schema: {}
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Extend Session
      tags:
      - session
  /api/v1/session/logout:
    delete:
      description: Logout and destroy session.
      operationId: logout_api_v1_session_logout_delete
      parameters:
      - in: header
        name: x-session-id
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: X-Session-Id
      responses:
        '200':
          content:
            application/json:
              schema: {}
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Logout
      tags:
      - session
  /api/v1/session/refresh-context:
    post:
      description: Refresh factory context with latest data.
      operationId: refresh_factory_context_api_v1_session_refresh_context_post
      parameters:
      - in: header
        name: x-session-id
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: X-Session-Id
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserSessionDTO'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Refresh Factory Context
      tags:
      - session
  /api/v1/session/status:
    get:
      description: Get current session status and factory context.
      operationId: get_session_status_api_v1_session_status_get
      parameters:
      - in: header
        name: x-session-id
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: X-Session-Id
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SessionStatusDTO'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Get Session Status
      tags:
      - session
  /api/v1/session/switch-factory:
    post:
      description: Switch user's factory context.
      operationId: switch_factory_context_api_v1_session_switch_factory_post
      parameters:
      - in: header
        name: x-session-id
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: X-Session-Id
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SwitchFactoryDTO'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserSessionDTO'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Switch Factory Context
      tags:
      - session
  /api/v1/skills/:
    get:
      description: Get all skills with optional filters.
      operationId: get_all_skills_api_v1_skills__get
      parameters:
      - description: Search by code, name, or description
        in: query
        name: search_term
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          description: Search by code, name, or description
          title: Search Term
      - description: Filter by category
        in: query
        name: category
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          description: Filter by category
          title: Category
      - description: Filter by active status
        in: query
        name: is_active
        required: false
        schema:
          anyOf:
          - type: boolean
          - type: 'null'
          description: Filter by active status
          title: Is Active
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SkillListDTO'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      security:
      - OAuth2PasswordBearer: []
      summary: Get All Skills
      tags:
      - skills
    post:
      description: Create a new skill.
      operationId: create_skill_api_v1_skills__post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SkillCreateDTO'
        required: true
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SkillResponseDTO'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      security:
      - OAuth2PasswordBearer: []
      summary: Create Skill
      tags:
      - skills
  /api/v1/skills/active:
    get:
      description: Get all active skills.
      operationId: get_active_skills_api_v1_skills_active_get
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SkillListDTO'
          description: Successful Response
      security:
      - OAuth2PasswordBearer: []
      summary: Get Active Skills
      tags:
      - skills
  /api/v1/skills/assign:
    post:
      description: Assign skills to user in current factory.
      operationId: assign_skills_to_user_api_v1_skills_assign_post
      parameters:
      - in: header
        name: x-session-id
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: X-Session-Id
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AssignSkillsDTO'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/SkillOperationResultDTO'
                title: Response Assign Skills To User Api V1 Skills Assign Post
                type: array
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      security:
      - OAuth2PasswordBearer: []
      summary: Assign Skills To User
      tags:
      - skills
  /api/v1/skills/certify:
    post:
      description: Certify user in skill.
      operationId: certify_user_skill_api_v1_skills_certify_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CertifySkillDTO'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SkillOperationResultDTO'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      security:
      - OAuth2PasswordBearer: []
      summary: Certify User Skill
      tags:
      - skills
  /api/v1/skills/modify-proficiency:
    put:
      description: Modify user's skill proficiency level.
      operationId: modify_skill_proficiency_api_v1_skills_modify_proficiency_put
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateSkillProficiencyDTO'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SkillOperationResultDTO'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      security:
      - OAuth2PasswordBearer: []
      summary: Modify Skill Proficiency
      tags:
      - skills
  /api/v1/skills/remove:
    delete:
      description: Remove skill from user.
      operationId: remove_user_skill_api_v1_skills_remove_delete
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RemoveSkillDTO'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SkillOperationResultDTO'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      security:
      - OAuth2PasswordBearer: []
      summary: Remove User Skill
      tags:
      - skills
  /api/v1/skills/user/{user_id}/skills:
    get:
      description: Get user's skills in current factory.
      operationId: get_user_skills_api_v1_skills_user__user_id__skills_get
      parameters:
      - in: path
        name: user_id
        required: true
        schema:
          title: User Id
          type: integer
      - in: header
        name: x-session-id
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: X-Session-Id
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserSkillsDTO'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      security:
      - OAuth2PasswordBearer: []
      summary: Get User Skills
      tags:
      - skills
  /api/v1/skills/{skill_id}:
    delete:
      description: Delete skill.
      operationId: delete_skill_api_v1_skills__skill_id__delete
      parameters:
      - in: path
        name: skill_id
        required: true
        schema:
          title: Skill Id
          type: integer
      responses:
        '204':
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      security:
      - OAuth2PasswordBearer: []
      summary: Delete Skill
      tags:
      - skills
    get:
      description: Get skill by ID.
      operationId: get_skill_by_id_api_v1_skills__skill_id__get
      parameters:
      - in: path
        name: skill_id
        required: true
        schema:
          title: Skill Id
          type: integer
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SkillResponseDTO'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      security:
      - OAuth2PasswordBearer: []
      summary: Get Skill By Id
      tags:
      - skills
    put:
      description: Update skill.
      operationId: update_skill_api_v1_skills__skill_id__put
      parameters:
      - in: path
        name: skill_id
        required: true
        schema:
          title: Skill Id
          type: integer
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SkillUpdateDTO'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SkillResponseDTO'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      security:
      - OAuth2PasswordBearer: []
      summary: Update Skill
      tags:
      - skills
  /api/v1/user-management/add-users:
    post:
      description: Add multiple users to the current factory.
      operationId: add_users_to_factory_api_v1_user_management_add_users_post
      parameters:
      - in: header
        name: x-session-id
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: X-Session-Id
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddUsersToFactoryDTO'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/UserOperationResultDTO'
                title: Response Add Users To Factory Api V1 User Management Add Users
                  Post
                type: array
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      security:
      - OAuth2PasswordBearer: []
      summary: Add Users To Factory
      tags:
      - user-management
  /api/v1/user-management/available-users:
    get:
      description: Get list of users available to add to the factory.
      operationId: get_available_users_api_v1_user_management_available_users_get
      parameters:
      - description: Search by username, email, or full name
        in: query
        name: search_term
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          description: Search by username, email, or full name
          title: Search Term
      - description: Filter by system role
        in: query
        name: role_id
        required: false
        schema:
          anyOf:
          - type: integer
          - type: 'null'
          description: Filter by system role
          title: Role Id
      - description: Filter by active status
        in: query
        name: is_active
        required: false
        schema:
          anyOf:
          - type: boolean
          - type: 'null'
          description: Filter by active status
          title: Is Active
      - in: header
        name: x-session-id
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: X-Session-Id
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AvailableUsersDTO'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      security:
      - OAuth2PasswordBearer: []
      summary: Get Available Users
      tags:
      - user-management
  /api/v1/user-management/bind-roles:
    post:
      description: Bind system role to a user.
      operationId: bind_user_role_api_v1_user_management_bind_roles_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BindUserRoleDTO'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserOperationResultDTO'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      security:
      - OAuth2PasswordBearer: []
      summary: Bind User Role
      tags:
      - user-management
  /api/v1/user-management/remove-user:
    delete:
      description: Remove a user from the current factory.
      operationId: remove_user_from_factory_api_v1_user_management_remove_user_delete
      parameters:
      - in: header
        name: x-session-id
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: X-Session-Id
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RemoveUserFromFactoryDTO'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserOperationResultDTO'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      security:
      - OAuth2PasswordBearer: []
      summary: Remove User From Factory
      tags:
      - user-management
  /api/v1/user-management/suspend:
    post:
      description: Suspend a user in the current factory.
      operationId: suspend_user_in_factory_api_v1_user_management_suspend_post
      parameters:
      - in: header
        name: x-session-id
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: X-Session-Id
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SuspendUserDTO'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserOperationResultDTO'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      security:
      - OAuth2PasswordBearer: []
      summary: Suspend User In Factory
      tags:
      - user-management
  /api/v1/user-management/user-list:
    get:
      description: Get list of all users in the current factory.
      operationId: get_factory_user_list_api_v1_user_management_user_list_get
      parameters:
      - in: header
        name: x-session-id
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: X-Session-Id
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FactoryUserListDTO'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      security:
      - OAuth2PasswordBearer: []
      summary: Get Factory User List
      tags:
      - user-management
  /api/v1/users/:
    get:
      description: Get all users.
      operationId: get_users_api_v1_users__get
      parameters:
      - in: query
        name: skip
        required: false
        schema:
          default: 0
          title: Skip
          type: integer
      - in: query
        name: limit
        required: false
        schema:
          default: 100
          title: Limit
          type: integer
      responses:
        '200':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/UserResponseDTO'
                title: Response Get Users Api V1 Users  Get
                type: array
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      security:
      - OAuth2PasswordBearer: []
      summary: Get Users
      tags:
      - users
    post:
      description: Create a new user.
      operationId: create_user_api_v1_users__post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserCreateDTO'
        required: true
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserResponseDTO'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Create User
      tags:
      - users
  /api/v1/users/{user_id}:
    delete:
      description: Delete user.
      operationId: delete_user_api_v1_users__user_id__delete
      parameters:
      - in: path
        name: user_id
        required: true
        schema:
          title: User Id
          type: integer
      responses:
        '204':
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      security:
      - OAuth2PasswordBearer: []
      summary: Delete User
      tags:
      - users
    get:
      description: Get user by ID.
      operationId: get_user_api_v1_users__user_id__get
      parameters:
      - in: path
        name: user_id
        required: true
        schema:
          title: User Id
          type: integer
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserResponseDTO'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      security:
      - OAuth2PasswordBearer: []
      summary: Get User
      tags:
      - users
    put:
      description: Update user.
      operationId: update_user_api_v1_users__user_id__put
      parameters:
      - in: path
        name: user_id
        required: true
        schema:
          title: User Id
          type: integer
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserUpdateDTO'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserResponseDTO'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      security:
      - OAuth2PasswordBearer: []
      summary: Update User
      tags:
      - users
  /health:
    get:
      description: Health check endpoint.
      operationId: health_check_health_get
      responses:
        '200':
          content:
            application/json:
              schema: {}
          description: Successful Response
      summary: Health Check
