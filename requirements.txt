# FastAPI and Web Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0

# Database and ORM
sqlalchemy==2.0.23
alembic==1.12.1
aiomysql==0.2.0
asyncpg==0.29.0

# Data Validation and Settings
pydantic==2.5.0
pydantic-settings==2.1.0

# Dependency Injection
dependency-injector==4.41.0

# Authentication and Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# Async Support and Caching
aioredis==2.0.1
redis==5.0.1

# Task Queue
celery==5.3.4

# Configuration Management
python-dotenv==1.0.0

# Logging
structlog==23.2.0

# QR Code and Barcode
qrcode[pil]==7.4.2
python-barcode==0.15.1

# Image Processing for Captcha
Pillow==10.1.0

# HTTP Client
httpx==0.25.2

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0

# Additional Dependencies for Python 3.13 compatibility
email-validator==2.2.0
greenlet==3.2.3

# Development
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1
pre-commit==3.6.0